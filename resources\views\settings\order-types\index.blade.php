<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Order Types Management') }}
            </h2>
            <a href="{{ route('settings.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Back to Settings
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Order Types Management</h3>
                        <a href="{{ route('settings.order-types.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-plus"></i> Add New Order Type
                        </a>
                    </div>
                </div>
                <div class="p-6">
                    @if(session('success'))
                        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('error') }}</span>
                        </div>
                    @endif

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200" id="orderTypesTable">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">Order</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Requires Table</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">Requires Party Size</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="sortable-order-types">
                                @forelse($orderTypes as $orderType)
                                    <tr data-id="{{ $orderType->id }}" class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <i class="fas fa-grip-vertical text-gray-400 drag-handle cursor-move"></i>
                                            {{ $orderType->sort_order }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                @if($orderType->icon)
                                                    <i class="{{ $orderType->icon }} mr-2" 
                                                       @if($orderType->color) style="color: {{ $orderType->color }}" @endif></i>
                                                @endif
                                                <span class="font-medium text-gray-900">{{ $orderType->name }}</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $orderType->description ?? '-' }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if($orderType->requires_table)
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Yes</span>
                                            @else
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">No</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if($orderType->requires_party_size)
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Yes</span>
                                            @else
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">No</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <form action="{{ route('settings.order-types.toggle-status', $orderType) }}" method="POST" class="inline">
                                                @csrf
                                                <button type="submit" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $orderType->is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }} hover:bg-opacity-75">
                                                    {{ $orderType->is_active ? 'Active' : 'Inactive' }}
                                                </button>
                                            </form>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('settings.order-types.show', $orderType) }}" 
                                                   class="text-blue-600 hover:text-blue-900" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('settings.order-types.edit', $orderType) }}" 
                                                   class="text-yellow-600 hover:text-yellow-900" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('settings.order-types.destroy', $orderType) }}" 
                                                      method="POST" class="inline" 
                                                      onsubmit="return confirm('Are you sure you want to delete this order type?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="text-red-600 hover:text-red-900" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="px-6 py-12 text-center">
                                            <div class="text-gray-500">
                                                <i class="fas fa-inbox text-4xl mb-3 block"></i>
                                                <p>No order types found. <a href="{{ route('settings.order-types.create') }}" class="text-blue-600 hover:text-blue-800">Create your first order type</a>.</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize sortable for order types
    const sortableElement = document.getElementById('sortable-order-types');
    if (sortableElement && sortableElement.children.length > 0) {
        const sortable = Sortable.create(sortableElement, {
            handle: '.drag-handle',
            animation: 150,
            onEnd: function(evt) {
                const orderTypes = Array.from(sortableElement.children).map(row => row.dataset.id);
                
                fetch('{{ route("settings.order-types.update-order") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        order_types: orderTypes
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update the order numbers in the UI
                        Array.from(sortableElement.children).forEach((row, index) => {
                            row.querySelector('td:first-child').innerHTML = 
                                '<i class="fas fa-grip-vertical text-gray-400 drag-handle cursor-move"></i> ' + (index + 1);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error updating order:', error);
                    // Revert the change
                    location.reload();
                });
            }
        });
    }
});
</script>
@endpush
        </div>
    </div>
</x-app-layout>