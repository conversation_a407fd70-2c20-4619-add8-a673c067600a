<?php

namespace App\Http\Controllers;

use App\Models\TaxSetting;
use Illuminate\Http\Request;

class TaxSettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $taxSettings = TaxSetting::orderBy('is_active', 'desc')->orderBy('created_at', 'desc')->get();
        return view('settings.tax.index', compact('taxSettings'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('settings.tax.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'rate' => 'required|numeric|min:0|max:1',
            'application_type' => 'required|in:customer_pays,business_absorbs',
            'description' => 'nullable|string'
        ]);

        // If this is being set as active, deactivate others
        if ($request->has('is_active')) {
            TaxSetting::where('is_active', true)->update(['is_active' => false]);
        }

        TaxSetting::create([
            'name' => $request->name,
            'rate' => $request->rate,
            'is_active' => $request->has('is_active'),
            'application_type' => $request->application_type,
            'description' => $request->description
        ]);

        return redirect()->route('settings.tax.index')->with('success', 'Tax setting created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(TaxSetting $taxSetting)
    {
        return view('settings.tax.show', compact('taxSetting'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TaxSetting $taxSetting)
    {
        return view('settings.tax.edit', compact('taxSetting'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TaxSetting $taxSetting)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'rate' => 'required|numeric|min:0|max:1',
            'application_type' => 'required|in:customer_pays,business_absorbs',
            'description' => 'nullable|string'
        ]);

        // If this is being set as active, deactivate others
        if ($request->has('is_active') && !$taxSetting->is_active) {
            TaxSetting::where('is_active', true)->update(['is_active' => false]);
        }

        $taxSetting->update([
            'name' => $request->name,
            'rate' => $request->rate,
            'is_active' => $request->has('is_active'),
            'application_type' => $request->application_type,
            'description' => $request->description
        ]);

        return redirect()->route('settings.tax.index')->with('success', 'Tax setting updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TaxSetting $taxSetting)
    {
        if ($taxSetting->is_active) {
            return redirect()->route('settings.tax.index')->with('error', 'Cannot delete active tax setting!');
        }

        $taxSetting->delete();
        return redirect()->route('settings.tax.index')->with('success', 'Tax setting deleted successfully!');
    }

    /**
     * Activate a tax setting
     */
    public function activate(TaxSetting $taxSetting)
    {
        // Deactivate all other tax settings
        TaxSetting::where('is_active', true)->update(['is_active' => false]);

        // Activate this one
        $taxSetting->update(['is_active' => true]);

        return redirect()->route('settings.tax.index')->with('success', 'Tax setting activated successfully!');
    }
}
