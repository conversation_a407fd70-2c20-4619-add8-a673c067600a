<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Order;

class BackfillTransactionNumbers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:backfill-transaction-numbers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Backfill transaction numbers for existing orders';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to backfill transaction numbers for existing orders...');

        $ordersWithoutTransactionNumber = Order::whereNull('transaction_number')->get();

        if ($ordersWithoutTransactionNumber->isEmpty()) {
            $this->info('No orders found without transaction numbers.');
            return 0;
        }

        $this->info("Found {$ordersWithoutTransactionNumber->count()} orders without transaction numbers.");

        $progressBar = $this->output->createProgressBar($ordersWithoutTransactionNumber->count());
        $progressBar->start();

        foreach ($ordersWithoutTransactionNumber as $order) {
            $transactionNumber = $this->generateTransactionNumberForOrder($order);
            $order->update(['transaction_number' => $transactionNumber]);
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        $this->info("Successfully backfilled transaction numbers for {$ordersWithoutTransactionNumber->count()} orders.");

        return 0;
    }

    /**
     * Generate transaction number for a specific order based on its creation date
     */
    private function generateTransactionNumberForOrder(Order $order): string
    {
        $date = $order->created_at->format('Ymd');
        $time = $order->created_at->format('His');

        // Get the next sequential number for that specific day
        $existingOrdersOnSameDay = Order::whereDate('created_at', $order->created_at->toDateString())
            ->whereNotNull('transaction_number')
            ->where('id', '<', $order->id) // Only count orders created before this one
            ->count();

        $sequence = $existingOrdersOnSameDay + 1;
        $sequenceStr = str_pad($sequence, 4, '0', STR_PAD_LEFT);

        return "TXN-{$date}-{$time}-{$sequenceStr}";
    }
}
