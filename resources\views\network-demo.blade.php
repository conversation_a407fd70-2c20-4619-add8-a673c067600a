<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Network Connectivity Demo') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold mb-4">Network Connectivity Monitor</h3>
                        <p class="text-gray-600 mb-4">
                            The network indicator in the top navigation bar shows the current connection status. 
                            It's especially important for POS operations that depend on API connectivity.
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Connection Status Card -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">Connection Status</h4>
                            <div id="status-display" class="text-sm text-gray-600">
                                Loading...
                            </div>
                        </div>

                        <!-- API Health Card -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">API Health</h4>
                            <div id="api-health" class="text-sm text-gray-600">
                                Checking...
                            </div>
                        </div>

                        <!-- Last Check Card -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">Last Check</h4>
                            <div id="last-check" class="text-sm text-gray-600">
                                Never
                            </div>
                        </div>
                    </div>

                    <div class="mb-8">
                        <h4 class="font-semibold mb-4">Status Indicators</h4>
                        <div class="space-y-3">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                <span class="text-sm"><strong>Connected (Green):</strong> Stable internet connection, all systems operational</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                <span class="text-sm"><strong>Unstable (Yellow):</strong> Internet available but API issues detected</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                <span class="text-sm"><strong>Disconnected (Red):</strong> No internet connection</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-gray-400 rounded-full animate-pulse"></div>
                                <span class="text-sm"><strong>Checking (Gray):</strong> Testing connection status</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-8">
                        <h4 class="font-semibold mb-4">Test Controls</h4>
                        <div class="space-x-4">
                            <button onclick="testConnection()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Test Connection
                            </button>
                            <button onclick="simulateOffline()" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                Simulate Offline
                            </button>
                            <button onclick="simulateOnline()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                Simulate Online
                            </button>
                        </div>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-semibold text-blue-800 mb-2">POS Integration</h4>
                        <p class="text-blue-700 text-sm">
                            The network monitor automatically checks connectivity every 30 seconds and provides real-time feedback. 
                            When connection issues are detected, warnings appear to alert staff about potential POS operation impacts.
                            Click the network indicator in the navigation bar to manually refresh the connection status.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Update status display
        function updateStatusDisplay() {
            if (window.networkMonitor) {
                const status = window.networkMonitor.getStatus();
                document.getElementById('status-display').innerHTML = `
                    <div class="space-y-1">
                        <div>Status: <span class="font-semibold">${status.status}</span></div>
                        <div>Latency: <span class="font-semibold">${status.latency ? status.latency + 'ms' : 'N/A'}</span></div>
                    </div>
                `;
                
                document.getElementById('last-check').textContent = status.lastCheck 
                    ? new Date(status.lastCheck).toLocaleTimeString()
                    : 'Never';
            }
        }

        // Test connection manually
        function testConnection() {
            if (window.networkMonitor) {
                window.networkMonitor.checkConnection(true);
                setTimeout(updateStatusDisplay, 1000);
            }
        }

        // Simulate offline (for testing)
        function simulateOffline() {
            if (window.networkMonitor) {
                window.networkMonitor.updateIndicator('disconnected');
            }
        }

        // Simulate online (for testing)
        function simulateOnline() {
            if (window.networkMonitor) {
                window.networkMonitor.updateIndicator('connected');
            }
        }

        // Check API health
        async function checkApiHealth() {
            try {
                const response = await fetch('/api/health-check');
                const data = await response.json();
                
                document.getElementById('api-health').innerHTML = `
                    <div class="space-y-1">
                        <div>Status: <span class="font-semibold">${data.status}</span></div>
                        <div>Database: <span class="font-semibold">${data.services.database}</span></div>
                        <div>API: <span class="font-semibold">${data.services.api}</span></div>
                    </div>
                `;
            } catch (error) {
                document.getElementById('api-health').innerHTML = `
                    <div class="text-red-600">Error: ${error.message}</div>
                `;
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Listen for network status changes
            if (window.networkMonitor) {
                window.networkMonitor.onStatusChange((status, details) => {
                    updateStatusDisplay();
                });
            }
            
            // Initial updates
            setTimeout(() => {
                updateStatusDisplay();
                checkApiHealth();
            }, 1000);
            
            // Update every 5 seconds
            setInterval(() => {
                updateStatusDisplay();
                checkApiHealth();
            }, 5000);
        });
    </script>
</x-app-layout>
