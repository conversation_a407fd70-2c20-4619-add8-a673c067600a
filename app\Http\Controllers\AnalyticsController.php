<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\Customer;
use App\Models\Payment;
use App\Models\OrderItem;
use App\Models\MenuItem;
use App\Models\MenuCategory;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'pos.access']);
    }

    /**
     * Display analytics dashboard overview
     */
    public function index(Request $request)
    {
        // Get date range from request or default to last 30 days
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));

        // Convert to Carbon instances for queries
        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();

        // Get summary data for overview
        $summaryData = $this->getSummaryData($start, $end);

        return view('analytics.index', compact(
            'summaryData',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Display revenue analytics
     */
    public function revenue(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        $categoryId = $request->get('category_id');

        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();

        $revenueData = $this->getRevenueAnalytics($start, $end, $categoryId);
        $categoryData = $this->getCategoryAnalytics($start, $end);

        return view('analytics.revenue', compact(
            'revenueData',
            'categoryData',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Display customer analytics
     */
    public function customers(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        $categoryId = $request->get('category_id');

        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();

        $customerData = $this->getCustomerAnalytics($start, $end, $categoryId);
        $categoryData = $this->getCategoryAnalytics($start, $end);

        return view('analytics.customers', compact(
            'customerData',
            'categoryData',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Display transaction analytics
     */
    public function transactions(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        $categoryId = $request->get('category_id');

        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();

        $transactionData = $this->getTransactionAnalytics($start, $end, $categoryId);
        $categoryData = $this->getCategoryAnalytics($start, $end);

        return view('analytics.transactions', compact(
            'transactionData',
            'categoryData',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Display product analytics
     */
    public function products(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        $categoryId = $request->get('category_id');

        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();

        $productData = $this->getProductAnalytics($start, $end, $categoryId);
        $categoryData = $this->getCategoryAnalytics($start, $end);

        return view('analytics.products', compact(
            'productData',
            'categoryData',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Display category analytics
     */
    public function categories(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));

        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();

        $categoryData = $this->getCategoryAnalytics($start, $end);

        return view('analytics.categories', compact(
            'categoryData',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Display sync analytics
     */
    public function sync(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        $categoryId = $request->get('category_id');

        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();

        $syncData = $this->getSyncAnalytics($start, $end, $categoryId);
        $categoryData = $this->getCategoryAnalytics($start, $end);

        return view('analytics.sync', compact(
            'syncData',
            'categoryData',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Get summary data for overview dashboard
     */
    private function getSummaryData($start, $end)
    {
        $revenueData = $this->getRevenueAnalytics($start, $end);
        $customerData = $this->getCustomerAnalytics($start, $end);
        $transactionData = $this->getTransactionAnalytics($start, $end);
        $productData = $this->getProductAnalytics($start, $end);
        $categoryData = $this->getCategoryAnalytics($start, $end);
        $syncData = $this->getSyncAnalytics($start, $end);

        return [
            'total_revenue' => $revenueData['total_revenue'],
            'total_orders' => $revenueData['total_orders'],
            'total_customers' => $customerData['total_customers'],
            'active_customers' => $customerData['active_customers'],
            'total_transactions' => $transactionData['total_transactions'],
            'total_items_sold' => $productData['total_items_sold'],
            'total_categories' => $categoryData['total_categories'],
            'sync_success_rate' => $syncData['sync_success_rate'],
            'recent_revenue_trend' => array_slice($revenueData['daily_revenue']->toArray(), -7), // Last 7 days
            'top_category' => $categoryData['top_category'],
            'payment_methods' => $transactionData['payment_methods']->take(3), // Top 3 payment methods
        ];
    }

    /**
     * Get revenue analytics data
     */
    private function getRevenueAnalytics($start, $end, $categoryId = null)
    {
        $orders = Order::whereBetween('created_at', [$start, $end])
                      ->whereIn('status', ['Completed', 'Paid']);

        // Filter by category if specified
        if ($categoryId) {
            $orders = $orders->whereHas('orderItems.menuItem', function($query) use ($categoryId) {
                $query->where('category_id', $categoryId);
            });
        }

        return [
            'total_revenue' => $orders->sum('total_amount'),
            'total_orders' => $orders->count(),
            'average_order_value' => $orders->avg('total_amount') ?? 0,
            'total_tax' => $orders->sum('tax_amount'),
            'total_discounts' => $orders->sum('discount_amount'),
            'daily_revenue' => $this->getDailyRevenue($start, $end),
            'revenue_by_order_type' => $this->getRevenueByOrderType($start, $end),
        ];
    }

    /**
     * Get customer analytics data
     */
    private function getCustomerAnalytics($start, $end, $categoryId = null)
    {
        $totalCustomers = Customer::count();

        $activeCustomersQuery = Customer::whereHas('orders', function($query) use ($start, $end, $categoryId) {
            $query->whereBetween('created_at', [$start, $end]);
            if ($categoryId) {
                $query->whereHas('orderItems.menuItem', function($subQuery) use ($categoryId) {
                    $subQuery->where('category_id', $categoryId);
                });
            }
        });
        $activeCustomers = $activeCustomersQuery->count();

        $topCustomers = Customer::select('customers.*')
            ->selectRaw('COUNT(orders.id) as order_count')
            ->selectRaw('SUM(orders.total_amount) as total_spent')
            ->leftJoin('orders', 'customers.id', '=', 'orders.customer_id')
            ->whereBetween('orders.created_at', [$start, $end])
            ->whereIn('orders.status', ['Completed', 'Paid'])
            ->groupBy('customers.id')
            ->orderBy('total_spent', 'desc')
            ->limit(10)
            ->get();

        return [
            'total_customers' => $totalCustomers,
            'active_customers' => $activeCustomers,
            'new_customers' => Customer::whereBetween('created_at', [$start, $end])->count(),
            'top_customers' => $topCustomers,
            'customer_retention_rate' => $this->getCustomerRetentionRate($start, $end),
        ];
    }

    /**
     * Get transaction analytics data
     */
    private function getTransactionAnalytics($start, $end, $categoryId = null)
    {
        $payments = Payment::whereBetween('created_at', [$start, $end]);

        $paymentMethods = Payment::select('method')
            ->selectRaw('COUNT(*) as count')
            ->selectRaw('SUM(amount_paid) as total_amount')
            ->whereBetween('created_at', [$start, $end])
            ->groupBy('method')
            ->get();

        return [
            'total_transactions' => $payments->count(),
            'total_amount_paid' => $payments->sum('amount_paid'),
            'average_transaction' => $payments->avg('amount_paid') ?? 0,
            'payment_methods' => $paymentMethods,
            'hourly_transactions' => $this->getHourlyTransactions($start, $end),
        ];
    }

    /**
     * Get product analytics data
     */
    private function getProductAnalytics($start, $end, $categoryId = null)
    {
        $topProducts = OrderItem::select('menu_items.name', 'menu_items.id')
            ->selectRaw('SUM(order_items.quantity) as total_quantity')
            ->selectRaw('SUM(order_items.quantity * order_items.price_at_time) as total_revenue')
            ->join('menu_items', 'order_items.menu_item_id', '=', 'menu_items.id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereBetween('orders.created_at', [$start, $end])
            ->whereIn('orders.status', ['Completed', 'Paid'])
            ->groupBy('menu_items.id', 'menu_items.name')
            ->orderBy('total_quantity', 'desc')
            ->limit(10)
            ->get();

        return [
            'top_products' => $topProducts,
            'total_items_sold' => OrderItem::join('orders', 'order_items.order_id', '=', 'orders.id')
                ->whereBetween('orders.created_at', [$start, $end])
                ->whereIn('orders.status', ['Completed', 'Paid'])
                ->sum('order_items.quantity'),
        ];
    }

    /**
     * Get category analytics data
     */
    private function getCategoryAnalytics($start, $end)
    {
        // Get category performance
        $categoryPerformance = OrderItem::select('menu_categories.name as category_name', 'menu_categories.id as category_id')
            ->selectRaw('SUM(order_items.quantity) as total_quantity')
            ->selectRaw('SUM(order_items.quantity * order_items.price_at_time) as total_revenue')
            ->selectRaw('COUNT(DISTINCT order_items.order_id) as order_count')
            ->join('menu_items', 'order_items.menu_item_id', '=', 'menu_items.id')
            ->join('menu_categories', 'menu_items.category_id', '=', 'menu_categories.id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereBetween('orders.created_at', [$start, $end])
            ->whereIn('orders.status', ['Completed', 'Paid'])
            ->groupBy('menu_categories.id', 'menu_categories.name')
            ->orderBy('total_revenue', 'desc')
            ->get();

        // Get category distribution for pie chart
        $totalRevenue = $categoryPerformance->sum('total_revenue');
        $categoryDistribution = $categoryPerformance->map(function($category) use ($totalRevenue) {
            return [
                'name' => $category->category_name,
                'revenue' => $category->total_revenue,
                'percentage' => $totalRevenue > 0 ? ($category->total_revenue / $totalRevenue) * 100 : 0,
                'quantity' => $category->total_quantity,
                'orders' => $category->order_count
            ];
        });

        return [
            'category_performance' => $categoryPerformance,
            'category_distribution' => $categoryDistribution,
            'total_categories' => $categoryPerformance->count(),
            'top_category' => $categoryPerformance->first(),
        ];
    }

    /**
     * Get sync analytics data
     */
    private function getSyncAnalytics($start, $end, $categoryId = null)
    {
        $orders = Order::whereBetween('created_at', [$start, $end])
                      ->whereIn('status', ['Completed', 'Paid']);

        return [
            'total_syncable_orders' => $orders->count(),
            'synced_orders' => $orders->where('isSync', true)->count(),
            'unsynced_orders' => $orders->where('isSync', false)->count(),
            'failed_sync_orders' => $orders->whereNotNull('sync_error')->count(),
            'sync_success_rate' => $orders->count() > 0 ?
                ($orders->where('isSync', true)->count() / $orders->count()) * 100 : 0,
        ];
    }

    /**
     * Get daily revenue data for charts
     */
    private function getDailyRevenue($start, $end)
    {
        $dateFormat = $this->getDateFormat();

        return Order::select(DB::raw("{$dateFormat} as date"))
            ->selectRaw('SUM(total_amount) as revenue')
            ->selectRaw('COUNT(*) as orders')
            ->whereBetween('created_at', [$start, $end])
            ->whereIn('status', ['Completed', 'Paid'])
            ->groupBy(DB::raw($dateFormat))
            ->orderBy('date')
            ->get();
    }

    /**
     * Get revenue by order type
     */
    private function getRevenueByOrderType($start, $end)
    {
        return Order::select('order_type')
            ->selectRaw('SUM(total_amount) as revenue')
            ->selectRaw('COUNT(*) as orders')
            ->whereBetween('created_at', [$start, $end])
            ->whereIn('status', ['Completed', 'Paid'])
            ->groupBy('order_type')
            ->get();
    }

    /**
     * Get hourly transaction data
     */
    private function getHourlyTransactions($start, $end)
    {
        $hourFormat = $this->getHourFormat();
        $hourGroupBy = $this->getHourGroupBy();

        return Payment::select(DB::raw("{$hourFormat} as hour"))
            ->selectRaw('COUNT(*) as count')
            ->selectRaw('SUM(amount_paid) as total')
            ->whereBetween('created_at', [$start, $end])
            ->groupBy(DB::raw($hourGroupBy))
            ->orderBy('hour')
            ->get();
    }

    /**
     * Calculate customer retention rate
     */
    private function getCustomerRetentionRate($start, $end)
    {
        $previousPeriod = $start->copy()->subDays($start->diffInDays($end));

        $previousCustomers = Customer::whereHas('orders', function($query) use ($previousPeriod, $start) {
            $query->whereBetween('created_at', [$previousPeriod, $start]);
        })->pluck('id');

        $returningCustomers = Customer::whereIn('id', $previousCustomers)
            ->whereHas('orders', function($query) use ($start, $end) {
                $query->whereBetween('created_at', [$start, $end]);
            })->count();

        return $previousCustomers->count() > 0 ?
            ($returningCustomers / $previousCustomers->count()) * 100 : 0;
    }

    /**
     * Get database-specific date format for daily grouping
     */
    private function getDateFormat()
    {
        $driver = DB::connection()->getDriverName();

        return match($driver) {
            'sqlite' => "strftime('%Y-%m-%d', created_at)",
            'mysql' => "DATE(created_at)",
            'pgsql' => "DATE(created_at)",
            default => "DATE(created_at)"
        };
    }

    /**
     * Get database-specific hour format
     */
    private function getHourFormat()
    {
        $driver = DB::connection()->getDriverName();

        return match($driver) {
            'sqlite' => "CAST(strftime('%H', created_at) AS INTEGER)",
            'mysql' => "HOUR(created_at)",
            'pgsql' => "EXTRACT(HOUR FROM created_at)",
            default => "HOUR(created_at)"
        };
    }

    /**
     * Get database-specific hour group by format
     */
    private function getHourGroupBy()
    {
        $driver = DB::connection()->getDriverName();

        return match($driver) {
            'sqlite' => "strftime('%H', created_at)",
            'mysql' => "HOUR(created_at)",
            'pgsql' => "EXTRACT(HOUR FROM created_at)",
            default => "HOUR(created_at)"
        };
    }

    /**
     * Export analytics data
     */
    public function export(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        $format = $request->get('format', 'csv'); // csv or json

        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();

        // Get all analytics data
        $data = [
            'export_info' => [
                'generated_at' => now()->toISOString(),
                'date_range' => [
                    'start' => $startDate,
                    'end' => $endDate
                ],
                'generated_by' => auth()->user()->name ?? 'System'
            ],
            'revenue' => $this->getRevenueAnalytics($start, $end),
            'customers' => $this->getCustomerAnalytics($start, $end),
            'transactions' => $this->getTransactionAnalytics($start, $end),
            'products' => $this->getProductAnalytics($start, $end),
            'categories' => $this->getCategoryAnalytics($start, $end),
            'sync' => $this->getSyncAnalytics($start, $end)
        ];

        if ($format === 'csv') {
            return $this->exportToCsv($data, $startDate, $endDate);
        } else {
            return $this->exportToJson($data, $startDate, $endDate);
        }
    }

    /**
     * Export data to CSV format
     */
    private function exportToCsv($data, $startDate, $endDate)
    {
        $filename = "analytics_export_{$startDate}_to_{$endDate}.csv";

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // Export Summary
            fputcsv($file, ['ANALYTICS EXPORT SUMMARY']);
            fputcsv($file, ['Generated At', $data['export_info']['generated_at']]);
            fputcsv($file, ['Date Range', $data['export_info']['date_range']['start'] . ' to ' . $data['export_info']['date_range']['end']]);
            fputcsv($file, ['Generated By', $data['export_info']['generated_by']]);
            fputcsv($file, []);

            // Revenue Summary
            fputcsv($file, ['REVENUE ANALYTICS']);
            fputcsv($file, ['Metric', 'Value']);
            fputcsv($file, ['Total Revenue', '$' . number_format($data['revenue']['total_revenue'], 2)]);
            fputcsv($file, ['Total Orders', number_format($data['revenue']['total_orders'])]);
            fputcsv($file, ['Average Order Value', '$' . number_format($data['revenue']['average_order_value'], 2)]);
            fputcsv($file, ['Total Tax', '$' . number_format($data['revenue']['total_tax'], 2)]);
            fputcsv($file, ['Total Discounts', '$' . number_format($data['revenue']['total_discounts'], 2)]);
            fputcsv($file, []);

            // Daily Revenue
            fputcsv($file, ['DAILY REVENUE']);
            fputcsv($file, ['Date', 'Revenue', 'Orders']);
            foreach ($data['revenue']['daily_revenue'] as $day) {
                fputcsv($file, [$day->date, '$' . number_format($day->revenue, 2), $day->orders]);
            }
            fputcsv($file, []);

            // Customer Analytics
            fputcsv($file, ['CUSTOMER ANALYTICS']);
            fputcsv($file, ['Metric', 'Value']);
            fputcsv($file, ['Total Customers', number_format($data['customers']['total_customers'])]);
            fputcsv($file, ['Active Customers', number_format($data['customers']['active_customers'])]);
            fputcsv($file, ['New Customers', number_format($data['customers']['new_customers'])]);
            fputcsv($file, ['Retention Rate', number_format($data['customers']['customer_retention_rate'], 1) . '%']);
            fputcsv($file, []);

            // Top Customers
            fputcsv($file, ['TOP CUSTOMERS']);
            fputcsv($file, ['Customer Name', 'Orders', 'Total Spent', 'Average Order']);
            foreach ($data['customers']['top_customers'] as $customer) {
                fputcsv($file, [
                    $customer->name,
                    $customer->order_count,
                    '$' . number_format($customer->total_spent, 2),
                    '$' . number_format($customer->total_spent / $customer->order_count, 2)
                ]);
            }
            fputcsv($file, []);

            // Transaction Analytics
            fputcsv($file, ['TRANSACTION ANALYTICS']);
            fputcsv($file, ['Metric', 'Value']);
            fputcsv($file, ['Total Transactions', number_format($data['transactions']['total_transactions'])]);
            fputcsv($file, ['Total Amount Paid', '$' . number_format($data['transactions']['total_amount_paid'], 2)]);
            fputcsv($file, ['Average Transaction', '$' . number_format($data['transactions']['average_transaction'], 2)]);
            fputcsv($file, []);

            // Payment Methods
            fputcsv($file, ['PAYMENT METHODS']);
            fputcsv($file, ['Method', 'Count', 'Total Amount']);
            foreach ($data['transactions']['payment_methods'] as $method) {
                fputcsv($file, [
                    ucfirst($method->method),
                    $method->count,
                    '$' . number_format($method->total_amount, 2)
                ]);
            }
            fputcsv($file, []);

            // Product Analytics
            fputcsv($file, ['PRODUCT ANALYTICS']);
            fputcsv($file, ['Total Items Sold', number_format($data['products']['total_items_sold'])]);
            fputcsv($file, []);
            fputcsv($file, ['TOP PRODUCTS']);
            fputcsv($file, ['Product Name', 'Quantity Sold', 'Revenue']);
            foreach ($data['products']['top_products'] as $product) {
                fputcsv($file, [
                    $product->name,
                    $product->total_quantity,
                    '$' . number_format($product->total_revenue, 2)
                ]);
            }
            fputcsv($file, []);

            // Category Analytics
            fputcsv($file, ['CATEGORY ANALYTICS']);
            fputcsv($file, ['Total Categories', number_format($data['categories']['total_categories'])]);
            if ($data['categories']['top_category']) {
                fputcsv($file, ['Top Category', $data['categories']['top_category']->category_name]);
            }
            fputcsv($file, []);
            fputcsv($file, ['CATEGORY PERFORMANCE']);
            fputcsv($file, ['Category', 'Revenue', 'Quantity', 'Orders', 'Percentage']);
            foreach ($data['categories']['category_distribution'] as $category) {
                fputcsv($file, [
                    $category['name'],
                    '$' . number_format($category['revenue'], 2),
                    $category['quantity'],
                    $category['orders'],
                    number_format($category['percentage'], 1) . '%'
                ]);
            }
            fputcsv($file, []);

            // Sync Analytics
            fputcsv($file, ['SYNC ANALYTICS']);
            fputcsv($file, ['Metric', 'Value']);
            fputcsv($file, ['Syncable Orders', number_format($data['sync']['total_syncable_orders'])]);
            fputcsv($file, ['Synced Orders', number_format($data['sync']['synced_orders'])]);
            fputcsv($file, ['Pending Sync', number_format($data['sync']['unsynced_orders'])]);
            fputcsv($file, ['Failed Sync', number_format($data['sync']['failed_sync_orders'])]);
            fputcsv($file, ['Success Rate', number_format($data['sync']['sync_success_rate'], 1) . '%']);

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export data to JSON format
     */
    private function exportToJson($data, $startDate, $endDate)
    {
        $filename = "analytics_export_{$startDate}_to_{$endDate}.json";

        $headers = [
            'Content-Type' => 'application/json',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        return response()->json($data, 200, $headers);
    }
}
