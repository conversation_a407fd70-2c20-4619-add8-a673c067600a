@extends('analytics.layout')

@section('title', 'Overview')
@section('description', 'Key performance indicators and business metrics at a glance')

@section('header-actions')
    <div class="relative">
        <button onclick="toggleExportDropdown()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded flex items-center">
            📊 Export Data
            <svg class="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
            </svg>
        </button>
        <div id="exportDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
            <div class="py-1">
                <button onclick="exportData('csv')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    📄 Export as CSV
                </button>
                <button onclick="exportData('json')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    📋 Export as JSON
                </button>
            </div>
        </div>
    </div>
@endsection

@section('content')

<!-- Date Range Filter -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">📅 Date Range</h3>
        <form method="GET" action="{{ route('analytics.index') }}" class="flex items-center space-x-4">
            <div>
                <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                <input type="date" id="start_date" name="start_date" value="{{ $startDate }}"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            </div>
            <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                <input type="date" id="end_date" name="end_date" value="{{ $endDate }}"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            </div>
            <div class="pt-6">
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    📊 Update Overview
                </button>
            </div>
        </form>
        
        <!-- Quick Date Filters -->
        <div class="mt-4 pt-4 border-t border-gray-200">
            <p class="text-sm font-medium text-gray-700 mb-2">Quick Ranges:</p>
            <div class="flex flex-wrap gap-2">
                <a href="{{ route('analytics.index', ['start_date' => now()->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}"
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">Today</a>
                <a href="{{ route('analytics.index', ['start_date' => now()->subDays(7)->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}"
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">Last 7 Days</a>
                <a href="{{ route('analytics.index', ['start_date' => now()->subDays(30)->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}"
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">Last 30 Days</a>
                <a href="{{ route('analytics.index', ['start_date' => now()->startOfMonth()->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}"
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">This Month</a>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics Overview -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-bold">$</span>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Revenue</p>
                    <p class="text-2xl font-bold text-green-600">${{ number_format($summaryData['total_revenue'], 2) }}</p>
                </div>
            </div>
            <div class="mt-4">
                <a href="{{ route('analytics.revenue', ['start_date' => $startDate, 'end_date' => $endDate]) }}"
                   class="text-sm text-blue-600 hover:text-blue-800">View Details →</a>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-bold">#</span>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Orders</p>
                    <p class="text-2xl font-bold text-blue-600">{{ number_format($summaryData['total_orders']) }}</p>
                </div>
            </div>
            <div class="mt-4">
                <a href="{{ route('analytics.transactions', ['start_date' => $startDate, 'end_date' => $endDate]) }}"
                   class="text-sm text-blue-600 hover:text-blue-800">View Details →</a>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-bold">👥</span>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Active Customers</p>
                    <p class="text-2xl font-bold text-purple-600">{{ number_format($summaryData['active_customers']) }}</p>
                </div>
            </div>
            <div class="mt-4">
                <a href="{{ route('analytics.customers', ['start_date' => $startDate, 'end_date' => $endDate]) }}"
                   class="text-sm text-blue-600 hover:text-blue-800">View Details →</a>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-bold">📦</span>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Items Sold</p>
                    <p class="text-2xl font-bold text-orange-600">{{ number_format($summaryData['total_items_sold']) }}</p>
                </div>
            </div>
            <div class="mt-4">
                <a href="{{ route('analytics.products', ['start_date' => $startDate, 'end_date' => $endDate]) }}"
                   class="text-sm text-blue-600 hover:text-blue-800">View Details →</a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Insights -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <!-- Revenue Trend -->
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">📈 Recent Revenue Trend</h3>
            <div class="bg-gray-50 p-4 rounded-lg">
                <canvas id="revenueOverviewChart" width="400" height="200"></canvas>
            </div>
            <div class="mt-4">
                <a href="{{ route('analytics.revenue', ['start_date' => $startDate, 'end_date' => $endDate]) }}"
                   class="text-sm text-blue-600 hover:text-blue-800">View Full Revenue Analytics →</a>
            </div>
        </div>
    </div>

    <!-- Top Category -->
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">🏆 Performance Highlights</h3>

            @if($summaryData['top_category'])
                <div class="mb-4 p-4 bg-green-50 rounded-lg">
                    <h4 class="font-semibold text-green-800">Top Category</h4>
                    <p class="text-2xl font-bold text-green-600">{{ $summaryData['top_category']->category_name }}</p>
                    <p class="text-sm text-green-600">${{ number_format($summaryData['top_category']->total_revenue, 2) }} revenue</p>
                </div>
            @endif

            <div class="mb-4 p-4 bg-blue-50 rounded-lg">
                <h4 class="font-semibold text-blue-800">Sync Status</h4>
                <p class="text-2xl font-bold text-blue-600">{{ number_format($summaryData['sync_success_rate'], 1) }}%</p>
                <p class="text-sm text-blue-600">Success Rate</p>
            </div>

            <div class="mt-4">
                <a href="{{ route('analytics.categories', ['start_date' => $startDate, 'end_date' => $endDate]) }}"
                   class="text-sm text-blue-600 hover:text-blue-800">View Category Analytics →</a>
            </div>
        </div>
    </div>
</div>

<!-- Analytics Sections -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">📊 Analytics Sections</h3>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <a href="{{ route('analytics.revenue', ['start_date' => $startDate, 'end_date' => $endDate]) }}"
               class="block p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-lg hover:from-green-100 hover:to-green-200 transition-all duration-200">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-green-800">Revenue Analytics</h4>
                </div>
                <p class="text-green-700">Track sales performance, daily trends, and revenue patterns</p>
            </a>

            <a href="{{ route('analytics.customers', ['start_date' => $startDate, 'end_date' => $endDate]) }}"
               class="block p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg hover:from-blue-100 hover:to-blue-200 transition-all duration-200">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-blue-800">Customer Analytics</h4>
                </div>
                <p class="text-blue-700">Analyze customer behavior, retention, and top spenders</p>
            </a>

            <a href="{{ route('analytics.transactions', ['start_date' => $startDate, 'end_date' => $endDate]) }}"
               class="block p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg hover:from-purple-100 hover:to-purple-200 transition-all duration-200">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-purple-800">Transaction Analytics</h4>
                </div>
                <p class="text-purple-700">Payment methods, transaction patterns, and timing analysis</p>
            </a>

            <a href="{{ route('analytics.products', ['start_date' => $startDate, 'end_date' => $endDate]) }}"
               class="block p-6 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg hover:from-orange-100 hover:to-orange-200 transition-all duration-200">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM6 9a1 1 0 112 0 1 1 0 01-2 0zm6 0a1 1 0 112 0 1 1 0 01-2 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-orange-800">Product Analytics</h4>
                </div>
                <p class="text-orange-700">Best-selling items, product performance, and inventory insights</p>
            </a>

            <a href="{{ route('analytics.categories', ['start_date' => $startDate, 'end_date' => $endDate]) }}"
               class="block p-6 bg-gradient-to-br from-pink-50 to-pink-100 rounded-lg hover:from-pink-100 hover:to-pink-200 transition-all duration-200">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-pink-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"/>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-pink-800">Category Analytics</h4>
                </div>
                <p class="text-pink-700">Category performance, distribution, and contribution analysis</p>
            </a>

            <a href="{{ route('analytics.sync', ['start_date' => $startDate, 'end_date' => $endDate]) }}"
               class="block p-6 bg-gradient-to-br from-teal-50 to-teal-100 rounded-lg hover:from-teal-100 hover:to-teal-200 transition-all duration-200">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-teal-800">Sync Analytics</h4>
                </div>
                <p class="text-teal-700">API synchronization status, success rates, and error tracking</p>
            </a>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Revenue Overview Chart
    const revenueOverviewData = @json($summaryData['recent_revenue_trend']);
    const revenueOverviewCtx = document.getElementById('revenueOverviewChart').getContext('2d');
    new Chart(revenueOverviewCtx, {
        type: 'line',
        data: {
            labels: revenueOverviewData.map(item => item.date),
            datasets: [{
                label: 'Daily Revenue',
                data: revenueOverviewData.map(item => item.revenue),
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Export dropdown functionality
    function toggleExportDropdown() {
        const dropdown = document.getElementById('exportDropdown');
        dropdown.classList.toggle('hidden');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const dropdown = document.getElementById('exportDropdown');
        const button = event.target.closest('button');

        if (!button || !button.onclick || button.onclick.toString().indexOf('toggleExportDropdown') === -1) {
            dropdown.classList.add('hidden');
        }
    });

    // Export functionality
    function exportData(format = 'csv') {
        const button = event.target;
        const originalText = button.innerHTML;

        // Show loading state
        button.disabled = true;
        button.innerHTML = '⏳ Exporting...';

        // Create form data
        const formData = new FormData();
        formData.append('start_date', '{{ $startDate }}');
        formData.append('end_date', '{{ $endDate }}');
        formData.append('format', format);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        fetch('{{ route("analytics.export") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                // Get filename from response headers or create default
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = `analytics_export_{{ $startDate }}_to_{{ $endDate }}.${format}`;

                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        filename = filenameMatch[1];
                    }
                }

                // Create blob and download
                return response.blob().then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    // Hide dropdown
                    document.getElementById('exportDropdown').classList.add('hidden');

                    // Show success message
                    alert(`Analytics data exported successfully as ${format.toUpperCase()}!`);
                });
            } else {
                throw new Error('Export failed');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Export failed. Please try again.');
        })
        .finally(() => {
            // Restore button state
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }
</script>
@endsection
