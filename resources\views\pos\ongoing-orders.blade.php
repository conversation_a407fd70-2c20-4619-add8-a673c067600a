<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Ongoing Orders') }}
            </h2>
            <div class="flex items-center space-x-3">
                <span class="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                    {{ $totalOngoing }} Active Orders
                </span>
                <a href="{{ route('pos.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Back to POS
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    
                    <!-- Search and Filter Section -->
                    <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                        <div class="flex-1 max-w-lg">
                            <form method="GET" action="{{ route('pos.ongoing-orders') }}" class="flex items-center space-x-2">
                                <input type="text" 
                                       name="search" 
                                       value="{{ request('search') }}"
                                       placeholder="Search by customer, table, or order ID..." 
                                       class="flex-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Search
                                </button>
                                @if(request('search'))
                                    <a href="{{ route('pos.ongoing-orders') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                        Clear
                                    </a>
                                @endif
                            </form>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <button onclick="location.reload()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                                🔄 Refresh
                            </button>
                            <button onclick="cleanupTableStatuses()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                                🔧 Fix Tables
                            </button>
                            <button onclick="syncAllTransactions()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                                📤 Sync All Transactions
                            </button>
                            <button onclick="showSyncStats()" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                                📊 Sync Stats
                            </button>
                        </div>
                    </div>

                    <!-- Orders Count -->
                    <div class="mb-4">
                        <p class="text-gray-600">
                            @if(request('search'))
                                Showing {{ $orders->count() }} ongoing order(s) matching "{{ request('search') }}"
                            @else
                                Showing {{ $orders->count() }} of {{ $totalOngoing }} ongoing orders
                            @endif
                        </p>
                    </div>

                    <!-- Orders Table -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full table-auto">
                            <thead>
                                <tr class="bg-gray-50">
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Table</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cashier</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Party</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sync</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($orders as $order)
                                    <tr class="hover:bg-gray-50
                                        {{ $order->created_at->setTimezone('Asia/Jakarta')->diffInMinutes() > 30 ? 'order-urgent' : '' }}
                                        {{ $order->status === 'Pending' ? 'order-row-pending' : '' }}
                                        {{ $order->status === 'In Progress' ? 'order-row-progress' : '' }}
                                        {{ $order->status === 'Preparing' ? 'order-row-preparing' : '' }}
                                        {{ $order->status === 'Ready' ? 'order-row-ready' : '' }}
                                        {{ $order->status === 'Completed' ? 'order-row-completed' : '' }}">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            #{{ $order->id }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $order->customer->name ?? 'Guest' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            @if($order->table)
                                                {{ $order->table->name }}
                                            @else
                                                <span class="text-gray-400 italic">No Table</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-blue-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                </svg>
                                                <span class="text-xs">{{ $order->cashier_name ?? 'Unknown' }}</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                                </svg>
                                                {{ $order->party_size ?? 2 }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold">
                                            {{ $orderSettings['currency_symbol'] }}{{ number_format($order->total_amount, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                @if($order->status === 'Pending') bg-yellow-100 text-yellow-800
                                                @elseif($order->status === 'In Progress') bg-blue-100 text-blue-800
                                                @elseif($order->status === 'Preparing') bg-orange-100 text-orange-800
                                                @elseif($order->status === 'Ready') bg-green-100 text-green-800
                                                @elseif($order->status === 'Completed') bg-purple-100 text-purple-800
                                                @endif">
                                                {{ $order->status }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full
                                                @if($order->isSync) bg-green-100 text-green-800
                                                @elseif($order->sync_error) bg-red-100 text-red-800
                                                @elseif($order->needsSync()) bg-yellow-100 text-yellow-800
                                                @else bg-gray-100 text-gray-800
                                                @endif">
                                                @if($order->isSync)
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                                    </svg>
                                                @elseif($order->sync_error)
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                                    </svg>
                                                @elseif($order->needsSync())
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                                    </svg>
                                                @else
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                                    </svg>
                                                @endif
                                                {{ $order->sync_status }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                @if($order->order_type === 'dine_in') bg-blue-100 text-blue-800
                                                @elseif($order->order_type === 'delivery') bg-purple-100 text-purple-800
                                                @else bg-orange-100 text-orange-800
                                                @endif">
                                                @if($order->order_type === 'dine_in')
                                                    Dine In
                                                @elseif($order->order_type === 'delivery')
                                                    Delivery
                                                @else
                                                    Take Away
                                                @endif
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <div class="flex flex-col">
                                                <span>{{ $order->created_at->setTimezone('Asia/Jakarta')->format('H:i') }}</span>
                                                <span class="text-xs text-gray-400">{{ $order->created_at->setTimezone('Asia/Jakarta')->diffForHumans() }}</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <!-- Edit Action - Available for Pending and In Progress orders -->
                                                @if(in_array($order->status, ['Pending', 'In Progress', 'Preparing']))
                                                    <a href="{{ route('pos.order', $order) }}"
                                                       class="action-btn inline-flex items-center px-3 py-1.5 bg-blue-100 hover:bg-blue-200 text-blue-700 hover:text-blue-800 text-xs font-medium rounded-md transition-colors duration-200"
                                                       title="Edit order items and details">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                        </svg>
                                                        Edit
                                                    </a>
                                                @endif

                                                <!-- View Action - Always available -->
                                                <a href="{{ route('pos.order-details', $order) }}"
                                                   class="action-btn inline-flex items-center px-3 py-1.5 bg-gray-100 hover:bg-gray-200 text-gray-700 hover:text-gray-800 text-xs font-medium rounded-md transition-colors duration-200"
                                                   title="View order details">
                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                    View
                                                </a>

                                                <!-- Complete Action - Available for orders that are ready to be completed -->
                                                @if(in_array($order->status, ['Pending', 'In Progress', 'Preparing', 'Ready']))
                                                    <form action="{{ route('pos.complete-order', $order) }}" method="POST" class="inline-block"
                                                          onsubmit="return confirm('Are you sure you want to complete this order and proceed to payment?')">
                                                        @csrf
                                                        <button type="submit"
                                                                class="action-btn inline-flex items-center px-3 py-1.5 bg-green-100 hover:bg-green-200 text-green-700 hover:text-green-800 text-xs font-medium rounded-md transition-colors duration-200"
                                                                title="Complete order and proceed to payment">
                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                            </svg>
                                                            Complete
                                                        </button>
                                                    </form>
                                                @endif

                                                <!-- Process Payment Action - Available for completed orders -->
                                                @if($order->status === 'Completed')
                                                    <a href="{{ route('pos.payment', $order) }}"
                                                       class="action-btn inline-flex items-center px-3 py-1.5 bg-yellow-100 hover:bg-yellow-200 text-yellow-700 hover:text-yellow-800 text-xs font-medium rounded-md transition-colors duration-200"
                                                       title="Process payment for this order">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-3a2 2 0 00-2-2H9a2 2 0 00-2 2v3a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                                        </svg>
                                                        Process Payment
                                                    </a>
                                                @endif

                                                <!-- Sync Action - Available for completed/paid orders that need syncing -->
                                                @if($order->needsSync())
                                                    <button onclick="syncOrder({{ $order->id }})"
                                                            class="action-btn inline-flex items-center px-3 py-1.5 bg-purple-100 hover:bg-purple-200 text-purple-700 hover:text-purple-800 text-xs font-medium rounded-md transition-colors duration-200"
                                                            title="Sync order to API">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                        </svg>
                                                        Sync
                                                    </button>
                                                @endif

                                                <!-- Additional Actions Dropdown for other functions -->
                                                <div class="relative inline-block text-left">
                                                    <button type="button"
                                                            class="inline-flex items-center px-2 py-1.5 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-700 text-xs font-medium rounded-md transition-colors duration-200"
                                                            onclick="toggleDropdown('dropdown-{{ $order->id }}')"
                                                            title="More actions">
                                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                                        </svg>
                                                    </button>

                                                    <div id="dropdown-{{ $order->id }}" class="dropdown-menu hidden absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5">
                                                        <div class="py-1">
                                                            <a href="{{ route('pos.kitchen', $order) }}" target="_blank"
                                                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                                                </svg>
                                                                Print Kitchen Ticket
                                                            </a>
                                                            @if($order->status === 'Completed')
                                                                <a href="{{ route('pos.receipt', $order) }}" target="_blank"
                                                                   class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                                    </svg>
                                                                    Print Receipt
                                                                </a>
                                                            @endif
                                                            @if(in_array($order->status, ['Pending', 'In Progress']))
                                                                <form action="{{ route('pos.cancel-order', $order) }}" method="POST" class="inline-block w-full">
                                                                    @csrf
                                                                    <button type="submit"
                                                                            class="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                                                                            onclick="return confirm('Are you sure you want to cancel this order?')">
                                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                                        </svg>
                                                                        Cancel Order
                                                                    </button>
                                                                </form>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                                            <div class="flex flex-col items-center">
                                                <svg class="w-12 h-12 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                                </svg>
                                                @if(request('search'))
                                                    <p class="text-lg font-medium">No ongoing orders found matching "{{ request('search') }}"</p>
                                                    <p class="text-sm">Try adjusting your search terms</p>
                                                @else
                                                    <p class="text-lg font-medium">No ongoing orders</p>
                                                    <p class="text-sm">All orders are completed or there are no active orders</p>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($orders->hasPages())
                        <div class="mt-6">
                            {{ $orders->appends(request()->query())->links() }}
                        </div>
                    @endif

                </div>
            </div>
        </div>
    </div>

    <style>
        /* Custom styles for action buttons */
        .action-btn {
            transition: all 0.2s ease-in-out;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Dropdown animation */
        .dropdown-menu {
            transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
            transform-origin: top right;
        }

        .dropdown-menu.hidden {
            opacity: 0;
            transform: scale(0.95);
        }

        .dropdown-menu:not(.hidden) {
            opacity: 1;
            transform: scale(1);
        }

        /* Status-based row highlighting */
        .order-row-pending {
            border-left: 4px solid #f59e0b;
        }

        .order-row-progress {
            border-left: 4px solid #3b82f6;
        }

        .order-row-preparing {
            border-left: 4px solid #8b5cf6;
        }

        .order-row-ready {
            border-left: 4px solid #10b981;
        }

        .order-row-completed {
            border-left: 4px solid #8b5cf6;
        }

        /* Urgent order highlighting */
        .order-urgent {
            background-color: #fef3c7 !important;
            animation: pulse-warning 2s infinite;
        }

        @keyframes pulse-warning {
            0%, 100% {
                background-color: #fef3c7;
            }
            50% {
                background-color: #fde68a;
            }
        }
    </style>

    <script>
        // Dropdown functionality
        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            const isHidden = dropdown.classList.contains('hidden');

            // Close all other dropdowns
            document.querySelectorAll('[id^="dropdown-"]').forEach(d => {
                if (d.id !== dropdownId) {
                    d.classList.add('hidden');
                }
            });

            // Toggle current dropdown
            if (isHidden) {
                dropdown.classList.remove('hidden');
            } else {
                dropdown.classList.add('hidden');
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('[onclick^="toggleDropdown"]') && !event.target.closest('[id^="dropdown-"]')) {
                document.querySelectorAll('[id^="dropdown-"]').forEach(dropdown => {
                    dropdown.classList.add('hidden');
                });
            }
        });

        // Auto-refresh functionality (optional)
        let autoRefreshInterval;

        function startAutoRefresh() {
            autoRefreshInterval = setInterval(() => {
                location.reload();
            }, 30000); // Refresh every 30 seconds
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        }

        // Table status cleanup functionality
        function cleanupTableStatuses() {
            if (confirm('This will fix any inconsistent table statuses. Continue?')) {
                fetch('{{ route("pos.cleanup-tables") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Table statuses cleaned up successfully!');
                        location.reload();
                    } else {
                        alert('Error cleaning up table statuses: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error cleaning up table statuses.');
                });
            }
        }

        // Order sync functionality
        function syncOrder(orderId) {
            if (confirm('Sync this order to the API?')) {
                const button = event.target.closest('button');
                const originalText = button.innerHTML;

                // Show loading state
                button.disabled = true;
                button.innerHTML = '<svg class="w-3 h-3 mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Syncing...';

                fetch(`/pos/order/${orderId}/sync-transaction`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Order synced successfully!');
                        location.reload();
                    } else {
                        alert('Sync failed: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error syncing order.');
                })
                .finally(() => {
                    // Restore button state
                    button.disabled = false;
                    button.innerHTML = originalText;
                });
            }
        }

        function syncAllOrders() {
            if (confirm('Sync all unsynced orders to the API? This may take a while.')) {
                const button = event.target;
                const originalText = button.innerHTML;

                // Show loading state
                button.disabled = true;
                button.innerHTML = '⏳ Syncing All Orders...';

                fetch('/pos/sync-all-orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Batch sync failed: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error syncing orders.');
                })
                .finally(() => {
                    // Restore button state
                    button.disabled = false;
                    button.innerHTML = originalText;
                });
            }
        }

        // Bulk sync all transactions
        function syncAllTransactions() {
            if (confirm('Sync all unsynced transactions to the API? This may take a while.')) {
                const button = event.target.closest('button');
                const originalText = button.innerHTML;

                // Show loading state
                button.disabled = true;
                button.innerHTML = '⏳ Syncing All Transactions...';

                fetch('/pos/sync-all-transactions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Bulk sync completed successfully!\n${data.message}`);
                        location.reload();
                    } else {
                        alert('Bulk sync failed: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error during bulk sync.');
                })
                .finally(() => {
                    // Restore button state
                    button.disabled = false;
                    button.innerHTML = originalText;
                });
            }
        }

        // Show sync statistics
        function showSyncStats() {
            const button = event.target.closest('button');
            const originalText = button.innerHTML;

            // Show loading state
            button.disabled = true;
            button.innerHTML = '⏳ Loading Stats...';

            fetch('/pos/transaction-sync-stats', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const stats = data.stats;
                    const lastSync = stats.last_sync ? new Date(stats.last_sync).toLocaleString() : 'Never';

                    alert(`📊 Transaction Sync Statistics\n\n` +
                          `Total Syncable Orders: ${stats.total_syncable_orders}\n` +
                          `Synced Transactions: ${stats.synced_transactions}\n` +
                          `Unsynced Transactions: ${stats.unsynced_transactions}\n` +
                          `Failed Transactions: ${stats.failed_transactions}\n` +
                          `Last Sync: ${lastSync}`);
                } else {
                    alert('Failed to get sync statistics: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error getting sync statistics.');
            })
            .finally(() => {
                // Restore button state
                button.disabled = false;
                button.innerHTML = originalText;
            });
        }

        // Start auto-refresh by default (uncomment if desired)
        // startAutoRefresh();
    </script>
</x-app-layout>
