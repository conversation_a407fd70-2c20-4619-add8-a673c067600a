<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Get user role - handle both ApiUser and regular User models
        $userRole = null;
        if (method_exists($user, 'getRole')) {
            // ApiUser has getRole() method
            $userRole = $user->getRole();
        } elseif (isset($user->role)) {
            // Regular User model has role property
            $userRole = $user->role;
        } elseif (isset($user->attributes['role'])) {
            // Fallback for direct attribute access
            $userRole = $user->attributes['role'];
        }

        if (empty($roles) || in_array($userRole, $roles)) {
            return $next($request);
        }

        // Check if this is a request to menu items or categories (POS access)
        $isPosAccess = $request->is('menu-items*') || $request->is('menu-categories*');

        if ($isPosAccess) {
            // For POS access, show Indonesian message
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Anda tidak memiliki akses ke POS'], 403);
            }

            // For web requests, redirect with popup message
            return redirect()->back()->with('error', 'Anda tidak memiliki akses ke POS');
        }

        abort(403, 'Unauthorized access.');
    }
}
