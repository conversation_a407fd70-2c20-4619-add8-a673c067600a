<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CouponSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $coupons = [
            [
                'code' => 'WELCOME10',
                'name' => 'Welcome Discount',
                'description' => 'Get 10% off your first order',
                'type' => 'percentage',
                'value' => 10,
                'minimum_amount' => 0,
                'usage_limit' => 100,
                'used_count' => 0,
                'is_active' => true,
                'valid_from' => now(),
                'valid_until' => now()->addMonths(3),
            ],
            [
                'code' => 'SUMMER25',
                'name' => 'Summer Special',
                'description' => '25% off for summer season',
                'type' => 'percentage',
                'value' => 25,
                'minimum_amount' => 50,
                'usage_limit' => 50,
                'used_count' => 0,
                'is_active' => true,
                'valid_from' => now(),
                'valid_until' => now()->addMonths(2),
            ],
            [
                'code' => 'FLAT10',
                'name' => 'Flat $10 Off',
                'description' => 'Get $10 off your order',
                'type' => 'fixed_amount',
                'value' => 10,
                'minimum_amount' => 30,
                'usage_limit' => 200,
                'used_count' => 0,
                'is_active' => true,
                'valid_from' => now(),
                'valid_until' => now()->addMonths(6),
            ],
        ];

        foreach ($coupons as $coupon) {
            \App\Models\Coupon::create($coupon);
        }
    }
}
