<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'requires_table',
        'requires_party_size',
        'icon',
        'color',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'requires_table' => 'boolean',
        'requires_party_size' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Scope for active order types
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordering by sort_order and name
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Find order type by slug
     */
    public static function findBySlug($slug)
    {
        return static::where('slug', $slug)->first();
    }

    /**
     * Check if this order type requires a table
     */
    public function requiresTable(): bool
    {
        return $this->requires_table;
    }

    /**
     * Check if this order type requires party size
     */
    public function requiresPartySize(): bool
    {
        return $this->requires_party_size;
    }

    /**
     * Get the display name with icon
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }

    /**
     * Get orders of this type
     */
    public function orders()
    {
        return $this->hasMany(Order::class, 'order_type', 'slug');
    }
}
