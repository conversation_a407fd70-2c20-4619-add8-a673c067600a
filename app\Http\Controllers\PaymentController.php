<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\Payment;
use App\Models\OrderSetting;
use App\Services\TransactionSyncService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show payment form for an order
     */
    public function show(Order $order)
    {
        // Ensure order is completed and not already paid
        if ($order->status !== 'Completed') {
            return redirect()->route('pos.order', $order)
                           ->with('error', 'Order must be completed before payment.');
        }

        $order->load(['orderItems.menuItem', 'table', 'payments', 'customer', 'activeDiscounts']);

        // Check if already fully paid
        $totalPaid = $order->payments->sum('amount_paid');
        $remainingAmount = $order->total_amount - $totalPaid;

        if ($remainingAmount <= 0) {
            return redirect()->route('pos.receipt', $order)
                           ->with('info', 'Order is already fully paid.');
        }

        // Get order settings for the view
        $orderSettings = [
            'tax_rate' => OrderSetting::get('tax_rate', 0.10),
            'tax_charging_method' => OrderSetting::get('tax_charging_method', 'customer_pays'),
            'service_charge_rate' => OrderSetting::get('service_charge_rate', 0.00),
            'auto_apply_tax' => OrderSetting::get('auto_apply_tax', true),
            'auto_apply_service_charge' => OrderSetting::get('auto_apply_service_charge', false),
            'currency_symbol' => OrderSetting::get('currency_symbol', '$'),
        ];

        return view('payments.show', compact('order', 'totalPaid', 'remainingAmount', 'orderSettings'));
    }

    /**
     * Process payment
     */
    public function process(Request $request, Order $order)
    {
        $request->validate([
            'method' => 'required|in:Cash,Card,QRIS',
            'amount_paid' => 'required|numeric|min:0.00',
        ]);

        // Reload order to get latest totals (in case discounts were applied during payment)
        $order->refresh();
        $order->load(['payments']);

        $totalPaid = $order->payments->sum('amount_paid');
        $remainingAmount = $order->total_amount - $totalPaid;

        if ($request->amount_paid > $remainingAmount + 100000) { // Allow some overpayment for cash
            return back()->withErrors(['amount_paid' => 'Payment amount is too high.']);
        }

        $orderWasPaid = false;

        DB::transaction(function () use ($request, $order, $remainingAmount, &$orderWasPaid) {
            // Create payment record
            Payment::create([
                'order_id' => $order->id,
                'method' => $request->input('method'),
                'amount_paid' => $request->input('amount_paid'),
            ]);

            // Refresh the order to get updated payments relationship
            $order->refresh();

            // Check if order is fully paid
            $newTotalPaid = $order->payments->sum('amount_paid');

            if ($newTotalPaid >= $order->total_amount) {
                // Mark order as paid and ensure table is available
                $order->update(['status' => 'Paid']);
                if ($order->table) {
                    $order->table->update(['status' => 'Available']);
                }
                $orderWasPaid = true;
            }
        });

        // Attempt automatic transaction sync if order was just paid
        if ($orderWasPaid) {
            $this->attemptTransactionSync($order);
        }

        return redirect()->route('payments.receipt', $order)
                        ->with('success', 'Payment processed successfully.');
    }

    /**
     * Show receipt
     */
    public function receipt(Order $order)
    {
        $order->load(['orderItems.menuItem', 'table', 'payments', 'customer', 'activeDiscounts']);

        $totalPaid = $order->payments->sum('amount_paid');
        $change = max(0, $totalPaid - $order->total_amount);

        // Get order settings for the view
        $orderSettings = [
            'tax_rate' => OrderSetting::get('tax_rate', 0.10),
            'tax_charging_method' => OrderSetting::get('tax_charging_method', 'customer_pays'),
            'service_charge_rate' => OrderSetting::get('service_charge_rate', 0.00),
            'auto_apply_tax' => OrderSetting::get('auto_apply_tax', true),
            'auto_apply_service_charge' => OrderSetting::get('auto_apply_service_charge', false),
            'currency_symbol' => OrderSetting::get('currency_symbol', '$'),
        ];

        return view('payments.receipt', compact('order', 'totalPaid', 'change', 'orderSettings'));
    }

    /**
     * Calculate change for cash payments
     */
    public function calculateChange(Request $request)
    {
        $request->validate([
            'order_total' => 'required|numeric',
            'amount_paid' => 'required|numeric',
        ]);

        $change = max(0, $request->amount_paid - $request->order_total);

        return response()->json([
            'change' => $change,
            'formatted_change' => number_format($change, 2)
        ]);
    }

    /**
     * Attempt to sync transaction to API when order is paid
     */
    private function attemptTransactionSync(Order $order): void
    {
        try {
            Log::info('PaymentController: Attempting automatic transaction sync', [
                'order_id' => $order->id,
                'transaction_number' => $order->transaction_number
            ]);

            $transactionSyncService = app(TransactionSyncService::class);
            $result = $transactionSyncService->syncTransaction($order);

            if ($result['success']) {
                Log::info('PaymentController: Transaction sync successful', [
                    'order_id' => $order->id,
                    'transaction_number' => $order->transaction_number
                ]);
            } else {
                Log::warning('PaymentController: Transaction sync failed', [
                    'order_id' => $order->id,
                    'transaction_number' => $order->transaction_number,
                    'error' => $result['error']
                ]);
            }

        } catch (\Exception $e) {
            Log::error('PaymentController: Exception during transaction sync', [
                'order_id' => $order->id,
                'transaction_number' => $order->transaction_number,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
