<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            
            
            <!-- Controls Section -->
            <div class="flex items-center space-x-6">
                <!-- Data Source Indicator -->
                

                <!-- Unified Sync Button -->
                <div class="flex space-x-2">
                    <button id="syncAllBtn" onclick="syncAll()"
                            class="flex items-center space-x-2 px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm">
                        <!-- Sync All Icon -->
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                        </svg>
                    </button>
                </div>

                <!-- View Mode Toggle Switch -->
                <?php if($needsTables): ?>
                <div class="flex items-center space-x-3">
                    <div class="flex bg-gray-100 rounded-lg p-1 shadow-sm">
                        <button id="tableViewBtn" onclick="switchViewMode('table')"
                                class="view-mode-btn flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 bg-blue-500 text-white shadow-sm">
                            <!-- Table Grid Icon -->
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 3h4v4H3V3zm6 0h4v4H9V3zm6 0h2v4h-2V3zM3 9h4v4H3V9zm6 0h4v4H9V9zm6 0h2v4h-2V9zM3 15h4v2H3v-2zm6 0h4v2H9v-2zm6 0h2v2h-2v-2z"/>
                            </svg>
                            <span>Table View</span>
                        </button>
                        <button id="menuViewBtn" onclick="switchViewMode('menu')"
                                class="view-mode-btn flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                            <!-- Menu List Icon -->
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 8a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 12a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 16a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
                            </svg>
                            <span>Menu View</span>
                        </button>
                    </div>
                </div>
                <?php else: ?>
                <!-- VOO outlet - only show menu view indicator -->
                <div class="flex items-center space-x-3">
                    <span class="text-sm font-medium text-gray-700"><?php echo e(session('api_outlet_category') ?? 'VOO'); ?> Outlet - Direct Order Mode</span>
                    <div class="flex bg-green-100 rounded-lg p-1 shadow-sm">
                        <div class="flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium bg-green-500 text-white shadow-sm">
                            <!-- Menu List Icon -->
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 8a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 12a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 16a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
                            </svg>
                            <span>Direct Order</span>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-6">
        <div class="max-w-full mx-auto sm:px-6 lg:px-8">
            <?php if(session('success')): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('info')): ?>
                <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
                    <?php echo e(session('info')); ?>

                </div>
            <?php endif; ?>

            <!-- Table View Mode -->
            <?php if($needsTables): ?>
            <div id="tableViewMode" class="view-mode">


                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">Select a Table to Start Order</h3>

                        <!-- Table Grid -->
                        <div class="grid grid-cols-5 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-10 xl:grid-cols-12 gap-2 auto-rows-max">
                            <?php $__currentLoopData = $tables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($table->currentOrder): ?>
                                    <!-- Table with existing order -->
                                    <a href="<?php echo e(route('pos.order', $table->currentOrder->id)); ?>"
                                       class="flex flex-col items-center justify-center border-2 border-orange-500 bg-orange-50 hover:bg-orange-100 rounded-lg p-2 text-center transition-all duration-200 aspect-square min-w-0">
                                        <div class="mb-1">
                                            <svg class="w-5 h-5 mx-auto text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                            </svg>
                                        </div>
                                        <h4 class="font-semibold text-xs text-orange-800 truncate w-full"><?php echo e($table->name); ?></h4>
                                        <p class="text-xs text-orange-600">#<?php echo e($table->currentOrder->id); ?></p>
                                        <p class="text-xs text-orange-600"><?php echo e($table->currentOrder->party_size ?? 2); ?> guests</p>
                                        <p class="text-xs text-orange-600 font-medium"><?php echo e($orderSettings['currency_symbol']); ?><?php echo e(number_format($table->currentOrder->total_amount, 2)); ?></p>
                                    </a>
                                <?php else: ?>
                                    <!-- Available table -->
                                    <button type="button" onclick="selectTableForOrder(<?php echo e($table->id); ?>, 'table')"
                                            class="flex flex-col items-center justify-center border-2 border-green-500 bg-green-50 hover:bg-green-100 rounded-lg p-2 text-center transition-all duration-200 aspect-square min-w-0">
                                        <div class="mb-1">
                                            <svg class="w-5 h-5 mx-auto text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                            </svg>
                                        </div>
                                        <h4 class="font-semibold text-xs text-green-800 truncate w-full"><?php echo e($table->name); ?></h4>
                                        <p class="text-xs text-green-600">Available</p>
                                    </button>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Legend -->
                        <div class="flex justify-center space-x-6 text-sm mt-6">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-green-500 rounded mr-2"></div>
                                <span>Available</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-orange-500 rounded mr-2"></div>
                                <span>Has Order</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Menu View Mode -->
            <div id="menuViewMode" class="view-mode hidden">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">

                    <div class="lg:col-span-1">
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg sticky top-6">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold mb-4">Order Cart</h3>
                                
                                <div class="flex flex-row gap-2">
                                <!-- Table Selection -->
                                    <?php if($needsTables): ?>
                                    <div class="mb-4 w-2/5">
                                        <label for="selectedTable" class="block text-sm font-medium text-gray-700 mb-2">
                                            Select Table 
                                            
                                            
                                        </label>
                                        <select id="selectedTable" class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <option value="">No table (takeout/delivery)</option>
                                            <?php $__currentLoopData = $tables->where('currentOrder', null); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($table->id); ?>"><?php echo e($table->name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php if(isset($selectedTable) && $selectedTable && $selectedTable->currentOrder): ?>
                                                <!-- Include the selected table even if it has an order (for table-first workflow) -->
                                                <option value="<?php echo e($selectedTable->id); ?>"><?php echo e($selectedTable->name); ?></option>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    <?php else: ?>
                                    <!-- Hidden table selection for non-table outlets -->
                                    <input type="hidden" id="selectedTable" value="">
                                    <?php endif; ?>

                                    <!-- Customer Selection -->
                                    <div class="mb-4 w-3/5">
                                        <label for="menuCustomerSelect" class="block text-sm font-medium text-gray-700 mb-2">
                                            Select Customer <span class="text-red-500">*</span>
                                        </label>
                                        <div class="flex items-center space-x-2">
                                            <select id="menuCustomerSelect" class="w-1/2 flex-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                                <option value="">Select a customer</option>
                                                <?php $__currentLoopData = $customers ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php $customerObj = (object) $customer; ?>
                                                    <option value="<?php echo e($customerObj->id ?? ''); ?>"><?php echo e($customerObj->name ?? 'Unknown'); ?> - <?php echo e($customerObj->phone ?? 'No phone'); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <button type="button" onclick="showAddCustomerModal()"
                                                    class="bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-md transition-colors duration-200">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex flex-row gap-2">
                                    <!-- Order Type Selection -->
                                    <div class="mb-4 w-1/2">
                                        <label for="menuOrderType" class="block text-sm font-medium text-gray-700 mb-2">
                                            Order Type <span class="text-red-500">*</span>
                                        </label>
                                        <select id="menuOrderType" class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <?php $__currentLoopData = $orderTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $orderType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($orderType->slug); ?>"
                                                        data-requires-table="<?php echo e($orderType->requires_table ? 'true' : 'false'); ?>"
                                                        data-requires-party-size="<?php echo e($orderType->requires_party_size ? 'true' : 'false'); ?>">
                                                    <?php if($orderType->icon): ?>
                                                        <i class="<?php echo e($orderType->icon); ?>"></i>
                                                    <?php endif; ?>
                                                    <?php echo e($orderType->name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>

                                    <!-- Party Size (only for dine-in orders) -->
                                    <div class="mb-4 w-1/2" id="partySizeContainer">
                                        <label for="menuPartySize" class="block text-sm font-medium text-gray-700 mb-2">
                                            Party Size <span class="text-red-500">*</span>
                                        </label>
                                        <div class="flex items-center space-x-2">
                                            
                                            <input type="number" id="menuPartySize" min="1" max="20" value="2"
                                                class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                placeholder="Number of guests">
                                            <span class="text-sm text-gray-500">guests</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- Cart Items -->
                                <div class="mb-4 max-h-32">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">
                                        Order Items <span class="text-red-500">*</span>
                                    </h4>
                                    <div id="cartItems" class="space-y-3 max-h-32 overflow-y-auto border rounded-md p-3">
                                        <p class="text-gray-500 text-center py-4">No items in cart</p>
                                    </div>
                                </div>

                                <!-- Discount Section -->
                                <div class="border-t pt-4 mb-4">
                                    <div class="mb-3">
                                        <button onclick="toggleDiscountSection()"
                                                class="w-full text-left flex items-center justify-between text-sm font-medium text-blue-600 hover:text-blue-800">
                                            <span>Apply Discount</span>
                                            <svg id="discountToggleIcon" class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            </svg>
                                        </button>
                                    </div>

                                    <div id="discountSection" class="hidden space-y-3">
                                        <!-- Discount Type Selection -->
                                        <div>
                                            <label class="block text-xs font-medium text-gray-700 mb-1">Discount Type</label>
                                            <select id="discountType" onchange="handleDiscountTypeChange()"
                                                    class="w-full text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500">
                                                <option value="none">No Discount</option>
                                                <option value="percentage">Percentage Discount</option>
                                                <option value="fixed">Fixed Amount Discount</option>
                                                <option value="coupon">Coupon Code</option>
                                                <option value="points">Use Points</option>
                                            </select>
                                        </div>

                                        <!-- Percentage Discount -->
                                        <div id="percentageDiscountSection" class="hidden">
                                            <label class="block text-xs font-medium text-gray-700 mb-1">Discount Percentage</label>
                                            <div class="flex items-center space-x-2">
                                                <input type="number" id="discountPercentage" min="0" max="100" step="0.01"
                                                       class="flex-1 text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
                                                       placeholder="Enter percentage">
                                                <span class="text-sm text-gray-500">%</span>
                                                <button onclick="applyPercentageDiscount()"
                                                        class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs">
                                                    Apply
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Fixed Amount Discount -->
                                        <div id="fixedDiscountSection" class="hidden">
                                            <label class="block text-xs font-medium text-gray-700 mb-1">Discount Amount</label>
                                            <div class="flex items-center space-x-2">
                                                <span class="text-sm text-gray-500">$</span>
                                                <input type="number" id="discountAmount" min="0" step="0.01"
                                                       class="flex-1 text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
                                                       placeholder="Enter amount">
                                                <button onclick="applyFixedDiscount()"
                                                        class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs">
                                                    Apply
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Coupon Code -->
                                        <div id="couponDiscountSection" class="hidden">
                                            <label class="block text-xs font-medium text-gray-700 mb-1">Coupon Code</label>
                                            <div class="flex items-center space-x-2">
                                                <input type="text" id="couponCode"
                                                       class="flex-1 text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
                                                       placeholder="Enter coupon code">
                                                <button onclick="applyCouponDiscount()"
                                                        class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-xs">
                                                    Apply
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Points Discount -->
                                        <div id="pointsDiscountSection" class="hidden">
                                            <label class="block text-xs font-medium text-gray-700 mb-1">Use Points</label>
                                            <div class="text-xs text-gray-600 mb-2">
                                                Available: <span id="customerPoints">0</span> points (100 points = <?php echo e($orderSettings['currency_symbol']); ?>1.00)
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="number" id="pointsToUse" min="0" step="1"
                                                       class="flex-1 text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
                                                       placeholder="Enter points to use">
                                                <button onclick="applyPointsDiscount()"
                                                        class="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-xs">
                                                    Apply
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Applied Discounts Display -->
                                        <div id="appliedDiscountsSection" class="hidden">
                                            <div class="text-xs font-medium text-gray-700 mb-2">Applied Discounts:</div>
                                            <div id="appliedDiscountsList" class="space-y-2 max-h-32 overflow-y-auto">
                                                <!-- Discounts will be populated here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Order Total -->
                                <div class="border-t pt-4 mb-4">
                                    <div class="flex justify-between mb-2">
                                        <span class="text-sm text-gray-600">Subtotal:</span>
                                        <span id="cartSubtotal" class="text-sm font-medium"><?php echo e($orderSettings['currency_symbol']); ?>0.00</span>
                                    </div>
                                    <div id="discountRow" class="flex justify-between mb-2 hidden">
                                        <span class="text-sm text-gray-600">Discount:</span>
                                        <span id="cartDiscount" class="text-sm font-medium text-green-600">-<?php echo e($orderSettings['currency_symbol']); ?>0.00</span>
                                    </div>
                                    <div class="flex justify-between mb-2">
                                        <span class="text-sm text-gray-600">Tax (<?php echo e(number_format($orderSettings['tax_rate'] * 100, 1)); ?>%):</span>
                                        <span id="cartTax" class="text-sm font-medium"><?php echo e($orderSettings['currency_symbol']); ?>0.00</span>
                                    </div>
                                    <div id="serviceChargeRow" class="flex justify-between mb-2 hidden">
                                        <span class="text-sm text-gray-600">Service Charge (<?php echo e(number_format($orderSettings['service_charge_rate'] * 100, 1)); ?>%):</span>
                                        <span id="cartServiceCharge" class="text-sm font-medium"><?php echo e($orderSettings['currency_symbol']); ?>0.00</span>
                                    </div>
                                    <div class="flex justify-between border-t pt-2">
                                        <span class="font-semibold">Total:</span>
                                        <span id="cartTotal" class="font-semibold"><?php echo e($orderSettings['currency_symbol']); ?>0.00</span>
                                    </div>
                                </div>

                                <!-- Validation Status Indicator -->
                                <div id="validationStatus" class="mb-3 p-2 rounded-md border hidden">
                                    <div class="flex items-center">
                                        <svg id="validationIcon" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                        <span id="validationMessage" class="text-sm font-medium"></span>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="space-y-2">
                                    <button id="placeOrderBtn" onclick="placeMenuOrder()" disabled
                                            class="w-full bg-green-500 hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-bold py-3 px-4 rounded">
                                        Place Order
                                    </button>
                                    <button onclick="clearCart()"
                                            class="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded">
                                        Clear Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Menu Items Section -->
                    <div class="lg:col-span-2">
                        <div class="bg-white shadow-sm sm:rounded-lg">
                            <div class="p-6">

                                <!-- Search and Barcode Section -->
                                <div class="mb-6 space-y-4">
                                    <!-- Search Bar -->
                                    <div class="flex items-center space-x-2">
                                        <div class="flex-1 relative">
                                            <input type="text"
                                                   id="menuSearchInput"
                                                   placeholder="Search menu items by name, SKU, or barcode..."
                                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                                </svg>
                                            </div>
                                        </div>

                                        <!-- Barcode Scanner Toggle -->
                                        <button id="barcodeScannerToggle"
                                                onclick="toggleBarcodeScanner()"
                                                class="flex items-center space-x-2 px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors duration-200">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h2M4 4h5m0 0v5m0 0h5m0 0v5"></path>
                                            </svg>
                                            <span id="barcodeScannerText">Start Barcode Scanner</span>
                                        </button>

                                        <!-- Clear Search -->
                                        <button id="clearSearchBtn"
                                                onclick="clearMenuSearch()"
                                                class="px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200 hidden">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>

                                    <!-- Barcode Scanner Status -->
                                    <div id="barcodeScannerStatus" class="hidden p-3 bg-purple-50 border border-purple-200 rounded-lg">
                                        <div class="flex items-center space-x-2">
                                            <div class="animate-pulse">
                                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h2M4 4h5m0 0v5m0 0h5m0 0v5"></path>
                                                </svg>
                                            </div>
                                            <span class="text-purple-700 font-medium">Barcode scanner is active - scan a barcode to add item to cart</span>
                                        </div>
                                    </div>

                                    <!-- Search Results Info -->
                                    <div id="searchResultsInfo" class="hidden text-sm text-gray-600">
                                        <span id="searchResultsText"></span>
                                    </div>
                                </div>

                                <div class="flex flex-row">
                                <!-- Category Tabs -->
                                    <div class="border-b border-gray-200 mb-4 pr-12" id="categoryTabs">
                                        <nav class="-mb-px flex flex-col space-y-5 text-xl">
                                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <button onclick="showMenuCategory(<?php echo e($category->id); ?>)"
                                                        class="menu-category-tab <?php echo e($index === 0 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-blue-700 hover:border-blue-300'); ?> whitespace-nowrap py-2 px-1 border-b-2 font-medium text-lg"
                                                        data-category="<?php echo e($category->id); ?>">
                                                    <?php echo e($category->name); ?>

                                                </button>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </nav>
                                    </div>

                                    <!-- Search Results Container -->
                                    <div id="searchResultsContainer" class="hidden">
                                        <div class="grid grid-cols-2 md:grid-cols-3 gap-4" id="searchResultsGrid">
                                            <!-- Search results will be populated here -->
                                        </div>
                                    </div>

                                    <!-- Menu Items Grid -->
                                    <div id="menuCategoriesContainer">
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div id="menu-category-<?php echo e($category->id); ?>" class="menu-category-content <?php echo e($index !== 0 ? 'hidden' : ''); ?>">
                                                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                                                    <?php $__currentLoopData = $category->menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="menu-item border rounded-lg p-4 hover:shadow-md hover:border-blue-300 transition-all cursor-pointer"
                                                            data-item-id="<?php echo e($item->id); ?>"
                                                            data-item-name="<?php echo e(strtolower($item->name)); ?>"
                                                            data-item-sku="<?php echo e(strtolower($item->sku ?? '')); ?>"
                                                            data-item-barcode="<?php echo e($item->barcode ?? ''); ?>"
                                                            data-category-id="<?php echo e($category->id); ?>"
                                                            onclick="addItemDirectlyToCart(<?php echo e($item->id); ?>, '<?php echo e($item->name); ?>', <?php echo e($item->price); ?>)">
                                                            <?php if($item->image_path): ?>
                                                                <img src="<?php echo e(asset('storage/' . $item->image_path)); ?>"
                                                                    alt="<?php echo e($item->name); ?>"
                                                                    class="w-full object-cover rounded mb-2">
                                                            <?php else: ?>
                                                                <div class="w-full aspect-square bg-gray-200 rounded mb-2 flex items-center justify-center">
                                                                    <span class="text-gray-400 text-5xl"><?php echo e($hasil = implode('', array_map(fn($k) => mb_substr($k, 0, 1), explode(' ',$item->name)))); ?></span>
                                                                </div>
                                                            <?php endif; ?>
                                                            <h4 class="font-semibold text-sm mb-1"><?php echo e($item->name); ?></h4>
                                                            <p class="text-green-600 font-bold"><?php echo e($orderSettings['currency_symbol']); ?><?php echo e(number_format($item->price, 2)); ?></p>
                                                            <?php if($item->description): ?>
                                                                <p class="text-xs text-gray-500 mt-1"><?php echo e(Str::limit($item->description, 50)); ?></p>
                                                            <?php endif; ?>
                                                            <?php if($item->sku): ?>
                                                                <p class="text-xs text-gray-400 mt-1">SKU: <?php echo e($item->sku); ?></p>
                                                            <?php endif; ?>
                                                            <?php if($item->barcode): ?>
                                                                <p class="text-xs text-gray-400">Barcode: <?php echo e($item->barcode); ?></p>
                                                            <?php endif; ?>
                                                            <div class="mt-2 text-center">
                                                                <span class="text-xs text-blue-600 font-medium">Click to add to cart</span>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cart/Order Summary Section -->
                    
                </div>
            </div>
        </div>
    </div>

    <!-- Success Popup Modal -->
    <div id="successPopup" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                    <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4" id="successPopupTitle">Order Placed Successfully!</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500" id="successPopupMessage">
                        Your order has been processed successfully.
                    </p>
                </div>
                <div class="items-center px-4 py-3">
                    <!-- Auto-close progress bar -->
                    <div class="w-full bg-gray-200 rounded-full h-1 mb-3">
                        <div id="successPopupProgress" class="bg-green-500 h-1 rounded-full transition-all duration-100 ease-linear" style="width: 100%"></div>
                    </div>
                    <button id="successPopupClose"
                            class="px-4 py-2 bg-green-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-300">
                        Continue
                    </button>
                    <p class="text-xs text-gray-400 mt-2">Auto-closing in <span id="successPopupCountdown">4</span> seconds</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Customer Modal -->
    <div id="addCustomerModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-[500px] max-h-screen overflow-y-auto">
            <h3 class="text-lg font-semibold mb-4">Add New Customer</h3>
            <form id="addCustomerForm">
                <?php echo csrf_field(); ?>
                <!-- Customer Name (Required) -->
                <div class="mb-4">
                    <label for="newCustomerName" class="block text-sm font-medium text-gray-700 mb-2">
                        Customer Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="newCustomerName" name="customer_name" required
                           class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                           placeholder="Enter customer name">
                </div>

                <!-- Phone Number (Required) -->
                <div class="mb-4">
                    <label for="newCustomerPhone" class="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number <span class="text-red-500">*</span>
                    </label>
                    <input type="tel" id="newCustomerPhone" name="customer_phone" required
                           class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                           placeholder="Enter phone number">
                </div>

                <!-- Email (Optional) -->
                <div class="mb-4">
                    <label for="newCustomerEmail" class="block text-sm font-medium text-gray-700 mb-2">
                        Email Address <span class="text-gray-400">(Optional)</span>
                    </label>
                    <input type="email" id="newCustomerEmail" name="customer_email"
                           class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                           placeholder="Enter email address">
                </div>

                <!-- Gender (Optional) -->
                <div class="mb-4">
                    <label for="newCustomerGender" class="block text-sm font-medium text-gray-700 mb-2">
                        Gender <span class="text-gray-400">(Optional)</span>
                    </label>
                    <select id="newCustomerGender" name="customer_gender"
                            class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">Pilih salah satu opsi</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <!-- Date of Birth (Optional) -->
                <div class="mb-4">
                    <label for="newCustomerDOB" class="block text-sm font-medium text-gray-700 mb-2">
                        Date of Birth <span class="text-gray-400">(Optional)</span>
                    </label>
                    <input type="date" id="newCustomerDOB" name="customer_date_of_birth"
                           class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                </div>

                <!-- Customer Segment (Optional) -->
                <div class="mb-4">
                    <label for="newCustomerSegment" class="block text-sm font-medium text-gray-700 mb-2">
                        Customer Segment <span class="text-gray-400">(Optional)</span>
                    </label>
                    <select id="newCustomerSegment" name="customer_segment"
                            class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">Pilih salah satu opsi</option>
                        <option value="regular">Regular</option>
                        <option value="vip">VIP</option>
                        <option value="premium">Premium</option>
                    </select>
                </div>

                <!-- Address (Optional) -->
                <div class="mb-4">
                    <label for="newCustomerAddress" class="block text-sm font-medium text-gray-700 mb-2">
                        Address <span class="text-gray-400">(Optional)</span>
                    </label>
                    <textarea id="newCustomerAddress" name="customer_address" rows="2"
                              class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                              placeholder="Enter customer address"></textarea>
                </div>

                <!-- Notes (Optional) -->
                <div class="mb-4">
                    <label for="newCustomerNotes" class="block text-sm font-medium text-gray-700 mb-2">
                        Notes <span class="text-gray-400">(Optional)</span>
                    </label>
                    <textarea id="newCustomerNotes" name="customer_notes" rows="2"
                              class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                              placeholder="Any additional notes about the customer"></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeAddCustomerModal()"
                            class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                        Cancel
                    </button>
                    <button type="submit"
                            class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
                        Add Customer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <style>
        /* Custom styles for view mode toggle buttons */
        .view-mode-btn {
            min-width: 120px;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        .view-mode-btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .view-mode-btn:active {
            transform: translateY(1px);
        }

        /* Smooth transitions for better UX */
        .view-mode-btn svg {
            transition: transform 0.2s ease;
        }

        .view-mode-btn:hover svg {
            transform: scale(1.1);
        }

        /* Success popup animations */
        #successPopup {
            transition: opacity 0.3s ease-in-out;
        }

        #successPopup.hidden {
            opacity: 0;
            pointer-events: none;
        }

        #successPopup:not(.hidden) {
            opacity: 1;
            pointer-events: auto;
        }

        #successPopup .relative {
            animation: slideInFromTop 0.4s ease-out;
        }

        @keyframes slideInFromTop {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Success icon pulse animation */
        #successPopup .bg-green-100 {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }
    </style>

    <script>
        // Global variables
        let cart = [];
        let currentViewMode = <?php echo json_encode($needsTables ? 'table' : 'menu', 15, 512) ?>;
        let currentOrderId = null; // Track existing order ID for updates
        const currencySymbol = '<?php echo e($orderSettings['currency_symbol']); ?>';
        const serviceChargeRate = <?php echo e($orderSettings['service_charge_rate']); ?>;
        const autoApplyServiceCharge = <?php echo e($orderSettings['auto_apply_service_charge'] ? 'true' : 'false'); ?>;
        let needsTables = <?php echo json_encode($needsTables, 15, 512) ?>;

        // Order type persistence
        const LAST_ORDER_TYPE_KEY = 'pos_last_order_type';

        // Functions for order type persistence
        function saveLastOrderType(orderType) {
            try {
                localStorage.setItem(LAST_ORDER_TYPE_KEY, orderType);
            } catch (e) {
                console.warn('Could not save order type to localStorage:', e);
            }
        }

        function loadLastOrderType() {
            try {
                return localStorage.getItem(LAST_ORDER_TYPE_KEY);
            } catch (e) {
                console.warn('Could not load order type from localStorage:', e);
                return null;
            }
        }

        let currentDiscount = {
            type: 'none',
            value: 0,
            amount: 0,
            couponCode: null,
            pointsUsed: 0
        };

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {

            // Load last selected order type
            const lastOrderType = loadLastOrderType();
            const orderTypeSelect = document.getElementById('menuOrderType');
            if (lastOrderType && orderTypeSelect) {
                // Check if the saved order type still exists in the options
                const optionExists = Array.from(orderTypeSelect.options).some(option => option.value === lastOrderType);
                if (optionExists) {
                    orderTypeSelect.value = lastOrderType;
                    // Trigger change event to update UI accordingly
                    orderTypeSelect.dispatchEvent(new Event('change'));
                }
            }

            // Check URL parameters for automatic view switching and table selection
            const urlParams = new URLSearchParams(window.location.search);
            const viewParam = urlParams.get('view');
            const tableParam = urlParams.get('table');
            const successParam = urlParams.get('success');

            // Determine view mode - URL parameter takes precedence over saved preference
            let viewMode = needsTables ? 'table' : 'menu';
            if (needsTables) {
                if (viewParam === 'menu') {
                    viewMode = 'menu';
                } else {
                    // Load saved view mode preference if no URL parameter
                    viewMode = localStorage.getItem('posViewMode') || 'table';
                }
            }

            // Switch to the determined view mode
            switchViewMode(viewMode);

            // If table parameter is provided and we're in menu view, pre-select the table
            if (tableParam && viewMode === 'menu') {
                // Function to attempt table selection
                const attemptTableSelection = (attempt = 1, maxAttempts = 10) => {
                    const tableSelect = document.getElementById('selectedTable');

                    if (tableSelect && !tableSelect.classList.contains('hidden')) {
                        // Check if the table option exists
                        const targetOption = Array.from(tableSelect.options).find(opt => opt.value === tableParam);

                        if (targetOption) {
                            // Set the table selection
                            tableSelect.value = tableParam;

                            // Trigger the change event to update cart display
                            tableSelect.dispatchEvent(new Event('change'));
                            return; // Success, exit
                        } else {
                            // Try to add the table option dynamically if it doesn't exist
                            fetch(`/pos/table-info/${tableParam}`)
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        const option = new Option(data.table.name, data.table.id);
                                        tableSelect.add(option);
                                        tableSelect.value = tableParam;
                                        tableSelect.dispatchEvent(new Event('change'));
                                    }
                                })
                                .catch(error => {
                                    console.error('Error fetching table info:', error);
                                });
                            return; // Exit after attempting dynamic addition
                        }
                    } else {
                        // Retry if we haven't reached max attempts
                        if (attempt < maxAttempts) {
                            setTimeout(() => attemptTableSelection(attempt + 1, maxAttempts), 200);
                        }
                    }
                };

                // Start the table selection process
                setTimeout(() => attemptTableSelection(), 100);
            }

            // Show success message if provided
            if (successParam) {
                // Create and show a temporary success message
                const successDiv = document.createElement('div');
                successDiv.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded shadow-lg z-50';
                successDiv.innerHTML = `
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>${decodeURIComponent(successParam)}</span>
                    </div>
                `;
                document.body.appendChild(successDiv);

                // Remove the message after 5 seconds
                setTimeout(() => {
                    if (successDiv.parentNode) {
                        successDiv.parentNode.removeChild(successDiv);
                    }
                }, 5000);
            }

            // Clean up URL parameters after processing
            if (viewParam || tableParam || successParam) {
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);
            }
        });

        // Unified sync function for both products and customers
        async function syncAll() {
            const syncBtn = document.getElementById('syncAllBtn');
            const originalContent = syncBtn.innerHTML;

            // Show loading state
            syncBtn.disabled = true;
            syncBtn.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                </svg>
                <span>Syncing All Data...</span>
            `;

            try {
                console.log('Starting unified sync (products + customers)...');
                const overallStartTime = performance.now();

                // Use the new unified sync endpoint that handles both products and customers
                const response = await fetch('<?php echo e(route("pos.sync-products")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const result = await response.json();
                console.log('Unified sync result:', result);

                // Calculate total duration
                const overallEndTime = performance.now();
                const totalDuration = Math.round(overallEndTime - overallStartTime);

                // Show results based on the unified response
                if (result.success) {
                    showNotification('success', `${result.message} (${totalDuration}ms)`);
                    updateDataSourceIndicator('api_synced_to_database');

                    // Simple page refresh to show updated data
                    setTimeout(() => {
                        console.log('Reloading page to show updated prices...');
                        window.location.reload();
                    }, 1500);
                } else if (result.partial) {
                    showNotification('warning', `${result.message} (${totalDuration}ms)`);

                    // Refresh even on partial success to show any updates
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showNotification('error', `${result.message} (${totalDuration}ms)`);
                }

            } catch (error) {
                console.error('Overall sync error:', error);
                showNotification('error', 'Network error occurred during sync');
            } finally {
                // Restore button state after a short delay
                setTimeout(() => {
                    syncBtn.disabled = false;
                    syncBtn.innerHTML = originalContent;
                }, 1000);
            }
        }



        // Sync customers from API
        async function syncCustomers() {
            const syncBtn = document.getElementById('syncCustomersBtn');
            const originalContent = syncBtn.innerHTML;

            // Show loading state
            syncBtn.disabled = true;
            syncBtn.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                </svg>
                <span>Syncing...</span>
            `;

            try {
                console.log('Starting customer sync...');
                const startTime = performance.now();

                const response = await fetch('<?php echo e(route("settings.customers.sync")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const result = await response.json();
                const endTime = performance.now();
                const duration = Math.round(endTime - startTime);

                console.log('Customer sync completed in', duration, 'ms', result);

                if (result.success) {
                    // Show detailed success message
                    const message = `Customers synced successfully! (${duration}ms)`;
                    showNotification('success', message);

                    // Reload customer dropdown
                    setTimeout(() => {
                        console.log('Reloading page to show synced customers...');
                        window.location.reload();
                    }, 800);
                } else {
                    showNotification('error', result.message || 'Failed to sync customers');
                    console.error('Customer sync failed:', result);
                }

            } catch (error) {
                console.error('Customer sync error:', error);
                showNotification('error', 'Network error occurred while syncing customers');
            } finally {
                // Restore button state after a short delay to show completion
                setTimeout(() => {
                    syncBtn.disabled = false;
                    syncBtn.innerHTML = originalContent;
                }, 500);
            }
        }

        // Update data source indicator
        function updateDataSourceIndicator(source) {
            const indicator = document.querySelector('.data-source-indicator');
            if (indicator) {
                const span = indicator.querySelector('span:last-child');
                if (span) {
                    span.textContent = source === 'api_synced_to_database' ? '🌐 API (Synced)' : '💾 Local';
                    span.className = source === 'api_synced_to_database' ?
                        'px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800' :
                        'px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800';
                }
            }
        }

        // Show notification function
        function showNotification(type, message) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;

            if (type === 'success') {
                notification.classList.add('bg-green-100', 'border', 'border-green-400', 'text-green-700');
            } else {
                notification.classList.add('bg-red-100', 'border', 'border-red-400', 'text-red-700');
            }

            notification.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        ${type === 'success' ?
                            '<svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' :
                            '<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>'
                        }
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium">${message}</p>
                    </div>
                    <div class="ml-auto pl-3">
                        <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        }

        // Switch between view modes
        function switchViewMode(mode) {
            currentViewMode = mode;
            const tableView = document.getElementById('tableViewMode');
            const menuView = document.getElementById('menuViewMode');
            const tableBtn = document.getElementById('tableViewBtn');
            const menuBtn = document.getElementById('menuViewBtn');

            // For outlets that don't need tables, always show menu view
            if (!needsTables) {
                if (tableView) tableView.classList.add('hidden');
                menuView.classList.remove('hidden');
                return;
            }

            // Save preference (only for outlets with tables)
            localStorage.setItem('posViewMode', mode);

            if (mode === 'table') {
                // Show/hide views
                tableView.classList.remove('hidden');
                menuView.classList.add('hidden');

                // Update button styles
                if (tableBtn) tableBtn.className = 'view-mode-btn flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 bg-blue-500 text-white shadow-sm';
                if (menuBtn) menuBtn.className = 'view-mode-btn flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-800 hover:bg-gray-50';
            } else {
                // Show/hide views
                if (tableView) tableView.classList.add('hidden');
                menuView.classList.remove('hidden');

                // Update button styles
                if (tableBtn) tableBtn.className = 'view-mode-btn flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-800 hover:bg-gray-50';
                if (menuBtn) menuBtn.className = 'view-mode-btn flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 bg-blue-500 text-white shadow-sm';
            }
        }

        // Menu category switching (for menu view)
        function showMenuCategory(categoryId) {
            // Hide all categories
            document.querySelectorAll('.menu-category-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Show selected category
            document.getElementById('menu-category-' + categoryId).classList.remove('hidden');

            // Update tab styles
            document.querySelectorAll('.menu-category-tab').forEach(tab => {
                tab.classList.remove('border-blue-500', 'text-blue-600');
                tab.classList.add('border-transparent', 'text-gray-500');
            });

            document.querySelector('[data-category="' + categoryId + '"]').classList.remove('border-transparent', 'text-gray-500');
            document.querySelector('[data-category="' + categoryId + '"]').classList.add('border-blue-500', 'text-blue-600');
        }

        // Add item directly to cart (menu view)
        function addItemDirectlyToCart(itemId, itemName, itemPrice) {
            // Find existing item in cart
            const existingItemIndex = cart.findIndex(item =>
                item.id === itemId && !item.notes
            );

            if (existingItemIndex !== -1) {
                // Update quantity of existing item
                cart[existingItemIndex].quantity += 1;
            } else {
                // Add new item to cart
                cart.push({
                    id: itemId,
                    name: itemName,
                    price: itemPrice,
                    quantity: 1,
                    notes: ''
                });
            }

            updateCartDisplay();

            // Visual feedback
            showAddToCartFeedback();
        }

        // Show visual feedback when item is added to cart
        function showAddToCartFeedback() {
            // Create a temporary notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50 transition-opacity duration-300';
            notification.textContent = 'Item added to cart!';
            document.body.appendChild(notification);

            // Remove notification after 2 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 2000);
        }



        // Update cart display
        function updateCartDisplay() {
            const cartItemsContainer = document.getElementById('cartItems');
            const subtotalElement = document.getElementById('cartSubtotal');
            const taxElement = document.getElementById('cartTax');
            const serviceChargeElement = document.getElementById('cartServiceCharge');
            const totalElement = document.getElementById('cartTotal');
            const placeOrderBtn = document.getElementById('placeOrderBtn');

            if (cart.length === 0) {
                cartItemsContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No items in cart</p>';
                subtotalElement.textContent = currencySymbol + '0.00';
                taxElement.textContent = currencySymbol + '0.00';
                serviceChargeElement.textContent = currencySymbol + '0.00';
                totalElement.textContent = currencySymbol + '0.00';

                // Hide service charge row when cart is empty
                const serviceChargeRow = document.getElementById('serviceChargeRow');
                serviceChargeRow.classList.add('hidden');

                placeOrderBtn.disabled = true;
                return;
            }

            // Generate cart items HTML
            let cartHTML = '';
            let subtotal = 0;

            cart.forEach((item, index) => {
                const itemTotal = item.price * item.quantity;
                subtotal += itemTotal;

                cartHTML += `
                    <div class="flex justify-between items-center border-b pb-2">
                        <div class="flex-1">
                            <h5 class="font-medium text-sm">${item.name}</h5>
                            ${item.notes ? `<p class="text-xs text-gray-500">${item.notes}</p>` : ''}
                            <p class="text-sm text-gray-600">${currencySymbol}${item.price.toFixed(2)} each</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="updateCartQuantity(${index}, ${item.quantity - 1})"
                                    class="bg-gray-200 hover:bg-gray-300 text-gray-700 w-6 h-6 rounded text-xs">-</button>
                            <span class="font-medium">${item.quantity}</span>
                            <button onclick="updateCartQuantity(${index}, ${item.quantity + 1})"
                                    class="bg-gray-200 hover:bg-gray-300 text-gray-700 w-6 h-6 rounded text-xs">+</button>
                            <button onclick="removeFromCart(${index})"
                                    class="bg-red-500 hover:bg-red-600 text-white w-6 h-6 rounded text-xs">×</button>
                        </div>
                    </div>
                `;
            });

            cartItemsContainer.innerHTML = cartHTML;

            // Recalculate client-side discount amounts when cart changes
            recalculateClientDiscounts(subtotal);

            // Calculate totals with cumulative discounts
            const totalDiscountAmount = orderDiscounts.reduce((sum, discount) => sum + parseFloat(discount.amount), 0);
            const discountedSubtotal = Math.max(0, subtotal - totalDiscountAmount);
            const taxRate = <?php echo e($orderSettings['tax_rate']); ?>;
            const taxChargingMethod = '<?php echo e($orderSettings['tax_charging_method']); ?>';
            let tax = 0;
            
            if (<?php echo e($orderSettings['auto_apply_tax'] ? 'true' : 'false'); ?>) {
                if (taxChargingMethod === 'customer_pays') {
                    tax = discountedSubtotal * taxRate;
                } else {
                    // Business absorbs tax - no additional tax added to customer
                    tax = 0;
                }
            }

            // Calculate service charge
            let serviceCharge = 0;
            if (autoApplyServiceCharge && serviceChargeRate > 0) {
                serviceCharge = discountedSubtotal * serviceChargeRate;
            }

            const total = discountedSubtotal + tax + serviceCharge;

            subtotalElement.textContent = currencySymbol + subtotal.toFixed(2);

            // Show/hide discount row
            const discountRow = document.getElementById('discountRow');
            const discountElement = document.getElementById('cartDiscount');
            if (totalDiscountAmount > 0) {
                discountRow.classList.remove('hidden');
                discountElement.textContent = '-' + currencySymbol + totalDiscountAmount.toFixed(2);
            } else {
                discountRow.classList.add('hidden');
            }

            taxElement.textContent = currencySymbol + tax.toFixed(2);

            // Show/hide service charge row
            const serviceChargeRow = document.getElementById('serviceChargeRow');
            if (serviceCharge > 0) {
                serviceChargeRow.classList.remove('hidden');
                serviceChargeElement.textContent = currencySymbol + serviceCharge.toFixed(2);
            } else {
                serviceChargeRow.classList.add('hidden');
            }

            totalElement.textContent = currencySymbol + total.toFixed(2);

            // Update place order button based on comprehensive validation
            updatePlaceOrderButton();

            // Update button text based on whether we're updating an existing order
            if (currentOrderId) {
                placeOrderBtn.textContent = 'Update Order';
                placeOrderBtn.className = 'w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-bold py-3 px-4 rounded';
            } else {
                placeOrderBtn.textContent = 'Place Order';
                placeOrderBtn.className = 'w-full bg-green-500 hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-bold py-3 px-4 rounded';
            }
        }

        // Update cart item quantity
        function updateCartQuantity(index, newQuantity) {
            if (newQuantity < 1) {
                removeFromCart(index);
                return;
            }

            cart[index].quantity = newQuantity;
            updateCartDisplay();
        }

        // Remove item from cart
        function removeFromCart(index) {
            cart.splice(index, 1);
            updateCartDisplay();
        }

        // Clear entire cart
        function clearCart() {
            if (cart.length > 0 && confirm('Clear all items from cart?')) {
                cart = [];
                orderDiscounts = [];
                updateCartDisplay();
                displayAppliedDiscounts();
            }
        }

        // Discount Functions
        function toggleDiscountSection() {
            const section = document.getElementById('discountSection');
            const icon = document.getElementById('discountToggleIcon');

            if (section.classList.contains('hidden')) {
                section.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                section.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }

        function handleDiscountTypeChange() {
            const discountType = document.getElementById('discountType').value;

            // Hide all discount sections
            document.getElementById('percentageDiscountSection').classList.add('hidden');
            document.getElementById('fixedDiscountSection').classList.add('hidden');
            document.getElementById('couponDiscountSection').classList.add('hidden');
            document.getElementById('pointsDiscountSection').classList.add('hidden');

            // Show relevant section
            if (discountType === 'percentage') {
                document.getElementById('percentageDiscountSection').classList.remove('hidden');
            } else if (discountType === 'fixed') {
                document.getElementById('fixedDiscountSection').classList.remove('hidden');
            } else if (discountType === 'coupon') {
                document.getElementById('couponDiscountSection').classList.remove('hidden');
            } else if (discountType === 'points') {
                document.getElementById('pointsDiscountSection').classList.remove('hidden');
                updateCustomerPoints();
            }

            // Reset discount if changing type
            if (discountType === 'none') {
                removeDiscount();
            }
        }

        // This function is now handled by the cumulative discount system
        // See the new applyPercentageDiscount function at the end of the script

        // This function is now handled by the cumulative discount system
        // See the new applyFixedDiscount function at the end of the script

        // This function is now handled by the cumulative discount system
        // See the new applyCouponDiscount function at the end of the script

        // This function is now handled by the cumulative discount system
        // See the new applyPointsDiscount function at the end of the script

        // These functions are now handled by the cumulative discount system
        // See displayAppliedDiscounts and removeDiscountFromOrder functions at the end of the script

        function resetDiscountUI() {
            document.getElementById('discountType').value = 'none';
            document.getElementById('percentageDiscountSection').classList.add('hidden');
            document.getElementById('fixedDiscountSection').classList.add('hidden');
            document.getElementById('couponDiscountSection').classList.add('hidden');
            document.getElementById('pointsDiscountSection').classList.add('hidden');
            document.getElementById('appliedDiscountSection').classList.add('hidden');

            // Clear input fields
            document.getElementById('discountPercentage').value = '';
            document.getElementById('discountAmount').value = '';
            document.getElementById('couponCode').value = '';
            document.getElementById('pointsToUse').value = '';
        }

        function updateCustomerPoints() {
            const customerSelect = document.getElementById('menuCustomerSelect');
            const customerId = customerSelect.value;

            if (!customerId) {
                document.getElementById('customerPoints').textContent = '0';
                return;
            }

            // Fetch customer points
            fetch(`/pos/customer-points/${customerId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('customerPoints').textContent = data.points;
                    document.getElementById('pointsToUse').max = data.points;
                }
            })
            .catch(error => {
                console.error('Error fetching customer points:', error);
                document.getElementById('customerPoints').textContent = '0';
            });
        }



        // Update place order button when table selection changes
        document.getElementById('selectedTable').addEventListener('change', function() {
            const selectedTableId = this.value;

            if (selectedTableId) {
                // Check if this table has an existing order
                fetch(`/pos/table-order/${selectedTableId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.order) {
                            // Only load existing order if cart is empty or user confirms
                            if (cart.length === 0 || confirm('This table has an existing order. Do you want to load it? (This will replace your current cart)')) {
                                // Load existing order items into cart
                                cart = data.order.items.map(item => ({
                                    id: item.id,
                                    name: item.name,
                                    price: item.price,
                                    quantity: item.quantity,
                                    notes: item.notes,
                                    orderItemId: item.order_item_id // Track existing order item ID
                                }));

                                // Set current order ID for updates
                                currentOrderId = data.order.id;

                                // Load discounts for this order
                                loadOrderDiscounts();

                                // Update customer selection if needed
                                const customerSelect = document.getElementById('menuCustomerSelect');
                                if (customerSelect && data.order.customer_id) {
                                    customerSelect.value = data.order.customer_id;
                                }

                                // Update order type if needed
                                const orderTypeSelect = document.getElementById('menuOrderType');
                                if (orderTypeSelect && data.order.order_type) {
                                    orderTypeSelect.value = data.order.order_type;
                                }
                            } else {
                                // User chose not to load existing order, just set current order ID for updates
                                currentOrderId = data.order.id;
                            }
                        } else {
                            // No existing order, just clear current order ID
                            currentOrderId = null;
                        }

                        updateCartDisplay();
                    })
                    .catch(error => {
                        console.error('Error fetching table order:', error);
                        // On error, just clear current order ID
                        currentOrderId = null;
                    });
            } else {
                // No table selected, clear current order ID
                currentOrderId = null;
            }
        });

        // Update customer points when customer selection changes
        document.getElementById('menuCustomerSelect').addEventListener('change', function() {
            updateCustomerPoints();
        });

        // Show/hide party size and table fields based on order type
        document.getElementById('menuOrderType').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const requiresTable = selectedOption.dataset.requiresTable === 'true';
            const requiresPartySize = selectedOption.dataset.requiresPartySize === 'true';

            // Save the selected order type
            saveLastOrderType(this.value);
            
            const partySizeContainer = document.getElementById('partySizeContainer');
            const tableContainer = document.getElementById('selectedTable').closest('.mb-4');

            // Show/hide party size based on order type requirements
            if (requiresPartySize) {
                partySizeContainer.style.display = 'block';
            } else {
                partySizeContainer.style.display = 'none';
            }

            // Show/hide table selection based on order type requirements and outlet needs
            if (needsTables && requiresTable) {
                if (tableContainer) {
                    tableContainer.style.display = 'block';
                }
            } else {
                if (tableContainer) {
                    tableContainer.style.display = 'none';
                }
            }

            // Update place order button validation
            updatePlaceOrderButton();
        });

        // Table selection for table view - simplified workflow
        function selectTableForOrder(tableId, orderType) {
            // Redirect directly to menu view with table pre-selected
            window.location.href = `<?php echo e(route('pos.index')); ?>?table=${tableId}&view=menu`;
        }



        // Place menu order with selected customer
        function placeMenuOrder() {
            // Clear any previous validation errors
            clearValidationErrors();

            const selectedTable = document.getElementById('selectedTable').value;
            const customerSelect = document.getElementById('menuCustomerSelect');
            const orderTypeSelect = document.getElementById('menuOrderType');
            const partySizeInput = document.getElementById('menuPartySize');
            const customerId = customerSelect.value;
            const orderType = orderTypeSelect.value;
            const partySize = parseInt(partySizeInput.value) || 2;

            // Comprehensive validation
            const validationErrors = validateOrderForm(selectedTable, customerId, orderType, partySize, cart);

            if (validationErrors.length > 0) {
                displayValidationErrors(validationErrors);
                return;
            }

            // Prepare order data
            const orderData = {
                table_id: selectedTable,
                customer_id: customerId,
                order_type: orderType,
                party_size: partySize,
                items: cart.map(item => ({
                    menu_item_id: item.id,
                    quantity: item.quantity,
                    notes: item.notes || null
                }))
            };

            // Determine if we're updating an existing order or creating a new one
            const isUpdatingOrder = currentOrderId !== null;
            const url = isUpdatingOrder
                ? `/pos/update-order/${currentOrderId}`
                : '<?php echo e(route("pos.create-menu-order-with-customer")); ?>';

            // Submit order
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(orderData)
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                // Check if response is actually JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    console.error('Response is not JSON, content-type:', contentType);
                    return response.text().then(text => {
                        console.error('Response body:', text);
                        throw new Error('Server returned HTML instead of JSON. Check server logs for errors.');
                    });
                }

                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Set the order ID for potential discount transfer
                    const newOrderId = data.order_id;

                    // Transfer client-side discounts to server if any exist
                    const clientDiscounts = orderDiscounts.filter(d => d.is_client_side);

                    if (clientDiscounts.length > 0) {
                        transferClientDiscountsToServer(newOrderId)
                            .then(() => {
                                // Clear cart and show success popup after discount transfer
                                cart = [];
                                currentOrderId = null;
                                orderDiscounts = [];
                                updateCartDisplay();

                                // Show success popup with appropriate message
                                const title = isUpdatingOrder ? 'Order Updated Successfully!' : 'Order Placed Successfully!';
                                const message = isUpdatingOrder
                                    ? `Order #${newOrderId} has been updated successfully.`
                                    : `Order #${newOrderId} has been placed for ${data.table_name}.`;

                                showSuccessPopup(title, message);

                                // Save the current order type for next time
                                const currentOrderType = document.getElementById('menuOrderType').value;
                                if (currentOrderType) {
                                    saveLastOrderType(currentOrderType);
                                }

                                // Reset table selection to allow creating new orders
                                document.getElementById('selectedTable').value = '';
                                // Reset to Guest Customer (first option)
                                const customerSelect = document.getElementById('menuCustomerSelect');
                                if (customerSelect.options.length > 0) {
                                    customerSelect.selectedIndex = 0;
                                }
                                // Keep the last selected order type (don't reset)
                                document.getElementById('menuPartySize').value = '2';
                            })
                            .catch(error => {
                                console.error('Error transferring discounts:', error);
                                // Still show success but warn about discount issue
                                alert('Order created successfully, but there was an issue applying discounts. Please check the order.');
                            });
                    } else {
                        // No client-side discounts to transfer
                        cart = [];
                        currentOrderId = null;
                        orderDiscounts = [];
                        updateCartDisplay();

                        // Show success popup with appropriate message
                        const title = isUpdatingOrder ? 'Order Updated Successfully!' : 'Order Placed Successfully!';
                        const message = isUpdatingOrder
                            ? `Order #${newOrderId} has been updated successfully.`
                            : `Order #${newOrderId} has been placed for ${data.table_name}.`;

                        showSuccessPopup(title, message);

                        // Save the current order type for next time
                        const currentOrderType = document.getElementById('menuOrderType').value;
                        if (currentOrderType) {
                            saveLastOrderType(currentOrderType);
                        }

                        // Reset table selection to allow creating new orders
                        document.getElementById('selectedTable').value = '';
                        // Reset to Guest Customer (first option)
                        const customerSelect = document.getElementById('menuCustomerSelect');
                        if (customerSelect.options.length > 0) {
                            customerSelect.selectedIndex = 0;
                        }
                        // Keep the last selected order type (don't reset)
                        document.getElementById('menuPartySize').value = '2';
                    }
                } else {
                    // Handle server-side validation errors
                    if (data.errors) {
                        displayServerValidationErrors(data.errors);
                    } else {
                        alert(data.message || (isUpdatingOrder ? 'Error updating order' : 'Error creating order'));
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);

                // Try to parse JSON error response
                if (error.response) {
                    error.response.json().then(errorData => {
                        if (errorData.errors) {
                            displayServerValidationErrors(errorData.errors);
                        } else {
                            alert(errorData.message || (isUpdatingOrder ? 'Error updating order' : 'Error creating order'));
                        }
                    }).catch(() => {
                        alert((isUpdatingOrder ? 'Error updating order: ' : 'Error creating order: ') + error.message);
                    });
                } else {
                    alert((isUpdatingOrder ? 'Error updating order: ' : 'Error creating order: ') + error.message);
                }
            });
        }

        // Success popup functions
        let successPopupTimeout;
        let successPopupInterval;

        function showSuccessPopup(title, message, autoCloseDelay = 4000) {
            document.getElementById('successPopupTitle').textContent = title;
            document.getElementById('successPopupMessage').textContent = message;
            document.getElementById('successPopup').classList.remove('hidden');
            document.getElementById('successPopup').classList.add('flex');

            // Clear any existing timeouts/intervals
            if (successPopupTimeout) {
                clearTimeout(successPopupTimeout);
            }
            if (successPopupInterval) {
                clearInterval(successPopupInterval);
            }

            // Initialize countdown and progress bar
            const countdownElement = document.getElementById('successPopupCountdown');
            const progressElement = document.getElementById('successPopupProgress');
            let remainingTime = autoCloseDelay / 1000; // Convert to seconds

            countdownElement.textContent = Math.ceil(remainingTime);
            progressElement.style.width = '100%';

            // Update countdown and progress bar every 100ms
            const updateInterval = 100;
            successPopupInterval = setInterval(() => {
                remainingTime -= updateInterval / 1000;

                if (remainingTime <= 0) {
                    clearInterval(successPopupInterval);
                    closeSuccessPopup();
                    return;
                }

                // Update countdown (only show whole seconds)
                countdownElement.textContent = Math.ceil(remainingTime);

                // Update progress bar
                const progressPercentage = (remainingTime / (autoCloseDelay / 1000)) * 100;
                progressElement.style.width = progressPercentage + '%';
            }, updateInterval);

            // Backup timeout in case interval fails
            successPopupTimeout = setTimeout(() => {
                closeSuccessPopup();
            }, autoCloseDelay);
        }

        function closeSuccessPopup() {
            // Clear the auto-close timeout and interval
            if (successPopupTimeout) {
                clearTimeout(successPopupTimeout);
                successPopupTimeout = null;
            }
            if (successPopupInterval) {
                clearInterval(successPopupInterval);
                successPopupInterval = null;
            }

            document.getElementById('successPopup').classList.add('hidden');
            document.getElementById('successPopup').classList.remove('flex');
        }

        // Close popup when clicking the close button
        document.getElementById('successPopupClose').addEventListener('click', closeSuccessPopup);

        // Close popup when clicking outside the modal
        document.getElementById('successPopup').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSuccessPopup();
            }
        });

        // Add customer modal functions
        function showAddCustomerModal() {
            // Reset form
            document.getElementById('addCustomerForm').reset();

            document.getElementById('addCustomerModal').classList.remove('hidden');
            document.getElementById('addCustomerModal').classList.add('flex');
        }

        function closeAddCustomerModal() {
            document.getElementById('addCustomerModal').classList.add('hidden');
            document.getElementById('addCustomerModal').classList.remove('flex');
        }

        // Handle add customer form submission
        document.getElementById('addCustomerForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            // Create customer via API
            fetch('<?php echo e(route("pos.add-customer")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Add customer to both dropdowns
                    const option1 = new Option(data.customer.name + ' - ' + data.customer.phone, data.customer.id);
                    const option2 = new Option(data.customer.name + ' - ' + data.customer.phone, data.customer.id);

                    document.getElementById('tableCustomerSelect').add(option1);
                    document.getElementById('menuCustomerSelect').add(option2);

                    // Select the new customer in both dropdowns
                    document.getElementById('tableCustomerSelect').value = data.customer.id;
                    document.getElementById('menuCustomerSelect').value = data.customer.id;

                    closeAddCustomerModal();

                    // Show success message
                    alert('Customer added successfully!');
                } else {
                    alert(data.message || 'Error adding customer');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error adding customer');
            });
        });

        // Cumulative Discount Functions
        let orderDiscounts = [];

        function addDiscountToOrder(type, value, couponCode = null, pointsUsed = null, description = null) {
            const selectedTableId = document.getElementById('selectedTable').value;

            if (!selectedTableId) {
                alert('Please select a table first.');
                return;
            }

            // Check if cart has items
            if (cart.length === 0) {
                alert('Please add items to cart before applying discounts.');
                return;
            }

            // If order exists, use server-side discount management
            if (currentOrderId) {
                addServerDiscount(type, value, couponCode, pointsUsed, description);
                return;
            }

            // Otherwise, use client-side discount management for cart
            addClientDiscount(type, value, couponCode, pointsUsed, description);
        }

        function addServerDiscount(type, value, couponCode = null, pointsUsed = null, description = null) {
            const discountData = {
                type: type,
                value: value,
                coupon_code: couponCode,
                points_used: pointsUsed,
                description: description
            };

            fetch(`/pos/order/${currentOrderId}/add-discount`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(discountData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadOrderDiscounts();
                    updateCartDisplay();
                    clearDiscountInputs();
                } else {
                    alert('Error applying discount: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error applying discount');
            });
        }

        function addClientDiscount(type, value, couponCode = null, pointsUsed = null, description = null) {
            // Calculate discount amount based on current cart subtotal
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            let discountAmount = 0;

            switch (type) {
                case 'percentage':
                    discountAmount = subtotal * (value / 100);
                    break;
                case 'fixed':
                    discountAmount = Math.min(value, subtotal); // Don't exceed subtotal
                    break;
                case 'points':
                    discountAmount = Math.min(value, subtotal); // Value is already the dollar amount
                    break;
                case 'coupon':
                    discountAmount = Math.min(value, subtotal);
                    break;
            }

            // Create client-side discount object
            const discount = {
                id: Date.now(), // Temporary ID for client-side management
                type: type,
                value: value,
                amount: discountAmount,
                coupon_code: couponCode,
                points_used: pointsUsed,
                description: description,
                formatted_description: description,
                is_client_side: true
            };

            // Add to local discounts array
            orderDiscounts.push(discount);

            // Update display
            displayAppliedDiscounts();
            updateCartDisplay();
            clearDiscountInputs();
        }

        function removeDiscountFromOrder(discountId) {
            // Find the discount to determine if it's client-side or server-side
            const discount = orderDiscounts.find(d => d.id == discountId);

            if (!discount) {
                return;
            }

            // If it's a client-side discount or no order exists yet
            if (discount.is_client_side || !currentOrderId) {
                removeClientDiscount(discountId);
                return;
            }

            // Otherwise, remove server-side discount
            removeServerDiscount(discountId);
        }

        function removeServerDiscount(discountId) {
            fetch(`/pos/order/${currentOrderId}/remove-discount`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ discount_id: discountId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadOrderDiscounts();
                    updateCartDisplay();
                } else {
                    alert('Error removing discount: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error removing discount');
            });
        }

        function removeClientDiscount(discountId) {
            // Remove from local array
            orderDiscounts = orderDiscounts.filter(discount => discount.id != discountId);

            // Update display
            displayAppliedDiscounts();
            updateCartDisplay();
        }

        function loadOrderDiscounts() {
            if (!currentOrderId) {
                // No order exists yet, just display current client-side discounts
                displayAppliedDiscounts();
                return;
            }

            fetch(`/pos/order/${currentOrderId}/discounts`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Replace with server-side discounts
                    orderDiscounts = data.discounts;
                    displayAppliedDiscounts();
                }
            })
            .catch(error => {
                console.error('Error loading discounts:', error);
            });
        }

        function displayAppliedDiscounts() {
            const section = document.getElementById('appliedDiscountsSection');
            const list = document.getElementById('appliedDiscountsList');

            if (orderDiscounts.length === 0) {
                section.classList.add('hidden');
                return;
            }

            section.classList.remove('hidden');
            list.innerHTML = '';

            orderDiscounts.forEach(discount => {
                const discountElement = document.createElement('div');
                discountElement.className = 'bg-green-50 border border-green-200 rounded p-2 flex items-center justify-between';
                discountElement.innerHTML = `
                    <div class="text-xs">
                        <span class="font-medium text-green-800">${discount.formatted_description}</span>
                        <span class="text-green-600">(-${currencySymbol}${parseFloat(discount.amount).toFixed(2)})</span>
                    </div>
                    <button onclick="removeDiscountFromOrder(${discount.id})"
                            class="text-red-600 hover:text-red-800 text-xs">
                        Remove
                    </button>
                `;
                list.appendChild(discountElement);
            });
        }

        function clearDiscountInputs() {
            document.getElementById('discountPercentage').value = '';
            document.getElementById('discountAmount').value = '';
            document.getElementById('couponCode').value = '';
            document.getElementById('pointsToUse').value = '';
        }

        // Update existing discount functions to use cumulative system
        function applyPercentageDiscount() {
            const percentage = parseFloat(document.getElementById('discountPercentage').value);

            if (!percentage || percentage < 0 || percentage > 100) {
                alert('Please enter a valid percentage between 0 and 100.');
                return;
            }

            addDiscountToOrder('percentage', percentage, null, null, `${percentage}% Discount`);
        }

        function applyFixedDiscount() {
            const amount = parseFloat(document.getElementById('discountAmount').value);

            if (!amount || amount < 0) {
                alert('Please enter a valid discount amount.');
                return;
            }

            addDiscountToOrder('fixed', amount, null, null, `${currencySymbol}${amount.toFixed(2)} Off`);
        }

        function applyCouponDiscount() {
            const couponCode = document.getElementById('couponCode').value.trim();

            if (!couponCode) {
                alert('Please enter a coupon code.');
                return;
            }

            // For now, apply a fixed 5.00 discount for any coupon
            // In a real system, you'd validate the coupon and get its value
            addDiscountToOrder('coupon', 5.00, couponCode, null, `Coupon: ${couponCode}`);
        }

        function applyPointsDiscount() {
            const pointsToUse = parseInt(document.getElementById('pointsToUse').value);
            const availablePoints = parseInt(document.getElementById('customerPoints').textContent) || 0;

            if (!pointsToUse || pointsToUse <= 0) {
                alert('Please enter a valid number of points.');
                return;
            }

            if (pointsToUse > availablePoints) {
                alert('Not enough points available.');
                return;
            }

            const discountAmount = pointsToUse / 100; // 100 points = 1.00 currency unit
            addDiscountToOrder('points', discountAmount, null, pointsToUse, `${pointsToUse} Points Used`);
        }

        // Comprehensive Order Validation Functions
        function validateOrderForm(selectedTable, customerId, orderType, partySize, cart) {
            const errors = [];

            // Get order type requirements from the selected option
            const orderTypeSelect = document.getElementById('menuOrderType');
            const selectedOption = orderTypeSelect.options[orderTypeSelect.selectedIndex];
            const requiresTable = selectedOption ? selectedOption.dataset.requiresTable === 'true' : false;
            const requiresPartySize = selectedOption ? selectedOption.dataset.requiresPartySize === 'true' : false;

            // Validate table selection (only required if order type requires table and outlet needs tables)
            if (needsTables && requiresTable && (!selectedTable || selectedTable.trim() === '')) {
                errors.push({
                    field: 'selectedTable',
                    message: 'Please select a table for this order type.'
                });
            }

            // Validate customer selection
            if (!customerId || customerId.trim() === '') {
                errors.push({
                    field: 'menuCustomerSelect',
                    message: 'Please select a customer.'
                });
            }

            // Validate order type
            if (!orderType || orderType.trim() === '') {
                errors.push({
                    field: 'menuOrderType',
                    message: 'Please select an order type.'
                });
            }

            // Validate party size (only if order type requires it)
            if (requiresPartySize && (!partySize || isNaN(partySize) || partySize < 1 || partySize > 20)) {
                errors.push({
                    field: 'menuPartySize',
                    message: 'Party size must be between 1 and 20 guests for this order type.'
                });
            }

            // Validate cart items
            if (!cart || cart.length === 0) {
                errors.push({
                    field: 'cart',
                    message: 'Please add at least one item to the cart.'
                });
            }

            return errors;
        }

        function displayValidationErrors(errors) {
            // Remove any existing error displays
            clearValidationErrors();

            let errorMessage = 'Please fix the following issues:\n\n';

            errors.forEach((error, index) => {
                // Add error message to alert
                errorMessage += `${index + 1}. ${error.message}\n`;

                // Highlight the field with error
                highlightFieldError(error.field);
            });

            // Show error alert
            alert(errorMessage);

            // Focus on first error field
            if (errors.length > 0) {
                focusErrorField(errors[0].field);
            }
        }

        function displayServerValidationErrors(serverErrors) {
            // Remove any existing error displays
            clearValidationErrors();

            let errorMessage = 'Please fix the following issues:\n\n';
            let errorCount = 1;

            // Convert server errors to client format
            const clientErrors = [];

            for (const [field, messages] of Object.entries(serverErrors)) {
                messages.forEach(message => {
                    errorMessage += `${errorCount}. ${message}\n`;
                    errorCount++;

                    // Map server field names to client field IDs
                    let clientField = field;
                    switch (field) {
                        case 'table_id':
                            clientField = 'selectedTable';
                            break;
                        case 'customer_id':
                        case 'customer':
                            clientField = 'menuCustomerSelect';
                            break;
                        case 'order_type':
                            clientField = 'menuOrderType';
                            break;
                        case 'party_size':
                            clientField = 'menuPartySize';
                            break;
                        case 'items':
                            clientField = 'cart';
                            break;
                    }

                    clientErrors.push({ field: clientField, message: message });
                });
            }

            // Show error alert
            alert(errorMessage);

            // Highlight fields with errors
            clientErrors.forEach(error => {
                highlightFieldError(error.field);
            });

            // Focus on first error field
            if (clientErrors.length > 0) {
                focusErrorField(clientErrors[0].field);
            }
        }

        function highlightFieldError(fieldId) {
            const field = document.getElementById(fieldId);
            if (field) {
                field.classList.add('border-red-500', 'bg-red-50');
                field.classList.remove('border-gray-300');

                // Add error styling to label if exists
                const label = document.querySelector(`label[for="${fieldId}"]`);
                if (label) {
                    label.classList.add('text-red-700', 'font-semibold');
                }
            }
        }

        function clearValidationErrors() {
            // Remove error styling from all form fields
            const fields = ['selectedTable', 'menuCustomerSelect', 'menuOrderType', 'menuPartySize'];

            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.classList.remove('border-red-500', 'bg-red-50');
                    field.classList.add('border-gray-300');

                    // Remove error styling from label
                    const label = document.querySelector(`label[for="${fieldId}"]`);
                    if (label) {
                        label.classList.remove('text-red-700', 'font-semibold');
                    }
                }
            });

            // Clear cart error styling
            const cartSection = document.getElementById('cartItems');
            if (cartSection) {
                cartSection.classList.remove('border-red-500', 'bg-red-50');
            }
        }

        function focusErrorField(fieldId) {
            const field = document.getElementById(fieldId);
            if (field && field.focus) {
                field.focus();

                // Scroll field into view
                field.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }
        }

        // Real-time validation as user fills form
        function setupRealTimeValidation() {
            const fields = [
                { id: 'selectedTable', validator: (value) => value && value.trim() !== '' },
                { id: 'menuCustomerSelect', validator: (value) => value && value.trim() !== '' },
                { id: 'menuOrderType', validator: (value) => value && value.trim() !== '' },
                { id: 'menuPartySize', validator: (value) => {
                    const num = parseInt(value);
                    return !isNaN(num) && num >= 1 && num <= 20;
                }}
            ];

            fields.forEach(fieldConfig => {
                const field = document.getElementById(fieldConfig.id);
                if (field) {
                    field.addEventListener('change', function() {
                        validateSingleField(fieldConfig.id, fieldConfig.validator);
                        updatePlaceOrderButton();
                    });

                    field.addEventListener('input', function() {
                        validateSingleField(fieldConfig.id, fieldConfig.validator);
                        updatePlaceOrderButton();
                    });
                }
            });
        }

        function validateSingleField(fieldId, validator) {
            const field = document.getElementById(fieldId);
            if (!field) return;

            const isValid = validator(field.value);

            if (isValid) {
                // Remove error styling
                field.classList.remove('border-red-500', 'bg-red-50');
                field.classList.add('border-gray-300');

                const label = document.querySelector(`label[for="${fieldId}"]`);
                if (label) {
                    label.classList.remove('text-red-700', 'font-semibold');
                }
            } else {
                // Add error styling
                field.classList.add('border-red-500', 'bg-red-50');
                field.classList.remove('border-gray-300');
            }
        }

        function updatePlaceOrderButton() {
            const selectedTable = document.getElementById('selectedTable').value;
            const customerId = document.getElementById('menuCustomerSelect').value;
            const orderType = document.getElementById('menuOrderType').value;
            const partySize = parseInt(document.getElementById('menuPartySize').value) || 0;

            // Get order type requirements from the selected option
            const orderTypeSelect = document.getElementById('menuOrderType');
            const selectedOption = orderTypeSelect.options[orderTypeSelect.selectedIndex];
            const requiresTable = selectedOption ? selectedOption.dataset.requiresTable === 'true' : false;
            const requiresPartySize = selectedOption ? selectedOption.dataset.requiresPartySize === 'true' : false;

            // Table is only required if order type requires table and outlet needs tables
            const tableValid = (needsTables && requiresTable) ? (selectedTable && selectedTable.trim() !== '') : true;

            // Party size is only required if order type requires it
            const partySizeValid = requiresPartySize ? (partySize >= 1 && partySize <= 20) : true;

            const isFormValid = tableValid &&
                               customerId &&
                               orderType &&
                               partySizeValid &&
                               cart.length > 0;

            const placeOrderBtn = document.getElementById('placeOrderBtn');
            if (placeOrderBtn) {
                placeOrderBtn.disabled = !isFormValid;

                if (isFormValid) {
                    placeOrderBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                    placeOrderBtn.classList.add('hover:bg-green-600');
                } else {
                    placeOrderBtn.classList.add('opacity-50', 'cursor-not-allowed');
                    placeOrderBtn.classList.remove('hover:bg-green-600');
                }
            }

            // Update validation status indicator
            updateValidationStatus(isFormValid, selectedTable, customerId, orderType, partySize, cart.length, requiresTable, requiresPartySize);
        }

        function updateValidationStatus(isFormValid, selectedTable, customerId, orderType, partySize, cartLength, requiresTable, requiresPartySize) {
            const statusDiv = document.getElementById('validationStatus');
            const statusIcon = document.getElementById('validationIcon');
            const statusMessage = document.getElementById('validationMessage');

            if (!statusDiv || !statusIcon || !statusMessage) return;

            if (isFormValid) {
                // All fields are valid - show success status
                statusDiv.className = 'mb-3 p-2 rounded-md border border-green-200 bg-green-50 text-green-800';
                statusIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>';
                statusMessage.textContent = 'All required fields completed. Ready to place order!';
                statusDiv.classList.remove('hidden');
            } else {
                // Some fields are missing - show warning status
                const missingFields = [];
                if (needsTables && requiresTable && !selectedTable) missingFields.push('Table');
                if (!customerId) missingFields.push('Customer');
                if (!orderType) missingFields.push('Order Type');
                if (requiresPartySize && (partySize < 1 || partySize > 20)) missingFields.push('Party Size');
                if (cartLength === 0) missingFields.push('Items');

                if (missingFields.length > 0) {
                    statusDiv.className = 'mb-3 p-2 rounded-md border border-yellow-200 bg-yellow-50 text-yellow-800';
                    statusIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>';
                    statusMessage.textContent = `Required: ${missingFields.join(', ')}`;
                    statusDiv.classList.remove('hidden');
                } else {
                    statusDiv.classList.add('hidden');
                }
            }
        }

        // Initialize real-time validation when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setupRealTimeValidation();
            updatePlaceOrderButton();
        });

        function recalculateClientDiscounts(currentSubtotal) {
            // Only recalculate client-side discounts
            orderDiscounts.forEach(discount => {
                if (discount.is_client_side) {
                    let newAmount = 0;

                    switch (discount.type) {
                        case 'percentage':
                            newAmount = currentSubtotal * (discount.value / 100);
                            break;
                        case 'fixed':
                            newAmount = Math.min(discount.value, currentSubtotal);
                            break;
                        case 'points':
                            newAmount = Math.min(discount.value, currentSubtotal);
                            break;
                        case 'coupon':
                            newAmount = Math.min(discount.value, currentSubtotal);
                            break;
                    }

                    discount.amount = newAmount;
                }
            });

            // Update applied discounts display
            displayAppliedDiscounts();
        }

        function transferClientDiscountsToServer(orderId) {
            // Transfer all client-side discounts to the server
            const clientDiscounts = orderDiscounts.filter(d => d.is_client_side);

            if (clientDiscounts.length === 0) {
                return Promise.resolve();
            }

            const transferPromises = clientDiscounts.map(discount => {
                return fetch(`/pos/order/${orderId}/add-discount`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        type: discount.type,
                        value: discount.value,
                        coupon_code: discount.coupon_code,
                        points_used: discount.points_used,
                        description: discount.description
                    })
                });
            });

            return Promise.all(transferPromises)
                .then(() => {
                    // Clear client-side discounts after successful transfer
                    orderDiscounts = orderDiscounts.filter(d => !d.is_client_side);
                    // Load server-side discounts
                    return loadOrderDiscounts();
                })
                .catch(error => {
                    console.error('Error transferring discounts:', error);
                    throw error;
                });
        }

        // Search and Barcode Scanning Functionality
        let isBarcodeScannerActive = false;
        let barcodeBuffer = '';
        let barcodeTimeout = null;
        let allMenuItems = [];

        // Initialize search functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Collect all menu items for search
            collectAllMenuItems();

            // Setup search input listener
            const searchInput = document.getElementById('menuSearchInput');
            if (searchInput) {
                searchInput.addEventListener('input', handleMenuSearch);
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        handleMenuSearch();
                    }
                });
            }

            // Setup barcode scanning listener
            document.addEventListener('keydown', handleBarcodeInput);
        });

        // Collect all menu items from the DOM for search
        function collectAllMenuItems() {
            allMenuItems = [];
            document.querySelectorAll('.menu-item').forEach(item => {
                const itemData = {
                    id: parseInt(item.dataset.itemId),
                    name: item.dataset.itemName,
                    sku: item.dataset.itemSku,
                    barcode: item.dataset.itemBarcode,
                    categoryId: parseInt(item.dataset.categoryId),
                    element: item,
                    price: parseFloat(item.querySelector('.text-green-600').textContent.replace(/[^\d.-]/g, ''))
                };
                allMenuItems.push(itemData);
            });
        }

        // Handle menu search
        function handleMenuSearch() {
            const searchInput = document.getElementById('menuSearchInput');
            const searchTerm = searchInput.value.trim().toLowerCase();
            const clearBtn = document.getElementById('clearSearchBtn');
            const searchResultsContainer = document.getElementById('searchResultsContainer');
            const menuCategoriesContainer = document.getElementById('menuCategoriesContainer');
            const categoryTabs = document.getElementById('categoryTabs');
            const searchResultsInfo = document.getElementById('searchResultsInfo');
            const searchResultsText = document.getElementById('searchResultsText');

            if (searchTerm === '') {
                // Show categories and hide search results
                clearBtn.classList.add('hidden');
                searchResultsContainer.classList.add('hidden');
                menuCategoriesContainer.classList.remove('hidden');
                categoryTabs.classList.remove('hidden');
                searchResultsInfo.classList.add('hidden');
                return;
            }

            // Show clear button and search results
            clearBtn.classList.remove('hidden');
            searchResultsContainer.classList.remove('hidden');
            menuCategoriesContainer.classList.add('hidden');
            categoryTabs.classList.add('hidden');

            // Filter items based on search term
            const filteredItems = allMenuItems.filter(item => {
                return item.name.includes(searchTerm) ||
                       item.sku.includes(searchTerm) ||
                       item.barcode.includes(searchTerm);
            });

            // Display search results
            displaySearchResults(filteredItems, searchTerm);

            // Update search results info
            searchResultsText.textContent = `Found ${filteredItems.length} item(s) matching "${searchInput.value}"`;
            searchResultsInfo.classList.remove('hidden');
        }

        // Display search results
        function displaySearchResults(items, searchTerm) {
            const searchResultsGrid = document.getElementById('searchResultsGrid');
            searchResultsGrid.innerHTML = '';

            if (items.length === 0) {
                searchResultsGrid.innerHTML = `
                    <div class="col-span-full text-center py-8">
                        <div class="text-gray-400 mb-2">
                            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <p class="text-gray-500">No items found matching "${searchTerm}"</p>
                        <p class="text-sm text-gray-400 mt-1">Try searching by name, SKU, or barcode</p>
                    </div>
                `;
                return;
            }

            items.forEach(item => {
                const itemClone = item.element.cloneNode(true);
                searchResultsGrid.appendChild(itemClone);
            });
        }

        // Clear menu search
        function clearMenuSearch() {
            const searchInput = document.getElementById('menuSearchInput');
            searchInput.value = '';
            handleMenuSearch();
            searchInput.focus();
        }

        // Toggle barcode scanner
        function toggleBarcodeScanner() {
            isBarcodeScannerActive = !isBarcodeScannerActive;
            const toggleBtn = document.getElementById('barcodeScannerToggle');
            const toggleText = document.getElementById('barcodeScannerText');
            const statusDiv = document.getElementById('barcodeScannerStatus');

            if (isBarcodeScannerActive) {
                toggleBtn.classList.remove('bg-purple-500', 'hover:bg-purple-600');
                toggleBtn.classList.add('bg-red-500', 'hover:bg-red-600');
                toggleText.textContent = 'Stop Barcode Scanner';
                statusDiv.classList.remove('hidden');

                // Clear any existing barcode buffer
                barcodeBuffer = '';

                // Focus on a hidden input to capture barcode input
                createBarcodeInput();
            } else {
                toggleBtn.classList.remove('bg-red-500', 'hover:bg-red-600');
                toggleBtn.classList.add('bg-purple-500', 'hover:bg-purple-600');
                toggleText.textContent = 'Start Barcode Scanner';
                statusDiv.classList.add('hidden');

                // Remove barcode input
                removeBarcodeInput();
            }
        }

        // Create hidden input for barcode scanning
        function createBarcodeInput() {
            removeBarcodeInput(); // Remove any existing input

            const barcodeInput = document.createElement('input');
            barcodeInput.id = 'barcodeHiddenInput';
            barcodeInput.type = 'text';
            barcodeInput.style.position = 'absolute';
            barcodeInput.style.left = '-9999px';
            barcodeInput.style.opacity = '0';
            document.body.appendChild(barcodeInput);

            barcodeInput.focus();

            // Refocus when it loses focus (to keep capturing barcode input)
            barcodeInput.addEventListener('blur', function() {
                if (isBarcodeScannerActive) {
                    setTimeout(() => barcodeInput.focus(), 100);
                }
            });
        }

        // Remove barcode input
        function removeBarcodeInput() {
            const existingInput = document.getElementById('barcodeHiddenInput');
            if (existingInput) {
                existingInput.remove();
            }
        }

        // Handle barcode input
        function handleBarcodeInput(event) {
            if (!isBarcodeScannerActive) return;

            // Clear timeout if it exists
            if (barcodeTimeout) {
                clearTimeout(barcodeTimeout);
            }

            // Add character to buffer
            if (event.key.length === 1) {
                barcodeBuffer += event.key;
            } else if (event.key === 'Enter') {
                // Process the barcode
                processBarcodeInput(barcodeBuffer);
                barcodeBuffer = '';
                return;
            }

            // Set timeout to process barcode after 100ms of no input
            barcodeTimeout = setTimeout(() => {
                if (barcodeBuffer.length > 0) {
                    processBarcodeInput(barcodeBuffer);
                    barcodeBuffer = '';
                }
            }, 100);
        }

        // Process barcode input and add item to cart
        function processBarcodeInput(barcode) {
            if (!barcode || barcode.length < 3) return; // Ignore very short inputs

            console.log('Processing barcode:', barcode);

            // Find item by barcode
            const matchedItem = allMenuItems.find(item =>
                item.barcode && item.barcode.toLowerCase() === barcode.toLowerCase()
            );

            if (matchedItem) {
                // Add item to cart
                addItemDirectlyToCart(matchedItem.id, matchedItem.name, matchedItem.price);

                // Show success feedback
                showBarcodeSuccess(matchedItem.name, barcode);
            } else {
                // Show error feedback
                showBarcodeError(barcode);
            }
        }

        // Show barcode success feedback
        function showBarcodeSuccess(itemName, barcode) {
            const statusDiv = document.getElementById('barcodeScannerStatus');
            const originalContent = statusDiv.innerHTML;

            statusDiv.innerHTML = `
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-green-700 font-medium">Added "${itemName}" to cart (Barcode: ${barcode})</span>
                </div>
            `;
            statusDiv.className = 'p-3 bg-green-50 border border-green-200 rounded-lg';

            // Restore original content after 3 seconds
            setTimeout(() => {
                statusDiv.innerHTML = originalContent;
                statusDiv.className = 'p-3 bg-purple-50 border border-purple-200 rounded-lg';
            }, 3000);
        }

        // Show barcode error feedback
        function showBarcodeError(barcode) {
            const statusDiv = document.getElementById('barcodeScannerStatus');
            const originalContent = statusDiv.innerHTML;

            statusDiv.innerHTML = `
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-red-700 font-medium">No item found with barcode: ${barcode}</span>
                </div>
            `;
            statusDiv.className = 'p-3 bg-red-50 border border-red-200 rounded-lg';

            // Restore original content after 3 seconds
            setTimeout(() => {
                statusDiv.innerHTML = originalContent;
                statusDiv.className = 'p-3 bg-purple-50 border border-purple-200 rounded-lg';
            }, 3000);
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\POS\resources\views/pos/index.blade.php ENDPATH**/ ?>