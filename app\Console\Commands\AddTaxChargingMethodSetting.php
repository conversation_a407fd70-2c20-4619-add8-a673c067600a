<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\OrderSetting;
use Illuminate\Support\Facades\DB;

class AddTaxChargingMethodSetting extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:add-tax-charging-method';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add tax charging method setting to order settings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Check if the tax_charging_method setting already exists
        $existingSetting = OrderSetting::where('key', 'tax_charging_method')->first();
        
        if ($existingSetting) {
            $this->info('Tax charging method setting already exists.');
            return 0;
        }

        try {
            // Insert the tax charging method setting
            DB::table('order_settings')->insert([
                'key' => 'tax_charging_method',
                'value' => 'customer_pays',
                'type' => 'select',
                'category' => 'tax',
                'label' => 'Tax Charging Method',
                'description' => 'Choose whether customers pay tax separately or business absorbs tax in prices',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            $this->info('Tax charging method setting added successfully!');
            $this->line('You can now configure tax charging method in Order Settings.');
            $this->line('Options:');
            $this->line('  - Customer Pays Tax: Tax is added to the final price');
            $this->line('  - Business Absorbs Tax: Tax is included in the displayed price');
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to add tax charging method setting: ' . $e->getMessage());
            return 1;
        }
    }
}