<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\MenuItem;
use App\Models\MenuCategory;
use App\Services\ProductService;
use App\Services\ProductSyncService;
use Illuminate\Support\Facades\Storage;

class MenuItemController extends Controller
{
    private ProductService $productService;
    private ProductSyncService $productSyncService;

    public function __construct(
        ProductService $productService,
        ProductSyncService $productSyncService
    ) {
        $this->middleware(['auth', 'menu.access']);
        $this->productService = $productService;
        $this->productSyncService = $productSyncService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $menuItems = MenuItem::with('category')->get();
        $syncStatus = $this->productService->getSyncStatus();

        return view('menu-items.index', compact('menuItems', 'syncStatus'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = MenuCategory::all();
        return view('menu-items.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // First, check network connectivity - products can only be created online
        $networkAvailable = $this->productSyncService->isNetworkAvailable();

        if (!$networkAvailable) {
            \Log::warning('Product creation blocked - no network connectivity', [
                'user_id' => auth()->id(),
                'attempted_product' => $request->input('name')
            ]);

            return redirect()->back()
                ->with('error', 'Tidak dapat membuat produk: Koneksi internet diperlukan untuk sinkronisasi dengan API. Pastikan Anda terhubung ke internet dan coba lagi.')
                ->withInput();
        }

        \Log::info('Product creation allowed - network connectivity confirmed', [
            'user_id' => auth()->id(),
            'product_name' => $request->input('name')
        ]);

        $request->validate([
            'category_id' => 'required|exists:menu_categories,id',
            'name' => 'required|string|max:255',
            'sku' => 'nullable|string|max:255|unique:menu_items,sku',
            'barcode' => 'nullable|string|max:255|unique:menu_items,barcode',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'stock_quantity' => 'nullable|integer|min:0',
            'is_active' => 'nullable|boolean',
            'is_food_item' => 'nullable|boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only([
            'category_id', 'name', 'sku', 'barcode', 'description',
            'price', 'cost_price', 'stock_quantity', 'is_active', 'is_food_item'
        ]);

        // Set defaults
        $data['is_active'] = $request->boolean('is_active', true);
        $data['is_food_item'] = $request->boolean('is_food_item', true);
        $data['stock_quantity'] = $request->input('stock_quantity', 0);

        // Handle image upload
        if ($request->hasFile('image')) {
            $image = $request->file('image');

            // Validate the uploaded file
            if ($image->isValid()) {
                try {
                    // Ensure the directory exists
                    $directory = 'menu-items';
                    if (!Storage::disk('public')->exists($directory)) {
                        Storage::disk('public')->makeDirectory($directory);
                    }

                    // Generate a unique filename
                    $filename = time() . '_' . $image->getClientOriginalName();
                    $path = $image->storeAs($directory, $filename, 'public');

                    if ($path) {
                        $data['image'] = $path;
                    } else {
                        return redirect()->back()
                            ->withErrors(['image' => 'Failed to upload image. Please try again.'])
                            ->withInput();
                    }
                } catch (\Exception $e) {
                    return redirect()->back()
                        ->withErrors(['image' => 'Error uploading image: ' . $e->getMessage()])
                        ->withInput();
                }
            } else {
                return redirect()->back()
                    ->withErrors(['image' => 'Invalid image file. Please select a valid image.'])
                    ->withInput();
            }
        }

        // Create the product locally first
        $menuItem = MenuItem::create($data);

        \Log::info('Product created locally', [
            'product_id' => $menuItem->id,
            'product_name' => $menuItem->name,
            'user_id' => auth()->id()
        ]);

        // Automatically sync to API
        $syncResult = $this->productSyncService->syncProductToApi($menuItem);

        if ($syncResult['success']) {
            \Log::info('Product successfully synced to API', [
                'product_id' => $menuItem->id,
                'api_id' => $syncResult['api_id'] ?? null
            ]);

            return redirect()->route('menu-items.index')
                ->with('success', '✅ Produk berhasil dibuat dan disinkronkan ke API!')
                ->with('sync_status', 'success')
                ->with('sync_details', [
                    'product_name' => $menuItem->name,
                    'api_id' => $syncResult['api_id'] ?? null,
                    'sync_time' => now()->format('H:i:s')
                ]);
        } else {
            // If sync failed, we still have the local product but show warning
            if ($syncResult['local_only'] ?? false) {
                \Log::warning('Product created locally but API sync failed', [
                    'product_id' => $menuItem->id,
                    'error' => $syncResult['error']
                ]);

                return redirect()->route('menu-items.index')
                    ->with('warning', '⚠️ Produk berhasil dibuat secara lokal, tetapi gagal disinkronkan ke API.')
                    ->with('sync_status', 'failed')
                    ->with('sync_details', [
                        'product_name' => $menuItem->name,
                        'error' => $syncResult['error'],
                        'sync_time' => now()->format('H:i:s')
                    ]);
            } else {
                // If it's a critical error, delete the local product and show error
                \Log::error('Product creation failed completely', [
                    'error' => $syncResult['error'],
                    'user_id' => auth()->id()
                ]);

                $menuItem->delete();
                return redirect()->back()
                    ->with('error', '❌ Gagal membuat produk: ' . $syncResult['error'])
                    ->withInput();
            }
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(MenuItem $menuItem)
    {
        $menuItem->load('category');
        return view('menu-items.show', compact('menuItem'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MenuItem $menuItem)
    {
        $categories = MenuCategory::all();
        return view('menu-items.edit', compact('menuItem', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MenuItem $menuItem)
    {
        $request->validate([
            'category_id' => 'required|exists:menu_categories,id',
            'name' => 'required|string|max:255',
            'sku' => 'nullable|string|max:255|unique:menu_items,sku,' . $menuItem->id,
            'barcode' => 'nullable|string|max:255|unique:menu_items,barcode,' . $menuItem->id,
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'stock_quantity' => 'nullable|integer|min:0',
            'is_active' => 'nullable|boolean',
            'is_food_item' => 'nullable|boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only([
            'category_id', 'name', 'sku', 'barcode', 'description',
            'price', 'cost_price', 'stock_quantity', 'is_active', 'is_food_item'
        ]);

        // Handle boolean fields
        $data['is_active'] = $request->boolean('is_active', $menuItem->is_active);
        $data['is_food_item'] = $request->boolean('is_food_item', $menuItem->is_food_item);
        $data['stock_quantity'] = $request->input('stock_quantity', $menuItem->stock_quantity);

        // Handle image upload
        if ($request->hasFile('image')) {
            $image = $request->file('image');

            // Validate the uploaded file
            if ($image->isValid()) {
                try {
                    // Ensure the directory exists
                    $directory = 'menu-items';
                    if (!Storage::disk('public')->exists($directory)) {
                        Storage::disk('public')->makeDirectory($directory);
                    }

                    // Generate a unique filename
                    $filename = time() . '_' . $image->getClientOriginalName();
                    $path = $image->storeAs($directory, $filename, 'public');

                    if ($path) {
                        // Delete old image if exists
                        if ($menuItem->image && Storage::disk('public')->exists($menuItem->image)) {
                            Storage::disk('public')->delete($menuItem->image);
                        }

                        $data['image'] = $path;
                    } else {
                        return redirect()->back()
                            ->withErrors(['image' => 'Failed to upload image. Please try again.'])
                            ->withInput();
                    }
                } catch (\Exception $e) {
                    return redirect()->back()
                        ->withErrors(['image' => 'Error uploading image: ' . $e->getMessage()])
                        ->withInput();
                }
            } else {
                return redirect()->back()
                    ->withErrors(['image' => 'Invalid image file. Please select a valid image.'])
                    ->withInput();
            }
        }

        $menuItem->update($data);

        return redirect()->route('menu-items.index')
                        ->with('success', 'Menu item updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MenuItem $menuItem)
    {
        // Delete image if exists
        if ($menuItem->image) {
            Storage::disk('public')->delete($menuItem->image);
        }

        $menuItem->delete();

        return redirect()->route('menu-items.index')
                        ->with('success', 'Menu item deleted successfully.');
    }

    /**
     * Sync menu items from API
     */
    public function sync()
    {
        $result = $this->productService->forceSyncFromApi();

        if ($result['success']) {
            $message = 'Menu items synchronized successfully! ';
            $message .= "Created {$result['stats']['categories_created']} categories, ";
            $message .= "updated {$result['stats']['categories_updated']} categories, ";
            $message .= "created {$result['stats']['items_created']} items, ";
            $message .= "updated {$result['stats']['items_updated']} items.";

            return redirect()->route('menu-items.index')
                            ->with('success', $message);
        } else {
            return redirect()->route('menu-items.index')
                            ->with('error', 'Sync failed: ' . $result['error']);
        }
    }

    /**
     * Get sync status as JSON
     */
    public function syncStatus()
    {
        return response()->json($this->productService->getSyncStatus());
    }

    /**
     * Perform manual bidirectional sync between local and API based on SKU
     */
    public function manualSync()
    {
        try {
            // Check network connectivity first
            if (!$this->productSyncService->isNetworkAvailable()) {
                return redirect()->route('menu-items.index')
                    ->with('error', 'Manual sync failed: No network connection available.');
            }

            // Get the manual sync service through app container to avoid dependency issues
            $manualSyncService = app(\App\Services\ManualProductSyncService::class);
            $result = $manualSyncService->performBidirectionalSync();

            if ($result['success']) {
                $stats = $result['stats'];

                // Create summary message
                $localToApiSent = $stats['local_to_api']['products_sent'];
                $apiToLocalCreated = $stats['api_to_local']['products_created'];
                $totalSkipped = $stats['local_to_api']['products_skipped'] + $stats['api_to_local']['products_skipped'];
                $totalFailed = $stats['local_to_api']['products_failed'] + $stats['api_to_local']['products_failed'];

                $message = "✅ Manual sync completed! ";
                $message .= "Sent {$localToApiSent} products to API, ";
                $message .= "created {$apiToLocalCreated} products locally, ";
                $message .= "skipped {$totalSkipped} existing products";

                if ($totalFailed > 0) {
                    $message .= ", {$totalFailed} failed";
                }

                \Log::info('Manual sync completed successfully', [
                    'user_id' => auth()->id(),
                    'stats' => $stats
                ]);

                return redirect()->route('menu-items.index')
                    ->with('success', $message)
                    ->with('sync_details', $stats);
            } else {
                \Log::warning('Manual sync failed', [
                    'user_id' => auth()->id(),
                    'error' => $result['error']
                ]);

                return redirect()->route('menu-items.index')
                    ->with('error', '❌ Manual sync failed: ' . $result['error']);
            }

        } catch (\Exception $e) {
            \Log::error('Manual sync error in controller', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id()
            ]);

            return redirect()->route('menu-items.index')
                ->with('error', '❌ Manual sync failed: ' . $e->getMessage());
        }
    }

    /**
     * Upload a menu item to the external API
     */
    public function uploadToApi(MenuItem $menuItem)
    {
        try {
            // Use the ProductSyncService to sync the product to API
            $result = $this->productSyncService->syncProductToApi($menuItem);

            if ($result['success']) {
                // Mark as synced using the model method
                $menuItem->markAsSyncedToApi($result['api_id'] ?? null);

                \Log::info('Product uploaded to API successfully', [
                    'menu_item_id' => $menuItem->id,
                    'api_id' => $result['api_id'] ?? null,
                    'user_id' => auth()->id()
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Product uploaded to API successfully',
                    'api_id' => $result['api_id'] ?? null
                ]);
            } else {
                \Log::warning('Product upload to API failed', [
                    'menu_item_id' => $menuItem->id,
                    'error' => $result['error'] ?? 'Unknown error',
                    'user_id' => auth()->id()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => $result['error'] ?? 'Failed to upload product to API'
                ], 500);
            }
        } catch (\Exception $e) {
            \Log::error('MenuItemController: Failed to upload product to API', [
                'menu_item_id' => $menuItem->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while uploading to API: ' . $e->getMessage()
            ], 500);
        }
    }
}
