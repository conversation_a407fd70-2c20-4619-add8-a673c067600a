<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;
use App\Services\CustomerService;
use App\Services\CustomerSyncService;
use Illuminate\Support\Facades\Validator;

class CustomerController extends Controller
{
    private CustomerService $customerService;
    private CustomerSyncService $customerSyncService;

    public function __construct(CustomerService $customerService, CustomerSyncService $customerSyncService)
    {
        $this->middleware(['auth']);
        $this->customerService = $customerService;
        $this->customerSyncService = $customerSyncService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Get customers from API-first service
        $customers = $this->customerService->getCustomers();
        $syncStatus = $this->customerService->getSyncStatus();

        // Apply search filter if provided
        if ($request->has('search') && $request->search) {
            $search = strtolower($request->search);
            $customers = array_filter($customers, function($customer) use ($search) {
                return strpos(strtolower($customer->name), $search) !== false ||
                       strpos(strtolower($customer->phone ?? ''), $search) !== false ||
                       strpos(strtolower($customer->email ?? ''), $search) !== false;
            });
        }

        return view('customers.index', compact('customers', 'syncStatus'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Customer $customer)
    {
        return view('customers.edit', compact('customer'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Customer $customer)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20|unique:customers,phone,' . $customer->id,
            'email' => 'nullable|email|max:255|unique:customers,email,' . $customer->id,
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'customer_segment' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:500',
            'detailed_address' => 'nullable|string|max:1000',
            'postal_code' => 'nullable|string|max:10',
            'points' => 'nullable|integer|min:0',
            'loyalty_points' => 'nullable|integer|min:0',
            'active_status' => 'nullable|boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Use CustomerService for update with API sync
        $result = $this->customerService->updateCustomer($customer, $request->all());

        if ($result['success']) {
            return redirect()->route('settings.customers.index')
                ->with('success', $result['message']);
        } else {
            return redirect()->back()
                ->with('error', $result['error'])
                ->withInput();
        }
    }

    /**
     * Store a newly created customer
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20|unique:customers,phone',
            'email' => 'nullable|email|max:255|unique:customers,email',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'customer_segment' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:500',
            'detailed_address' => 'nullable|string|max:1000',
            'postal_code' => 'nullable|string|max:10',
            'points' => 'nullable|integer|min:0',
            'loyalty_points' => 'nullable|integer|min:0',
            'active_status' => 'nullable|boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Use CustomerService for creation with API sync
        $result = $this->customerService->createCustomer($request->all());

        if ($result['success']) {
            return redirect()->route('settings.customers.index')
                ->with('success', $result['message']);
        } else {
            return redirect()->back()
                ->with('error', $result['error'])
                ->withInput();
        }
    }

    /**
     * Remove the specified customer
     */
    public function destroy(Customer $customer)
    {
        // Use CustomerService for deletion with API sync
        $result = $this->customerService->deleteCustomer($customer);

        if ($result['success']) {
            return redirect()->route('settings.customers.index')
                ->with('success', $result['message']);
        } else {
            return redirect()->back()
                ->with('error', $result['error']);
        }
    }

    /**
     * Manually sync customers to API
     */
    public function syncToApi()
    {
        try {
            // Sync all unsynced customers
            $result = $this->customerSyncService->syncUnsyncedCustomers();

            $message = "Sync completed: {$result['success_count']} customers synced successfully";
            if ($result['failed_count'] > 0) {
                $message .= ", {$result['failed_count']} failed";
            }

            if ($result['success_count'] > 0) {
                return redirect()->back()->with('success', $message);
            } else {
                return redirect()->back()->with('warning', $message);
            }

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Sync failed: ' . $e->getMessage());
        }
    }

    /**
     * Sync customers from API
     */
    public function sync()
    {
        $result = $this->customerService->forceSyncFromApi();

        if ($result['success']) {
            $message = 'Customers synchronized successfully! ';
            $message .= "Created {$result['stats']['customers_created']} customers, ";
            $message .= "updated {$result['stats']['customers_updated']} customers.";

            return redirect()->route('settings.customers.index')
                            ->with('success', $message);
        } else {
            return redirect()->route('settings.customers.index')
                            ->with('error', 'Sync failed: ' . $result['error']);
        }
    }

    /**
     * Get sync status as JSON
     */
    public function syncStatus()
    {
        return response()->json($this->customerService->getSyncStatus());
    }

    /**
     * Show the form for creating a new customer
     */
    public function create()
    {
        return view('customers.create');
    }
}
