<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    ➕ Create Tax Setting
                </h2>
                <p class="text-sm text-gray-600 mt-1">Add a new tax configuration</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{{ route('settings.tax.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    ← Back to Tax Settings
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            
            @if($errors->any())
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    <ul class="list-disc list-inside">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('settings.tax.store') }}">
                        @csrf
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Tax Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700">Tax Name</label>
                                <input type="text" id="name" name="name" value="{{ old('name') }}" 
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                       placeholder="e.g., Sales Tax, VAT, GST" required>
                                <p class="text-xs text-gray-500 mt-1">A descriptive name for this tax setting</p>
                            </div>

                            <!-- Tax Rate -->
                            <div>
                                <label for="rate" class="block text-sm font-medium text-gray-700">Tax Rate</label>
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <input type="number" id="rate" name="rate" value="{{ old('rate', '0.10') }}" 
                                           step="0.0001" min="0" max="1"
                                           class="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 pr-12"
                                           placeholder="0.10" required>
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                        <span class="text-gray-500 sm:text-sm" id="rate-percentage">10%</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Enter as decimal (0.10 = 10%, 0.0825 = 8.25%)</p>
                            </div>
                        </div>

                        <!-- Application Type -->
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-3">Tax Application Method</label>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="relative">
                                    <input type="radio" id="customer_pays" name="application_type" value="customer_pays" 
                                           {{ old('application_type', 'customer_pays') === 'customer_pays' ? 'checked' : '' }}
                                           class="sr-only">
                                    <label for="customer_pays" class="block p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 transition-colors">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                                    <span class="text-white text-sm font-bold">💰</span>
                                                </div>
                                            </div>
                                            <div class="ml-3">
                                                <h4 class="text-sm font-medium text-gray-900">Customer Pays Tax</h4>
                                                <p class="text-xs text-gray-500">Tax is added to the final price</p>
                                                <p class="text-xs text-blue-600 mt-1">$10.00 + $1.00 tax = $11.00 total</p>
                                            </div>
                                        </div>
                                    </label>
                                </div>

                                <div class="relative">
                                    <input type="radio" id="business_absorbs" name="application_type" value="business_absorbs"
                                           {{ old('application_type') === 'business_absorbs' ? 'checked' : '' }}
                                           class="sr-only">
                                    <label for="business_absorbs" class="block p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-purple-300 transition-colors">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                                    <span class="text-white text-sm font-bold">🏢</span>
                                                </div>
                                            </div>
                                            <div class="ml-3">
                                                <h4 class="text-sm font-medium text-gray-900">Business Absorbs Tax</h4>
                                                <p class="text-xs text-gray-500">Tax is included in the displayed price</p>
                                                <p class="text-xs text-purple-600 mt-1">$10.00 (includes $0.91 tax) = $10.00 total</p>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mt-6">
                            <label for="description" class="block text-sm font-medium text-gray-700">Description (Optional)</label>
                            <textarea id="description" name="description" rows="3" 
                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                      placeholder="Additional notes about this tax setting...">{{ old('description') }}</textarea>
                        </div>

                        <!-- Active Status -->
                        <div class="mt-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="is_active" name="is_active" value="1" 
                                       {{ old('is_active') ? 'checked' : '' }}
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                    Set as active tax setting
                                </label>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">If checked, this will become the active tax setting and deactivate others</p>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="mt-8 flex items-center justify-end space-x-3">
                            <a href="{{ route('settings.tax.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                ✅ Create Tax Setting
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Preview Section -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-3">📋 Preview</h3>
                <div id="tax-preview" class="bg-white p-4 rounded-lg">
                    <div class="text-sm">
                        <p><strong>Tax Name:</strong> <span id="preview-name">Sales Tax</span></p>
                        <p><strong>Rate:</strong> <span id="preview-rate">10.00%</span></p>
                        <p><strong>Application:</strong> <span id="preview-application">Customer Pays Tax</span></p>
                        <div class="mt-3 p-3 bg-gray-50 rounded">
                            <p class="font-medium">Example Calculation:</p>
                            <p id="preview-calculation">Item: $10.00 + Tax: $1.00 = Total: $11.00</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Update percentage display and preview
        function updatePreview() {
            const rate = parseFloat(document.getElementById('rate').value) || 0;
            const percentage = (rate * 100).toFixed(2);
            document.getElementById('rate-percentage').textContent = percentage + '%';
            
            // Update preview
            const name = document.getElementById('name').value || 'Sales Tax';
            const applicationType = document.querySelector('input[name="application_type"]:checked').value;
            
            document.getElementById('preview-name').textContent = name;
            document.getElementById('preview-rate').textContent = percentage + '%';
            
            if (applicationType === 'customer_pays') {
                document.getElementById('preview-application').textContent = 'Customer Pays Tax';
                const taxAmount = (10 * rate).toFixed(2);
                const total = (10 + parseFloat(taxAmount)).toFixed(2);
                document.getElementById('preview-calculation').textContent = `Item: $10.00 + Tax: $${taxAmount} = Total: $${total}`;
            } else {
                document.getElementById('preview-application').textContent = 'Business Absorbs Tax';
                const taxAmount = (10 * rate / (1 + rate)).toFixed(2);
                document.getElementById('preview-calculation').textContent = `Item: $10.00 (includes $${taxAmount} tax) = Total: $10.00`;
            }
        }

        // Update radio button styling
        function updateRadioStyling() {
            document.querySelectorAll('input[name="application_type"]').forEach(radio => {
                const label = radio.nextElementSibling;
                if (radio.checked) {
                    label.classList.add('border-blue-500', 'bg-blue-50');
                    label.classList.remove('border-gray-200');
                } else {
                    label.classList.remove('border-blue-500', 'bg-blue-50');
                    label.classList.add('border-gray-200');
                }
            });
        }

        // Event listeners
        document.getElementById('rate').addEventListener('input', updatePreview);
        document.getElementById('name').addEventListener('input', updatePreview);
        document.querySelectorAll('input[name="application_type"]').forEach(radio => {
            radio.addEventListener('change', () => {
                updatePreview();
                updateRadioStyling();
            });
        });

        // Initial setup
        updatePreview();
        updateRadioStyling();
    </script>
</x-app-layout>
