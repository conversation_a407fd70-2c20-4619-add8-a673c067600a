{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/forms": "^0.5.2", "@tailwindcss/vite": "^4.0.0", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.2", "axios": "^1.8.2", "concurrently": "^9.0.1", "cross-env": "^10.0.0", "electron": "^37.3.1", "electron-builder": "^26.0.12", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "tailwindcss": "^3.1.0", "vite": "^6.2.4"}, "dependencies": {"electron-vite": "^4.0.0", "playwright": "^1.55.0"}, "name": "POS", "description": "<p align=\"center\"><a href=\"https://laravel.com\" target=\"_blank\"><img src=\"https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg\" width=\"400\" alt=\"Laravel Logo\"></a></p>", "version": "1.0.0", "main": "api_test_examples.js", "directories": {"test": "tests"}, "repository": {"type": "git", "url": "git+https://github.com/tintopratam/POS.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/tintopratam/POS/issues"}, "homepage": "https://github.com/tintopratam/POS#readme"}