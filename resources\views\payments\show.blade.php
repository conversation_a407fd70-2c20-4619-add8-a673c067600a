<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Payment - Order #') . $order->id }}
            </h2>
            <div class="flex space-x-2">
                @if($order->table)
                    <span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">
                        {{ $order->table->name }}
                    </span>
                @else
                    <span class="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                        {{ ucfirst($order->order_type ?? 'Take Away') }}
                    </span>
                @endif
                <a href="{{ route('pos.order', $order) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Order
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                
                <!-- Order Summary -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">Order Summary</h3>
                        
                        <div class="space-y-3 mb-4">
                            @foreach($order->orderItems as $item)
                                <div class="flex justify-between items-center border-b pb-2">
                                    <div>
                                        <h5 class="font-medium">
                                            @if($item->menuItem)
                                                {{ $item->menuItem->name }}
                                            @else
                                                <span class="text-red-600">[Deleted Item - ID: {{ $item->menu_item_id }}]</span>
                                            @endif
                                        </h5>
                                        @if($item->notes)
                                            <p class="text-sm text-gray-500">{{ $item->notes }}</p>
                                        @endif
                                        <p class="text-sm text-gray-600">{{ $orderSettings['currency_symbol'] }}{{ number_format($item->price_at_time, 2) }} × {{ $item->quantity }}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-medium">{{ $orderSettings['currency_symbol'] }}{{ number_format($item->price_at_time * $item->quantity, 2) }}</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <div class="border-t pt-4">
                            <div class="flex justify-between text-sm">
                                <span>Subtotal:</span>
                                <span id="paymentSubtotal">{{ $orderSettings['currency_symbol'] }}{{ number_format($order->subtotal_amount ?? $order->subtotal, 2) }}</span>
                            </div>

                            <!-- Applied Discounts Section -->
                            <div id="paymentDiscountsSection">
                                <!-- Discounts will be loaded here -->
                            </div>

                            <div class="flex justify-between text-sm">
                                <span>Tax ({{ number_format($orderSettings['tax_rate'] * 100, 1) }}%):</span>
                                <span id="paymentTax">{{ $orderSettings['currency_symbol'] }}{{ number_format($order->tax_amount, 2) }}</span>
                            </div>

                            @if($order->service_charge_amount > 0)
                                <div class="flex justify-between text-sm">
                                    <span>Service Charge:</span>
                                    <span id="paymentServiceCharge">{{ $orderSettings['currency_symbol'] }}{{ number_format($order->service_charge_amount, 2) }}</span>
                                </div>
                            @endif

                            <div class="flex justify-between text-lg font-bold border-t pt-2 mt-2">
                                <span>Total:</span>
                                <span id="paymentTotal">{{ $orderSettings['currency_symbol'] }}{{ number_format($order->total_amount, 2) }}</span>
                            </div>
                            
                            @if($totalPaid > 0)
                                <div class="flex justify-between text-sm text-green-600 mt-2">
                                    <span>Already Paid:</span>
                                    <span>-{{ $orderSettings['currency_symbol'] }}{{ number_format($totalPaid, 2) }}</span>
                                </div>
                                <div class="flex justify-between text-lg font-bold text-red-600 border-t pt-2 mt-2">
                                    <span>Remaining:</span>
                                    <span>{{ $orderSettings['currency_symbol'] }}{{ number_format($remainingAmount, 2) }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Discount Management Section -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">Manage Discounts</h3>
                            <button onclick="togglePaymentDiscountSection()"
                                    class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <span id="paymentDiscountToggleText">Add Discount</span>
                                <svg id="paymentDiscountToggleIcon" class="w-4 h-4 inline-block ml-1 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                        </div>

                        <!-- Applied Discounts Display -->
                        <div id="paymentAppliedDiscountsSection" class="hidden mb-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Applied Discounts</h4>
                            <div id="paymentAppliedDiscountsList" class="space-y-2 max-h-32 overflow-y-auto">
                                <!-- Discounts will be loaded here -->
                            </div>
                        </div>

                        <!-- Add New Discount Section -->
                        <div id="paymentDiscountForm" class="hidden space-y-3">
                            <!-- Discount Type Selection -->
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">Add New Discount</label>
                                <select id="paymentDiscountType" onchange="handlePaymentDiscountTypeChange()"
                                        class="w-full text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500">
                                    <option value="none">Select Discount Type</option>
                                    <option value="percentage">Percentage Discount</option>
                                    <option value="fixed">Fixed Amount Discount</option>
                                    <option value="coupon">Coupon Code</option>
                                    <option value="points">Use Points</option>
                                </select>
                            </div>

                            <!-- Percentage Discount -->
                            <div id="paymentPercentageDiscountSection" class="hidden">
                                <label class="block text-xs font-medium text-gray-700 mb-1">Discount Percentage</label>
                                <div class="flex items-center space-x-2">
                                    <input type="number" id="paymentDiscountPercentage" min="0" max="100" step="0.01"
                                           class="flex-1 text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
                                           placeholder="Enter percentage (e.g., 10 for 10%)">
                                    <button onclick="applyPaymentPercentageDiscount()"
                                            class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-xs">
                                        Apply
                                    </button>
                                </div>
                            </div>

                            <!-- Fixed Amount Discount -->
                            <div id="paymentFixedDiscountSection" class="hidden">
                                <label class="block text-xs font-medium text-gray-700 mb-1">Discount Amount</label>
                                <div class="flex items-center space-x-2">
                                    <input type="number" id="paymentDiscountAmount" min="0" step="0.01"
                                           class="flex-1 text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
                                           placeholder="Enter discount amount">
                                    <button onclick="applyPaymentFixedDiscount()"
                                            class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-xs">
                                        Apply
                                    </button>
                                </div>
                            </div>

                            <!-- Coupon Discount -->
                            <div id="paymentCouponDiscountSection" class="hidden">
                                <label class="block text-xs font-medium text-gray-700 mb-1">Coupon Code</label>
                                <div class="flex items-center space-x-2">
                                    <input type="text" id="paymentCouponCode"
                                           class="flex-1 text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
                                           placeholder="Enter coupon code">
                                    <button onclick="applyPaymentCouponDiscount()"
                                            class="bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded text-xs">
                                        Apply
                                    </button>
                                </div>
                            </div>

                            <!-- Points Discount -->
                            <div id="paymentPointsDiscountSection" class="hidden">
                                <label class="block text-xs font-medium text-gray-700 mb-1">Use Points</label>
                                <div class="text-xs text-gray-600 mb-2">
                                    Customer: {{ $order->customer->name ?? 'Walk-in Customer' }} | Available: <span id="paymentCustomerPoints">{{ $order->customer->points ?? 0 }}</span> points (100 points = {{ $orderSettings['currency_symbol'] }}1.00)
                                </div>
                                <div class="flex items-center space-x-2">
                                    <input type="number" id="paymentPointsToUse" min="0" max="{{ $order->customer->points ?? 0 }}" step="1"
                                           class="flex-1 text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
                                           placeholder="Enter points to use">
                                    <button onclick="applyPaymentPointsDiscount()"
                                            class="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-xs">
                                        Apply
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Form -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">Process Payment</h3>
                        
                        <form action="{{ route('payments.process', $order) }}" method="POST" id="paymentForm">
                            @csrf
                            
                            <!-- Payment Method -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">Payment Method</label>
                                <div class="grid grid-cols-3 gap-3">
                                    <label class="payment-method cursor-pointer">
                                        <input type="radio" name="method" value="Cash" class="sr-only" required>
                                        <div class="border-2 border-gray-300 rounded-lg p-4 text-center hover:border-blue-500 transition-colors">
                                            <div class="text-2xl mb-2">💵</div>
                                            <div class="font-medium">Cash</div>
                                        </div>
                                    </label>
                                    <label class="payment-method cursor-pointer">
                                        <input type="radio" name="method" value="Card" class="sr-only">
                                        <div class="border-2 border-gray-300 rounded-lg p-4 text-center hover:border-blue-500 transition-colors">
                                            <div class="text-2xl mb-2">💳</div>
                                            <div class="font-medium">Card</div>
                                        </div>
                                    </label>
                                    <label class="payment-method cursor-pointer">
                                        <input type="radio" name="method" value="QRIS" class="sr-only">
                                        <div class="border-2 border-gray-300 rounded-lg p-4 text-center hover:border-blue-500 transition-colors">
                                            <div class="text-2xl mb-2">📱</div>
                                            <div class="font-medium">QRIS</div>
                                        </div>
                                    </label>
                                </div>
                                @error('method')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Amount -->
                            <div class="mb-6">
                                <label for="amount_paid" class="block text-sm font-medium text-gray-700 mb-2">Amount Paid</label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                                    <input type="number" 
                                           id="amount_paid" 
                                           name="amount_paid" 
                                           step="0.01" 
                                           min="0.01"
                                           value="{{ number_format($remainingAmount, 2, '.', '') }}"
                                           class="pl-8 w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-lg font-medium"
                                           required>
                                </div>
                                @error('amount_paid')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Quick Amount Buttons -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Quick Amount</label>
                                <div class="grid grid-cols-4 gap-2">
                                    <button type="button" onclick="setAmount({{ $remainingAmount }})" class="quick-amount bg-gray-100 hover:bg-gray-200 px-3 py-2 rounded text-sm">
                                        Exact
                                    </button>
                                    <button type="button" onclick="setAmount({{ ceil($remainingAmount / 5) * 5 }})" class="quick-amount bg-gray-100 hover:bg-gray-200 px-3 py-2 rounded text-sm">
                                        {{ $orderSettings['currency_symbol'] }}{{ ceil($remainingAmount / 5) * 5 }}
                                    </button>
                                    <button type="button" onclick="setAmount({{ ceil($remainingAmount / 10) * 10 }})" class="quick-amount bg-gray-100 hover:bg-gray-200 px-3 py-2 rounded text-sm">
                                        {{ $orderSettings['currency_symbol'] }}{{ ceil($remainingAmount / 10) * 10 }}
                                    </button>
                                    <button type="button" onclick="setAmount({{ ceil($remainingAmount / 20) * 20 }})" class="quick-amount bg-gray-100 hover:bg-gray-200 px-3 py-2 rounded text-sm">
                                        {{ $orderSettings['currency_symbol'] }}{{ ceil($remainingAmount / 20) * 20 }}
                                    </button>
                                </div>
                            </div>

                            <!-- Change Display -->
                            <div id="changeDisplay" class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg hidden">
                                <div class="flex justify-between items-center">
                                    <span class="font-medium text-green-800">Change:</span>
                                    <span id="changeAmount" class="text-xl font-bold text-green-800">{{ $orderSettings['currency_symbol'] }}0.00</span>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <button type="submit" class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-4 rounded-lg text-lg">
                                Process Payment
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentOrderTotal = {{ $remainingAmount }};
        let paymentDiscounts = [];
        const currencySymbol = '{{ $orderSettings['currency_symbol'] }}';

        // Handle payment method selection
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                // Remove selected class from all methods
                document.querySelectorAll('.payment-method div').forEach(div => {
                    div.classList.remove('border-blue-500', 'bg-blue-50');
                    div.classList.add('border-gray-300');
                });

                // Add selected class to clicked method
                const div = this.querySelector('div');
                div.classList.remove('border-gray-300');
                div.classList.add('border-blue-500', 'bg-blue-50');

                // Check the radio button
                this.querySelector('input[type="radio"]').checked = true;

                // Update change calculation
                calculateChange();
            });
        });

        // Set amount from quick buttons
        function setAmount(amount) {
            document.getElementById('amount_paid').value = amount.toFixed(2);
            calculateChange();
        }

        // Calculate and display change
        function calculateChange() {
            const amountPaid = parseFloat(document.getElementById('amount_paid').value) || 0;
            const change = Math.max(0, amountPaid - currentOrderTotal);

            const changeDisplay = document.getElementById('changeDisplay');
            const changeAmount = document.getElementById('changeAmount');

            if (change > 0) {
                changeDisplay.classList.remove('hidden');
                changeAmount.textContent = currencySymbol + change.toFixed(2);
            } else {
                changeDisplay.classList.add('hidden');
            }
        }

        // Update quick amount buttons
        function updateQuickAmountButtons() {
            const exactButton = document.querySelector('.quick-amount');
            if (exactButton) {
                exactButton.onclick = () => setAmount(currentOrderTotal);
                exactButton.textContent = 'Exact';
            }
        }

        // Listen for amount input changes
        document.getElementById('amount_paid').addEventListener('input', calculateChange);

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Select cash by default
            const cashMethod = document.querySelector('input[value="Cash"]').closest('.payment-method');
            if (cashMethod) {
                cashMethod.click();
            }

            // Load existing discounts
            loadPaymentDiscounts();
            calculateChange();
        });

        // Discount Management Functions
        function togglePaymentDiscountSection() {
            const form = document.getElementById('paymentDiscountForm');
            const toggleText = document.getElementById('paymentDiscountToggleText');
            const toggleIcon = document.getElementById('paymentDiscountToggleIcon');

            if (form.classList.contains('hidden')) {
                form.classList.remove('hidden');
                toggleText.textContent = 'Hide Discounts';
                toggleIcon.style.transform = 'rotate(180deg)';
            } else {
                form.classList.add('hidden');
                toggleText.textContent = 'Add Discount';
                toggleIcon.style.transform = 'rotate(0deg)';
            }
        }

        function handlePaymentDiscountTypeChange() {
            const discountType = document.getElementById('paymentDiscountType').value;

            // Hide all sections
            document.getElementById('paymentPercentageDiscountSection').classList.add('hidden');
            document.getElementById('paymentFixedDiscountSection').classList.add('hidden');
            document.getElementById('paymentCouponDiscountSection').classList.add('hidden');
            document.getElementById('paymentPointsDiscountSection').classList.add('hidden');

            // Show selected section
            if (discountType === 'percentage') {
                document.getElementById('paymentPercentageDiscountSection').classList.remove('hidden');
            } else if (discountType === 'fixed') {
                document.getElementById('paymentFixedDiscountSection').classList.remove('hidden');
            } else if (discountType === 'coupon') {
                document.getElementById('paymentCouponDiscountSection').classList.remove('hidden');
            } else if (discountType === 'points') {
                document.getElementById('paymentPointsDiscountSection').classList.remove('hidden');
            }
        }

        function applyPaymentPercentageDiscount() {
            const percentage = parseFloat(document.getElementById('paymentDiscountPercentage').value);

            if (!percentage || percentage < 0 || percentage > 100) {
                alert('Please enter a valid percentage between 0 and 100.');
                return;
            }

            addPaymentDiscount('percentage', percentage, null, null, `${percentage}% Discount`);
        }

        function applyPaymentFixedDiscount() {
            const amount = parseFloat(document.getElementById('paymentDiscountAmount').value);

            if (!amount || amount < 0) {
                alert('Please enter a valid discount amount.');
                return;
            }

            addPaymentDiscount('fixed', amount, null, null, `${currencySymbol}${amount.toFixed(2)} Off`);
        }

        function applyPaymentCouponDiscount() {
            const couponCode = document.getElementById('paymentCouponCode').value.trim();

            if (!couponCode) {
                alert('Please enter a coupon code.');
                return;
            }

            // For now, apply a fixed 5.00 discount for any coupon
            // In a real system, you'd validate the coupon and get its value
            addPaymentDiscount('coupon', 5.00, couponCode, null, `Coupon: ${couponCode}`);
        }

        function applyPaymentPointsDiscount() {
            const pointsToUse = parseInt(document.getElementById('paymentPointsToUse').value);
            const availablePoints = parseInt(document.getElementById('paymentCustomerPoints').textContent) || 0;

            if (!pointsToUse || pointsToUse < 1) {
                alert('Please enter a valid number of points to use.');
                return;
            }

            if (pointsToUse > availablePoints) {
                alert('Not enough points available.');
                return;
            }

            const discountAmount = pointsToUse / 100; // 100 points = 1.00 currency unit
            addPaymentDiscount('points', discountAmount, null, pointsToUse, `${pointsToUse} Points Used`);
        }

        function clearPaymentDiscountInputs() {
            document.getElementById('paymentDiscountPercentage').value = '';
            document.getElementById('paymentDiscountAmount').value = '';
            document.getElementById('paymentCouponCode').value = '';
            document.getElementById('paymentPointsToUse').value = '';
            document.getElementById('paymentDiscountType').value = 'none';
            handlePaymentDiscountTypeChange();
        }

        // Cumulative Discount System for Payment Page
        function addPaymentDiscount(type, value, couponCode = null, pointsUsed = null, description = null) {
            const discountData = {
                type: type,
                value: value,
                coupon_code: couponCode,
                points_used: pointsUsed,
                description: description
            };

            fetch(`/pos/order/{{ $order->id }}/add-discount`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(discountData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadPaymentDiscounts();
                    updatePaymentTotals();
                    clearPaymentDiscountInputs();
                } else {
                    alert('Error applying discount: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error applying discount');
            });
        }

        function removePaymentDiscount(discountId) {
            if (confirm('Are you sure you want to remove this discount?')) {
                fetch(`/pos/order/{{ $order->id }}/remove-discount`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ discount_id: discountId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadPaymentDiscounts();
                        updatePaymentTotals();
                    } else {
                        alert('Error removing discount: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error removing discount');
                });
            }
        }

        function loadPaymentDiscounts() {
            fetch(`/pos/order/{{ $order->id }}/discounts`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        paymentDiscounts = data.discounts;
                        displayPaymentDiscounts();
                        displayPaymentDiscountsSummary();
                    }
                })
                .catch(error => {
                    console.error('Error loading discounts:', error);
                });
        }

        function displayPaymentDiscounts() {
            const section = document.getElementById('paymentAppliedDiscountsSection');
            const list = document.getElementById('paymentAppliedDiscountsList');

            if (!section || !list) return;

            if (paymentDiscounts.length === 0) {
                section.classList.add('hidden');
                return;
            }

            section.classList.remove('hidden');
            list.innerHTML = '';

            paymentDiscounts.forEach(discount => {
                const discountElement = document.createElement('div');
                discountElement.className = 'bg-green-50 border border-green-200 rounded p-2 flex items-center justify-between';
                discountElement.innerHTML = `
                    <div class="text-xs">
                        <span class="font-medium text-green-800">${discount.formatted_description}</span>
                        <span class="text-green-600">(-${currencySymbol}${parseFloat(discount.amount).toFixed(2)})</span>
                    </div>
                    <button onclick="removePaymentDiscount(${discount.id})"
                            class="text-red-600 hover:text-red-800 text-xs">
                        Remove
                    </button>
                `;
                list.appendChild(discountElement);
            });
        }

        function displayPaymentDiscountsSummary() {
            const section = document.getElementById('paymentDiscountsSection');
            if (!section) return;

            if (paymentDiscounts.length === 0) {
                section.innerHTML = '';
                return;
            }

            let discountsHTML = '';
            paymentDiscounts.forEach(discount => {
                discountsHTML += `
                    <div class="flex justify-between text-sm text-green-600">
                        <span>${discount.formatted_description}:</span>
                        <span>-${currencySymbol}${parseFloat(discount.amount).toFixed(2)}</span>
                    </div>
                `;
            });

            section.innerHTML = discountsHTML;
        }

        function updatePaymentTotals() {
            // Fetch updated order totals from server
            fetch(`/pos/order/{{ $order->id }}/discounts`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Calculate new total based on remaining amount and total discount
                        const totalDiscount = data.total_discount || 0;
                        const originalTotal = {{ $order->total_amount }};
                        const totalPaid = {{ $totalPaid }};

                        currentOrderTotal = Math.max(0, originalTotal - totalDiscount - totalPaid);

                        // Update display elements
                        const totalElement = document.getElementById('paymentTotal');
                        if (totalElement) {
                            totalElement.textContent = currencySymbol + (originalTotal - totalDiscount).toFixed(2);
                        }

                        // Update payment amount input
                        const amountInput = document.getElementById('amount_paid');
                        if (amountInput) {
                            amountInput.value = currentOrderTotal.toFixed(2);
                        }

                        // Update quick amount buttons
                        updateQuickAmountButtons();

                        // Recalculate change
                        calculateChange();
                    }
                })
                .catch(error => {
                    console.error('Error updating totals:', error);
                });
        }
    </script>
</x-app-layout>
