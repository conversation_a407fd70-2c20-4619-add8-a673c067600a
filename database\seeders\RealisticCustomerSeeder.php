<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Customer;

class RealisticCustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, delete the Walk-in customer
        Customer::where('name', 'Walk-in')->delete();
        $this->command->info('Walk-in customer deleted.');

        // Clear all existing customers to start fresh
        Customer::truncate();
        $this->command->info('All existing customers cleared.');

        // Create realistic Indonesian customer data
        $customers = [
            [
                'name' => 'Budi Santoso',
                'phone' => '081234567890',
                'email' => '<EMAIL>',
                'address' => 'Jl. Sudirman No. 123, Jakarta Pusat'
            ],
            [
                'name' => 'Siti Nurhaliza',
                'phone' => '081234567891',
                'email' => '<EMAIL>',
                'address' => 'Jl. Thamrin No. 45, Jakarta Pusat'
            ],
            [
                'name' => '<PERSON>',
                'phone' => '081234567892',
                'email' => '<EMAIL>',
                'address' => 'Jl. Gatot Subroto No. 67, Jakarta Selatan'
            ],
            [
                'name' => 'Dewi Lestari',
                'phone' => '081234567893',
                'email' => '<EMAIL>',
                'address' => 'Jl. Kemang Raya No. 89, Jakarta Selatan'
            ],
            [
                'name' => 'Rudi Hartono',
                'phone' => '081234567894',
                'email' => '<EMAIL>',
                'address' => 'Jl. Senopati No. 12, Jakarta Selatan'
            ],
            [
                'name' => 'Maya Sari',
                'phone' => '081234567895',
                'email' => '<EMAIL>',
                'address' => 'Jl. Menteng Raya No. 34, Jakarta Pusat'
            ],
            [
                'name' => 'Andi Prasetyo',
                'phone' => '081234567896',
                'email' => '<EMAIL>',
                'address' => 'Jl. Kuningan No. 56, Jakarta Selatan'
            ],
            [
                'name' => 'Rina Wulandari',
                'phone' => '081234567897',
                'email' => '<EMAIL>',
                'address' => 'Jl. Pondok Indah No. 78, Jakarta Selatan'
            ],
            [
                'name' => 'Hendra Gunawan',
                'phone' => '081234567898',
                'email' => '<EMAIL>',
                'address' => 'Jl. Kelapa Gading No. 90, Jakarta Utara'
            ],
            [
                'name' => 'Lina Marlina',
                'phone' => '081234567899',
                'email' => '<EMAIL>',
                'address' => 'Jl. Pluit Raya No. 11, Jakarta Utara'
            ],
            [
                'name' => 'Bambang Sutrisno',
                'phone' => '081234567800',
                'email' => '<EMAIL>',
                'address' => 'Jl. Cempaka Putih No. 22, Jakarta Pusat'
            ],
            [
                'name' => 'Indira Sari',
                'phone' => '081234567801',
                'email' => '<EMAIL>',
                'address' => 'Jl. Tebet Raya No. 33, Jakarta Selatan'
            ],
            [
                'name' => 'Joko Widodo',
                'phone' => '081234567802',
                'email' => '<EMAIL>',
                'address' => 'Jl. Pancoran No. 44, Jakarta Selatan'
            ],
            [
                'name' => 'Sri Mulyani',
                'phone' => '081234567803',
                'email' => '<EMAIL>',
                'address' => 'Jl. Pasar Minggu No. 55, Jakarta Selatan'
            ],
            [
                'name' => 'Agus Salim',
                'phone' => '081234567804',
                'email' => '<EMAIL>',
                'address' => 'Jl. Cikini Raya No. 66, Jakarta Pusat'
            ],
            [
                'name' => 'Ratna Dewi',
                'phone' => '081234567805',
                'email' => '<EMAIL>',
                'address' => 'Jl. Salemba No. 77, Jakarta Pusat'
            ],
            [
                'name' => 'Doni Setiawan',
                'phone' => '081234567806',
                'email' => '<EMAIL>',
                'address' => 'Jl. Mangga Besar No. 88, Jakarta Barat'
            ],
            [
                'name' => 'Fitri Handayani',
                'phone' => '081234567807',
                'email' => '<EMAIL>',
                'address' => 'Jl. Grogol No. 99, Jakarta Barat'
            ],
            [
                'name' => 'Wahyu Nugroho',
                'phone' => '081234567808',
                'email' => '<EMAIL>',
                'address' => 'Jl. Jelambar No. 101, Jakarta Barat'
            ],
            [
                'name' => 'Sari Indah',
                'phone' => '081234567809',
                'email' => '<EMAIL>',
                'address' => 'Jl. Puri Indah No. 102, Jakarta Barat'
            ]
        ];

        foreach ($customers as $customerData) {
            Customer::create($customerData);
        }

        $this->command->info('Created ' . count($customers) . ' realistic customers successfully!');
    }
}
