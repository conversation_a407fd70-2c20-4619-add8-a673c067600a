<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TableLayout;

class TableLayoutController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'pos.access']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $tables = TableLayout::with('currentOrder')->get();
        return view('tables.index', compact('tables'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('tables.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:table_layouts',
        ]);

        TableLayout::create([
            'name' => $request->name,
            'status' => 'Available',
        ]);

        return redirect()->route('tables.index')
                        ->with('success', 'Table created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(TableLayout $table)
    {
        $table->load(['orders' => function($query) {
            $query->latest()->take(10);
        }, 'currentOrder']);

        return view('tables.show', compact('table'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TableLayout $table)
    {
        return view('tables.edit', compact('table'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TableLayout $table)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:table_layouts,name,' . $table->id,
            'status' => 'required|in:Available,Occupied',
        ]);

        $table->update($request->only(['name', 'status']));

        return redirect()->route('tables.index')
                        ->with('success', 'Table updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TableLayout $table)
    {
        if ($table->currentOrder) {
            return redirect()->route('tables.index')
                            ->with('error', 'Cannot delete table with active orders.');
        }

        $table->delete();

        return redirect()->route('tables.index')
                        ->with('success', 'Table deleted successfully.');
    }
}
