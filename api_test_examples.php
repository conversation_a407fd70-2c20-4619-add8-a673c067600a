<?php
/**
 * API Test Examples for POS Sync Products Endpoint
 * Endpoint: https://viera-filament.test/api/pos/sync/products
 * Token: 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35
 */

// =============================================================================
// 1. PHP using cURL
// =============================================================================

function fetchProductsWithCurl() {
    $url = 'https://viera-filament.test/api/pos/sync/products';
    $token = '5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35';
    
    // Initialize cURL
    $ch = curl_init();
    
    // Set cURL options
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json',
        ],
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false, // Only for development/testing
    ]);
    
    // Execute request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    // Error handling
    if ($error) {
        return [
            'success' => false,
            'error' => 'cURL Error: ' . $error
        ];
    }
    
    if ($httpCode !== 200) {
        return [
            'success' => false,
            'error' => 'HTTP Error: ' . $httpCode,
            'response' => $response
        ];
    }
    
    // Decode JSON response
    $data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        return [
            'success' => false,
            'error' => 'JSON Decode Error: ' . json_last_error_msg(),
            'raw_response' => $response
        ];
    }
    
    return [
        'success' => true,
        'data' => $data,
        'http_code' => $httpCode
    ];
}

// =============================================================================
// 2. PHP using Guzzle HTTP Client (if available)
// =============================================================================

function fetchProductsWithGuzzle() {
    // Requires: composer require guzzlehttp/guzzle
    
    try {
        $client = new \GuzzleHttp\Client();
        $token = '5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35';
        
        $response = $client->get('https://viera-filament.test/api/pos/sync/products', [
            'headers' => [
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'timeout' => 30,
            'verify' => false, // Only for development/testing
        ]);
        
        $data = json_decode($response->getBody()->getContents(), true);
        
        return [
            'success' => true,
            'data' => $data,
            'http_code' => $response->getStatusCode()
        ];
        
    } catch (\GuzzleHttp\Exception\RequestException $e) {
        return [
            'success' => false,
            'error' => 'Request Error: ' . $e->getMessage(),
            'http_code' => $e->hasResponse() ? $e->getResponse()->getStatusCode() : null
        ];
    } catch (\Exception $e) {
        return [
            'success' => false,
            'error' => 'General Error: ' . $e->getMessage()
        ];
    }
}

// =============================================================================
// 3. PHP using file_get_contents (simple method)
// =============================================================================

function fetchProductsWithFileGetContents() {
    $url = 'https://viera-filament.test/api/pos/sync/products';
    $token = '5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35';
    
    // Create context with headers
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'Authorization: Bearer ' . $token,
                'Accept: application/json',
                'Content-Type: application/json',
            ],
            'timeout' => 30,
        ],
        'ssl' => [
            'verify_peer' => false, // Only for development/testing
            'verify_peer_name' => false,
        ]
    ]);
    
    // Make request
    $response = @file_get_contents($url, false, $context);
    
    // Error handling
    if ($response === false) {
        $error = error_get_last();
        return [
            'success' => false,
            'error' => 'Request failed: ' . ($error['message'] ?? 'Unknown error')
        ];
    }
    
    // Decode JSON
    $data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        return [
            'success' => false,
            'error' => 'JSON Decode Error: ' . json_last_error_msg(),
            'raw_response' => $response
        ];
    }
    
    return [
        'success' => true,
        'data' => $data
    ];
}

// =============================================================================
// Usage Examples
// =============================================================================

echo "=== Testing API Endpoint ===\n\n";

// Test with cURL
echo "1. Testing with cURL:\n";
$result1 = fetchProductsWithCurl();
if ($result1['success']) {
    echo "✅ Success! HTTP Code: " . $result1['http_code'] . "\n";
    echo "📦 Products found: " . count($result1['data']['data'] ?? []) . "\n";
    echo "📄 Response preview:\n";
    echo json_encode($result1['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n\n";
} else {
    echo "❌ Error: " . $result1['error'] . "\n\n";
}

// Test with file_get_contents
echo "2. Testing with file_get_contents:\n";
$result2 = fetchProductsWithFileGetContents();
if ($result2['success']) {
    echo "✅ Success!\n";
    echo "📦 Products found: " . count($result2['data']['data'] ?? []) . "\n";
} else {
    echo "❌ Error: " . $result2['error'] . "\n\n";
}

// Uncomment to test Guzzle (if installed)
/*
echo "3. Testing with Guzzle:\n";
$result3 = fetchProductsWithGuzzle();
if ($result3['success']) {
    echo "✅ Success! HTTP Code: " . $result3['http_code'] . "\n";
    echo "📦 Products found: " . count($result3['data']['data'] ?? []) . "\n";
} else {
    echo "❌ Error: " . $result3['error'] . "\n\n";
}
*/

?>
