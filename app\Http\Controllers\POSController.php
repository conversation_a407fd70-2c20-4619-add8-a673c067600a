<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TableLayout;
use App\Models\MenuCategory;
use App\Models\MenuItem;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Customer;
use App\Models\OrderSetting;
use App\Models\TaxSetting;
use App\Models\OrderDiscount;
use App\Models\OrderType;
use App\Models\PaymentMethod;
use App\Services\ProductService;
use App\Services\CustomerService;
use App\Services\CustomerSyncService;
use App\Services\OrderSyncService;
use App\Services\TransactionSyncService;
use Illuminate\Support\Facades\DB;

class POSController extends Controller
{
    private ProductService $productService;
    private CustomerService $customerService;
    private CustomerSyncService $customerSyncService;

    public function __construct(ProductService $productService, CustomerService $customerService, CustomerSyncService $customerSyncService)
    {
        $this->middleware(['auth', 'pos.access']);
        $this->productService = $productService;
        $this->customerService = $customerService;
        $this->customerSyncService = $customerSyncService;
    }

    /**
     * Display the main POS interface
     */
    public function index(Request $request)
    {
        // Check if this outlet needs table functionality
        $needsTables = $this->needsTableFunctionality();

        // Only load tables if needed
        $tables = $needsTables ? TableLayout::with('currentOrder')->get() : collect();

        // Clean up table statuses before displaying (only if tables are needed)
        if ($needsTables) {
            $this->cleanupTableStatuses();
        }

        $categories = $this->productService->getCategoriesWithItems();
        $customers = collect($this->customerService->getCustomers());

        // If a specific table is requested (from table-first workflow),
        // we need to ensure it's available in the dropdown even if it has an order
        $selectedTableId = $request->get('table');
        $selectedTable = null;

        if ($selectedTableId && $needsTables) {
            $selectedTable = TableLayout::find($selectedTableId);
        }

        // Get order settings for the view
        $orderSettings = [
            'tax_rate' => OrderSetting::get('tax_rate', 0.10),
            'tax_charging_method' => OrderSetting::get('tax_charging_method', 'customer_pays'),
            'service_charge_rate' => OrderSetting::get('service_charge_rate', 0.00),
            'auto_apply_tax' => OrderSetting::get('auto_apply_tax', true),
            'auto_apply_service_charge' => OrderSetting::get('auto_apply_service_charge', false),
            'currency_symbol' => OrderSetting::get('currency_symbol', '$'),
        ];

        // Get active order types and payment methods
        $orderTypes = OrderType::active()->ordered()->get();
        $paymentMethods = PaymentMethod::active()->ordered()->get();

        return view('pos.index', compact('tables', 'categories', 'customers', 'selectedTable', 'needsTables', 'orderSettings', 'orderTypes', 'paymentMethods'));
    }

    /**
     * Sync products from API
     */
    public function syncProducts()
    {
        try {
            // Check if user has API authentication
            if (!session()->has('api_token') || !session('api_authenticated')) {
                return response()->json([
                    'success' => false,
                    'message' => 'API authentication required. Please log in again.',
                    'error' => 'No API token in session'
                ], 401);
            }

            // Debug session data
            \Log::info('POS: Starting unified product sync', [
                'session_api_outlet_id' => session('api_outlet_id'),
                'session_api_outlet_category' => session('api_outlet_category'),
                'cache_pos_outlet_id' => \Cache::get('pos_outlet_id'),
                'session_has_api_token' => session()->has('api_token'),
                'session_api_authenticated' => session('api_authenticated')
            ]);

            // Perform full database sync for both products and customers
            \Log::info('POS: Starting product sync');
            $productSyncResult = $this->productService->forceSyncFromApi();
            \Log::info('POS: Product sync returned', ['result' => $productSyncResult]);

            \Log::info('POS: Starting customer sync');
            $customerSyncResult = $this->customerService->forceSyncFromApi();
            \Log::info('POS: Customer sync returned', ['result' => $customerSyncResult]);

            // User sync disabled - using API-only authentication
            \Log::info('POS: User sync skipped (API-only authentication)');
            $userSyncResult = [
                'success' => true,
                'message' => 'User sync skipped - using API-only authentication',
                'stats' => ['users_created' => 0, 'users_updated' => 0]
            ];

            // Determine overall success (user sync is optional, so we don't require it for overall success)
            $overallSuccess = $productSyncResult['success'] && $customerSyncResult['success'];
            $isPartialSuccess = $productSyncResult['success'] || $customerSyncResult['success'] || $userSyncResult['success'];

            \Log::info('POS: Sync results analysis', [
                'product_success' => $productSyncResult['success'],
                'customer_success' => $customerSyncResult['success'],
                'user_success' => $userSyncResult['success'],
                'overall_success' => $overallSuccess,
                'is_partial_success' => $isPartialSuccess
            ]);

            if ($overallSuccess) {
                \Log::info('POS: Full sync completed successfully', [
                    'product_stats' => $productSyncResult['stats'],
                    'customer_stats' => $customerSyncResult['stats'],
                    'user_stats' => $userSyncResult['stats']
                ]);

                // Return success immediately
                return response()->json([
                    'success' => true,
                    'message' => $this->formatFullSyncMessage($productSyncResult, $customerSyncResult, $userSyncResult),
                    'stats' => [
                        'products' => $productSyncResult['stats'],
                        'customers' => $customerSyncResult['stats'],
                        'users' => $userSyncResult['stats']
                    ],
                    'data_source' => 'api_synced_to_database'
                ]);
            } elseif ($isPartialSuccess) {
                \Log::warning('POS: Partial sync completed', [
                    'product_success' => $productSyncResult['success'],
                    'customer_success' => $customerSyncResult['success'],
                    'user_success' => $userSyncResult['success'],
                    'product_error' => $productSyncResult['error'] ?? null,
                    'customer_error' => $customerSyncResult['error'] ?? null,
                    'user_error' => $userSyncResult['error'] ?? null
                ]);

                return response()->json([
                    'success' => false,
                    'message' => $this->formatPartialSyncMessage($productSyncResult, $customerSyncResult, $userSyncResult),
                    'partial' => true,
                    'stats' => [
                        'products' => $productSyncResult['stats'] ?? null,
                        'customers' => $customerSyncResult['stats'] ?? null,
                        'users' => $userSyncResult['stats'] ?? null
                    ]
                ]);
            } else {
                \Log::error('POS: Full sync failed', [
                    'product_error' => $productSyncResult['error'],
                    'customer_error' => $customerSyncResult['error'],
                    'user_error' => $userSyncResult['error']
                ]);

                $errors = array_filter([
                    $productSyncResult['error'] ?? null,
                    $customerSyncResult['error'] ?? null,
                    $userSyncResult['error'] ?? null
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Sync failed: ' . implode(' | ', $errors),
                    'error' => [
                        'products' => $productSyncResult['error'],
                        'customers' => $customerSyncResult['error'],
                        'users' => $userSyncResult['error']
                    ]
                ], 500);
            }

        } catch (\Exception $e) {
            \Log::error('POS: Sync exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format sync result message
     */
    private function formatSyncMessage(array $syncResult): string
    {
        $stats = $syncResult['stats'];
        $message = 'Products synchronized successfully! ';
        $message .= "Created {$stats['categories_created']} categories, ";
        $message .= "updated {$stats['categories_updated']} categories, ";
        $message .= "created {$stats['items_created']} items, ";
        $message .= "updated {$stats['items_updated']} items.";

        return $message;
    }

    /**
     * Format message for full sync (products + customers + users)
     */
    private function formatFullSyncMessage(array $productResult, array $customerResult, array $userResult = null): string
    {
        $productStats = $productResult['stats'];
        $customerStats = $customerResult['stats'];

        $message = 'All data synchronized successfully (complete replacement)! ';
        $message .= "Products: Created {$productStats['categories_created']} categories, ";
        $message .= "created {$productStats['items_created']} items. ";
        $message .= "Customers: Created {$customerStats['customers_created']} customers. ";

        // Include user stats if provided
        if ($userResult && isset($userResult['stats'])) {
            $userStats = $userResult['stats'];
            $message .= "Users: Created {$userStats['users_created']} users. ";
        }

        return $message;
    }

    /**
     * Format message for partial sync
     */
    private function formatPartialSyncMessage(array $productResult, array $customerResult, array $userResult = null): string
    {
        $messages = [];

        if ($productResult['success']) {
            $stats = $productResult['stats'];
            $messages[] = "Products: Created {$stats['categories_created']} categories, created {$stats['items_created']} items (complete replacement)";
        } else {
            $messages[] = "Products: Failed - " . ($productResult['error'] ?? 'Unknown error');
        }

        if ($customerResult['success']) {
            $stats = $customerResult['stats'];
            $messages[] = "Customers: Created {$stats['customers_created']} customers (complete replacement)";
        } else {
            $messages[] = "Customers: Failed - " . ($customerResult['error'] ?? 'Unknown error');
        }

        // Include user results if provided
        if ($userResult) {
            if ($userResult['success']) {
                $stats = $userResult['stats'];
                $messages[] = "Users: Created {$stats['users_created']} users (complete replacement)";
            } else {
                $messages[] = "Users: Failed - " . ($userResult['error'] ?? 'Unknown error');
            }
        }

        return 'Partial sync completed. ' . implode('. ', $messages) . '.';
    }



    /**
     * Get API user information for order creation
     */
    private function getCashierInfoForOrder()
    {
        // Get API user data from session
        $apiUser = session('api_user');

        if (!$apiUser) {
            \Log::error('POS: No API user data in session', [
                'session_data' => session()->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'API authentication required. Please log in again.',
                'redirect' => route('login')
            ], 401);
        }

        // Extract user ID from API user data
        $apiUserId = $apiUser['id'] ?? null;

        if (!$apiUserId) {
            \Log::error('POS: API user data missing ID field', [
                'api_user' => $apiUser
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Invalid API user data. Please log in again.',
                'redirect' => route('login')
            ], 401);
        }

        return [
            'api_user_id' => $apiUserId,
            'cashier_name' => $apiUser['name'] ?? 'Unknown Cashier',
            'cashier_email' => $apiUser['email'] ?? null
        ];
    }

    /**
     * Clean up inconsistent table statuses
     */
    private function cleanupTableStatuses()
    {
        // Get all tables
        $tables = TableLayout::all();

        foreach ($tables as $table) {
            $hasActiveOrder = $table->hasActiveOrder();

            // If table is marked as occupied but has no active orders, mark it as available
            if ($table->status === 'Occupied' && !$hasActiveOrder) {
                $table->update(['status' => 'Available']);
            }
            // If table is marked as available but has active orders, mark it as occupied
            elseif ($table->status === 'Available' && $hasActiveOrder) {
                $table->update(['status' => 'Occupied']);
            }
        }
    }

    /**
     * Create a new order for a table
     */
    public function createOrder(Request $request)
    {
        $request->validate([
            'table_id' => 'nullable|exists:table_layouts,id',
        ]);

        $table = null;
        if ($request->table_id) {
            $table = TableLayout::findOrFail($request->table_id);

            // Check if table already has an active order
            if ($table->currentOrder) {
                return redirect()->route('pos.ongoing-orders')
                               ->with('info', 'Table already has an active order. You can find it in the ongoing orders list.');
            }
        }

        // Get default walk-in customer
        $customer = Customer::where('name', 'Walk-in')->first();

        // Get cashier info for order creation
        $cashierInfo = $this->getCashierInfoForOrder();
        if ($cashierInfo instanceof \Illuminate\Http\JsonResponse) {
            return $cashierInfo; // Return error response
        }

        // Create new order
        $order = Order::create([
            'api_user_id' => $cashierInfo['api_user_id'],
            'cashier_name' => $cashierInfo['cashier_name'],
            'cashier_email' => $cashierInfo['cashier_email'],
            'customer_id' => $customer->id,
            'table_id' => $table ? $table->id : null,
            'status' => 'Pending',
            'total_amount' => 0,
            'tax_amount' => 0,
        ]);

        // Update table status if table is selected
        if ($table) {
            $table->update(['status' => 'Occupied']);
        }

        $message = $table
            ? 'Order created successfully for ' . $table->name . '. You can find it in the ongoing orders list.'
            : 'Order created successfully. You can find it in the ongoing orders list.';

        return redirect()->route('pos.ongoing-orders')->with('success', $message);
    }

    /**
     * Create a new order for a table with customer data
     */
    public function createOrderWithCustomer(Request $request)
    {
        // Make table_id required only for dine_in orders
        $validationRules = [
            'customer_id' => 'nullable|exists:customers,id',
            'use_walk_in' => 'nullable|boolean',
            'order_type' => 'required|in:dine_in,take_away,delivery',
            'discount' => 'nullable|array',
            'discount.type' => 'nullable|in:none,percentage,fixed,coupon,points',
            'discount.value' => 'nullable|numeric',
        ];

        if ($request->order_type === 'dine_in') {
            $validationRules['table_id'] = 'required|exists:table_layouts,id';
        } else {
            $validationRules['table_id'] = 'nullable|exists:table_layouts,id';
        }

        $request->validate($validationRules);

        $table = null;
        if ($request->table_id) {
            $table = TableLayout::findOrFail($request->table_id);

            // Check if table already has an active order
            if ($table->currentOrder) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table already has an active order. You can find it in the ongoing orders list.',
                    'redirect' => route('pos.ongoing-orders')
                ]);
            }
        }

        try {
            DB::transaction(function () use ($request, $table, &$order) {
                // Handle customer selection
                if ($request->use_walk_in || !$request->customer_id) {
                    // Create or get a guest customer
                    $customer = Customer::firstOrCreate(
                        ['name' => 'Guest Customer', 'phone' => '************'],
                        ['email' => null, 'address' => null]
                    );
                } else {
                    // Use selected customer
                    $customer = Customer::findOrFail($request->customer_id);
                }

                // Create new order
                // Get cashier info for order creation
                $cashierInfo = $this->getCashierInfoForOrder();
                if ($cashierInfo instanceof \Illuminate\Http\JsonResponse) {
                    return $cashierInfo; // Return error response
                }

                $order = Order::create([
                    'api_user_id' => $cashierInfo['api_user_id'],
                    'cashier_name' => $cashierInfo['cashier_name'],
                    'cashier_email' => $cashierInfo['cashier_email'],
                    'customer_id' => $customer->id,
                    'table_id' => $table ? $table->id : null,
                    'status' => 'Pending',
                    'order_type' => $request->order_type,
                    'total_amount' => 0,
                    'tax_amount' => 0,
                ]);

                // Apply discount if provided
                if ($request->has('discount') && $request->discount['type'] !== 'none') {
                    $discount = $request->discount;

                    // Apply discount to order (subtotal will be 0 initially, but structure is set up)
                    $order->applyDiscount(
                        $discount['type'],
                        $discount['value'] ?? 0,
                        null, // No coupon code for table orders
                        0     // No points for table orders
                    );
                }

                // Update table status if table is selected
                if ($table) {
                    $table->update(['status' => 'Occupied']);
                }
            });

            $message = $table
                ? 'Order created successfully for ' . $table->name . '. You can now add menu items.'
                : 'Order created successfully. You can now add menu items.';
            
            $redirect = $table
                ? route('pos.index') . '?table=' . $table->id . '&view=menu&success=' . urlencode($message)
                : route('pos.index') . '?view=menu&success=' . urlencode($message);

            return response()->json([
                'success' => true,
                'message' => $message,
                'redirect' => $redirect
            ]);
        } catch (\Exception $e) {
            \Log::error('POS: Order creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
                'session_api_user' => session('api_user'),
                'session_api_authenticated' => session('api_authenticated')
            ]);

            // Force JSON response even if there's a view error
            return response()->json([
                'success' => false,
                'message' => 'Error creating order: ' . $e->getMessage()
            ], 500)->header('Content-Type', 'application/json');
        }
    }

    /**
     * Create a new order with menu items first (menu-first workflow)
     */
    public function createMenuOrder(Request $request)
    {
        $request->validate([
            'table_id' => 'nullable|exists:table_layouts,id',
            'items' => 'required|array|min:1',
            'items.*.menu_item_id' => 'required|exists:menu_items,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.notes' => 'nullable|string|max:255',
        ]);

        $table = null;
        if ($request->table_id) {
            $table = TableLayout::findOrFail($request->table_id);

            // Check if table already has an active order
            if ($table->currentOrder) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table already has an active order. You can find it in the ongoing orders list.',
                    'redirect' => route('pos.ongoing-orders')
                ]);
            }
        }

        // Get default walk-in customer
        $customer = Customer::where('name', 'Walk-in')->first();

        // Get cashier info for order creation
        $cashierInfo = $this->getCashierInfoForOrder();
        if ($cashierInfo instanceof \Illuminate\Http\JsonResponse) {
            return $cashierInfo; // Return error response
        }

        DB::transaction(function () use ($request, $table, $customer, $cashierInfo, &$order) {
            // Create new order
            $order = Order::create([
                'api_user_id' => $cashierInfo['api_user_id'],
                'cashier_name' => $cashierInfo['cashier_name'],
                'cashier_email' => $cashierInfo['cashier_email'],
                'customer_id' => $customer->id,
                'table_id' => $table ? $table->id : null,
                'status' => 'Pending',
                'total_amount' => 0,
                'tax_amount' => 0,
            ]);

            // Add all items to the order
            foreach ($request->items as $item) {
                $menuItem = $this->productService->getMenuItem($item['menu_item_id']);

                if (!$menuItem) {
                    throw new \Exception("Menu item not found: " . $item['menu_item_id']);
                }

                OrderItem::create([
                    'order_id' => $order->id,
                    'menu_item_id' => $item['menu_item_id'],
                    'quantity' => $item['quantity'],
                    'price_at_time' => $menuItem->price,
                    'notes' => $item['notes'] ?? null,
                ]);
            }

            // Update order total
            $this->updateOrderTotal($order);

            // Update table status if table is selected
            if ($table) {
                $table->update(['status' => 'Occupied']);
            }
        });

        $message = $table
            ? 'Order created successfully for ' . $table->name . '. You can find it in the ongoing orders list.'
            : 'Order created successfully. You can find it in the ongoing orders list.';

        return response()->json([
            'success' => true,
            'message' => $message,
            'redirect' => route('pos.ongoing-orders')
        ]);
    }

    /**
     * Create a new order with menu items first and customer data (menu-first workflow)
     */
    public function createMenuOrderWithCustomer(Request $request)
    {
        try {
            // Get order type to determine validation requirements
            $orderType = OrderType::where('slug', $request->order_type)->first();
            
            // Basic validation rules
            $validationRules = [
                'items' => 'required|array|min:1',
                'items.*.menu_item_id' => 'required|integer',
                'items.*.quantity' => 'required|integer|min:1',
                'customer_id' => 'required|integer',
                'order_type' => 'required|string|exists:order_types,slug',
            ];

            // Add conditional validation based on order type requirements
            if ($orderType) {
                if ($orderType->requires_table) {
                    $validationRules['table_id'] = 'required|exists:table_layouts,id';
                } else {
                    $validationRules['table_id'] = 'nullable|exists:table_layouts,id';
                }

                if ($orderType->requires_party_size) {
                    $validationRules['party_size'] = 'required|integer|min:1|max:20';
                } else {
                    $validationRules['party_size'] = 'nullable|integer|min:1|max:20';
                }
            } else {
                // Fallback validation if order type not found
                $validationRules['table_id'] = 'nullable|exists:table_layouts,id';
                $validationRules['party_size'] = 'nullable|integer|min:1|max:20';
            }

            $request->validate($validationRules, [
                'table_id.required' => 'Please select a table for this order type.',
                'table_id.exists' => 'The selected table is not valid.',
                'order_type.exists' => 'The selected order type is not valid.',
            ]);

            // Handle table selection based on order type requirements
            $table = null;
            if ($orderType && $orderType->requires_table && $request->table_id) {
                $table = TableLayout::find($request->table_id);

                // Check if table already has an active order
                if ($table && $table->currentOrder) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Table already has an active order. Please select a different table.',
                    ], 422);
                }
            }

            // Handle customer selection - customer is now required
            if (!$request->customer_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please select a customer for this order.',
                    'errors' => ['customer_id' => ['Customer selection is required.']]
                ], 422);
            }

            $customer = Customer::find($request->customer_id);
            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Selected customer not found.',
                    'errors' => ['customer_id' => ['Selected customer is invalid.']]
                ], 422);
            }

            // Get cashier info from API session
            $apiUser = session('api_user');
            $cashierName = $apiUser['name'] ?? 'Unknown Cashier';
            $cashierEmail = $apiUser['email'] ?? null;
            $apiUserId = $apiUser['id'] ?? null;

            // Create order
            $order = Order::create([
                'api_user_id' => $apiUserId,
                'cashier_name' => $cashierName,
                'cashier_email' => $cashierEmail,
                'customer_id' => $customer->id,
                'table_id' => $table ? $table->id : null,
                'status' => 'Pending',  
                'order_type' => $request->order_type,
                'party_size' => $request->party_size,
                'total_amount' => 0,
                'tax_amount' => 0,
            ]);

            // Add items
            $total = 0;
            foreach ($request->items as $item) {
                $menuItem = MenuItem::find($item['menu_item_id']);
                if ($menuItem) {
                    try {
                        $orderItem = OrderItem::create([
                            'order_id' => $order->id,
                            'menu_item_id' => $item['menu_item_id'],
                            'quantity' => $item['quantity'],
                            'price_at_time' => $menuItem->price,
                            'notes' => $item['notes'] ?? null,
                        ]);

                        $total += $menuItem->price * $item['quantity'];

                        \Log::info('POS: Order item created successfully', [
                            'order_item_id' => $orderItem->id,
                            'menu_item_name' => $menuItem->name,
                            'quantity' => $item['quantity']
                        ]);
                    } catch (\Exception $e) {
                        \Log::error('POS: Failed to create order item', [
                            'menu_item_id' => $item['menu_item_id'],
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);

                        throw new \Exception("Failed to add item '{$menuItem->name}' to order: " . $e->getMessage());
                    }
                } else {
                    $availableItems = MenuItem::pluck('name', 'id')->toArray();
                    \Log::error('POS: Menu item not found', [
                        'requested_menu_item_id' => $item['menu_item_id'],
                        'available_menu_items' => $availableItems,
                        'total_available_items' => count($availableItems)
                    ]);

                    $availableItemsList = empty($availableItems)
                        ? 'No menu items available. Please sync products first.'
                        : 'Available items: ' . implode(', ', array_map(fn($id, $name) => "ID $id: $name", array_keys($availableItems), $availableItems));

                    throw new \Exception("Menu item with ID {$item['menu_item_id']} not found. {$availableItemsList}");
                }
            }

            // Update total
            $order->update(['total_amount' => $total]);

            // Update table status if dine_in
            if ($table) {
                $table->update(['status' => 'Occupied']);
            }

            return response()->json([
                'success' => true,
                'message' => $table
                    ? 'Order created successfully for ' . $table->name . '!'
                    : 'Order created successfully!',
                'order_id' => $order->id,
                'table_name' => $table ? $table->name : null
            ]);

        } catch (\Exception $e) {
            \Log::error('Order creation error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error creating order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add a new customer
     */
    public function addCustomer(Request $request)
    {
        $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'customer_date_of_birth' => 'nullable|date|before:today',
            'customer_gender' => 'nullable|in:male,female,other',
            'customer_segment' => 'nullable|string|max:255',
            'customer_address' => 'nullable|string|max:500',
            'customer_notes' => 'nullable|string|max:1000',
        ]);

        try {
            // Create or find customer
            $customer = Customer::firstOrCreate(
                [
                    'name' => $request->customer_name,
                    'phone' => $request->customer_phone,
                ],
                [
                    'email' => $request->customer_email,
                    'date_of_birth' => $request->customer_date_of_birth,
                    'gender' => $request->customer_gender,
                    'customer_segment' => $request->customer_segment,
                    'address' => $request->customer_address,
                    'notes' => $request->customer_notes,
                    'active_status' => true,
                ]
            );

            // Update customer info if it already exists but data has changed
            if (!$customer->wasRecentlyCreated) {
                $customer->update([
                    'email' => $request->customer_email ?: $customer->email,
                    'date_of_birth' => $request->customer_date_of_birth ?: $customer->date_of_birth,
                    'gender' => $request->customer_gender ?: $customer->gender,
                    'customer_segment' => $request->customer_segment ?: $customer->customer_segment,
                    'address' => $request->customer_address ?: $customer->address,
                    'notes' => $request->customer_notes ?: $customer->notes,
                ]);
            }

            // Try to sync to API if customer was newly created or updated
            if ($customer->wasRecentlyCreated || $customer->wasChanged()) {
                $syncResult = $this->customerSyncService->syncCustomer($customer);

                if (!$syncResult['success']) {
                    // Log the sync failure but don't fail the customer creation
                    \Log::warning('POS: Customer sync to API failed', [
                        'customer_id' => $customer->id,
                        'error' => $syncResult['error'] ?? 'Unknown error'
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Customer added successfully.',
                'customer' => $customer
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error adding customer: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show order details and allow adding items
     */
    public function showOrder(Order $order)
    {
        $order->load(['orderItems.menuItem', 'table', 'customer']);
        $categories = $this->productService->getCategoriesWithItems();

        // Get order settings for the view
        $orderSettings = [
            'tax_rate' => OrderSetting::get('tax_rate', 0.10),
            'tax_charging_method' => OrderSetting::get('tax_charging_method', 'customer_pays'),
            'service_charge_rate' => OrderSetting::get('service_charge_rate', 0.00),
            'auto_apply_tax' => OrderSetting::get('auto_apply_tax', true),
            'auto_apply_service_charge' => OrderSetting::get('auto_apply_service_charge', false),
            'currency_symbol' => OrderSetting::get('currency_symbol', '$'),
        ];

        return view('pos.order', compact('order', 'categories', 'orderSettings'));
    }

    /**
     * Add item to order
     */
    public function addItem(Request $request, Order $order)
    {
        $request->validate([
            'menu_item_id' => 'required',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:255',
        ]);

        $menuItem = $this->productService->getMenuItem($request->menu_item_id);

        if (!$menuItem) {
            return response()->json([
                'success' => false,
                'message' => 'Menu item not found'
            ], 404);
        }

        // Check if item already exists in order
        $existingItem = $order->orderItems()
                             ->where('menu_item_id', $request->menu_item_id)
                             ->where('notes', $request->notes ?? '')
                             ->first();

        if ($existingItem) {
            // Update quantity
            $existingItem->update([
                'quantity' => $existingItem->quantity + $request->quantity
            ]);
        } else {
            // Create new order item
            OrderItem::create([
                'order_id' => $order->id,
                'menu_item_id' => $request->menu_item_id,
                'quantity' => $request->quantity,
                'price_at_time' => $menuItem->price,
                'notes' => $request->notes,
            ]);
        }

        // Update order total
        $this->updateOrderTotal($order);

        return response()->json([
            'success' => true,
            'message' => 'Item added to order',
            'order_total' => $order->fresh()->total_amount
        ]);
    }

    /**
     * Update item quantity in order
     */
    public function updateItem(Request $request, Order $order, OrderItem $orderItem)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1',
        ]);

        $orderItem->update(['quantity' => $request->quantity]);
        $this->updateOrderTotal($order);

        return response()->json([
            'success' => true,
            'message' => 'Item updated',
            'order_total' => $order->fresh()->total_amount
        ]);
    }

    /**
     * Remove item from order
     */
    public function removeItem(Order $order, OrderItem $orderItem)
    {
        $orderItem->delete();
        $this->updateOrderTotal($order);

        return response()->json([
            'success' => true,
            'message' => 'Item removed',
            'order_total' => $order->fresh()->total_amount
        ]);
    }

    /**
     * Update order total amount
     */
    private function updateOrderTotal(Order $order)
    {
        $subtotal = $order->orderItems->sum(function ($item) {
            return $item->quantity * $item->price_at_time;
        });

        // Recalculate all discount amounts based on new subtotal
        $order->recalculateDiscounts();

        // Get total discount amount from all active discounts
        $totalDiscountAmount = $order->getTotalDiscountAmount();

        // Calculate discounted subtotal
        $discountedSubtotal = max(0, $subtotal - $totalDiscountAmount);

        // Get tax and service charge rates from settings
        $taxRate = OrderSetting::get('tax_rate', 0.10);
        $taxChargingMethod = OrderSetting::get('tax_charging_method', 'customer_pays');
        $serviceChargeRate = OrderSetting::get('service_charge_rate', 0.00);
        $autoApplyTax = OrderSetting::get('auto_apply_tax', true);
        $autoApplyService = OrderSetting::get('auto_apply_service_charge', false);

        // Calculate tax amount based on charging method
        $taxAmount = 0;
        if ($autoApplyTax) {
            if ($taxChargingMethod === 'customer_pays') {
                // Customer pays tax - add tax to the final amount
                $taxAmount = $discountedSubtotal * $taxRate;
            } else {
                // Business absorbs tax - tax is included in the price, so no additional tax
                $taxAmount = 0;
            }
        }

        // Calculate service charge on discounted subtotal
        $serviceChargeAmount = $autoApplyService ? ($discountedSubtotal * $serviceChargeRate) : 0;

        // Calculate total
        $total = $discountedSubtotal + $taxAmount + $serviceChargeAmount;

        $order->update([
            'subtotal_amount' => $subtotal,
            'discount_amount' => $totalDiscountAmount,
            'tax_amount' => $taxAmount,
            'service_charge_amount' => $serviceChargeAmount,
            'total_amount' => $total,
        ]);
    }

    /**
     * Hold an order (save for later)
     */
    public function holdOrder(Order $order)
    {
        // Order is already saved, just redirect to ongoing orders
        return redirect()->route('pos.ongoing-orders')
                        ->with('success', 'Order held successfully. You can find it in the ongoing orders list.');
    }

    /**
     * Cancel an order
     */
    public function cancelOrder(Request $request, Order $order)
    {
        $request->validate([
            'reason' => 'nullable|string|max:255',
        ]);

        DB::transaction(function () use ($order) {
            // Update order status
            $order->update(['status' => 'Cancelled']);

            // Free up the table if it exists
            if ($order->table) {
                $order->table->update(['status' => 'Available']);
            }
        });

        return redirect()->route('pos.ongoing-orders')
                        ->with('success', 'Order cancelled successfully.');
    }

    /**
     * Complete an order (mark as ready for payment)
     */
    public function completeOrder(Order $order)
    {
        DB::transaction(function () use ($order) {
            // Update order status to completed
            $order->update(['status' => 'Completed']);

            // Free up the table since order is completed
            if ($order->table) {
                $order->table->update(['status' => 'Available']);
            }
        });

        return redirect()->route('pos.payment', $order)
                        ->with('success', 'Order completed. Ready for payment.');
    }



    /**
     * Show ongoing orders (Pending, In Progress, etc.)
     */
    public function ongoingOrders(Request $request)
    {
        // Clean up table statuses before displaying
        $this->cleanupTableStatuses();

        $query = Order::with(['table', 'customer', 'orderItems.menuItem'])
            ->whereIn('status', ['Pending', 'In Progress', 'Preparing', 'Ready', 'Completed']);

        // Add search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('customer', function($customerQuery) use ($search) {
                    $customerQuery->where('name', 'LIKE', "%{$search}%");
                })
                ->orWhereHas('table', function($tableQuery) use ($search) {
                    $tableQuery->where('name', 'LIKE', "%{$search}%");
                })
                ->orWhere('id', 'LIKE', "%{$search}%");
            });
        }

        $orders = $query->latest()->paginate(15);
        $totalOngoing = Order::whereIn('status', ['Pending', 'In Progress', 'Preparing', 'Ready', 'Completed'])->count();

        // Get order settings for the view
        $orderSettings = [
            'tax_rate' => OrderSetting::get('tax_rate', 0.10),
            'tax_charging_method' => OrderSetting::get('tax_charging_method', 'customer_pays'),
            'service_charge_rate' => OrderSetting::get('service_charge_rate', 0.00),
            'auto_apply_tax' => OrderSetting::get('auto_apply_tax', true),
            'auto_apply_service_charge' => OrderSetting::get('auto_apply_service_charge', false),
            'currency_symbol' => OrderSetting::get('currency_symbol', '$'),
        ];

        return view('pos.ongoing-orders', compact('orders', 'totalOngoing', 'orderSettings'));
    }

    /**
     * Show order history (all orders)
     */
    public function orderHistory(Request $request)
    {
        $query = Order::with(['table', 'customer', 'orderItems.menuItem']);

        // Filter by status if specified
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Add search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('customer', function($customerQuery) use ($search) {
                    $customerQuery->where('name', 'LIKE', "%{$search}%");
                })
                ->orWhereHas('table', function($tableQuery) use ($search) {
                    $tableQuery->where('name', 'LIKE', "%{$search}%");
                })
                ->orWhere('id', 'LIKE', "%{$search}%");
            });
        }

        $orders = $query->latest()->paginate(20);

        // Get statistics for the dashboard
        $stats = [
            'total' => Order::count(),
            'completed' => Order::where('status', 'Completed')->count(),
            'cancelled' => Order::where('status', 'Cancelled')->count(),
            'paid' => Order::where('status', 'Paid')->count(),
        ];

        // Get order settings for the view
        $orderSettings = [
            'tax_rate' => OrderSetting::get('tax_rate', 0.10),
            'tax_charging_method' => OrderSetting::get('tax_charging_method', 'customer_pays'),
            'service_charge_rate' => OrderSetting::get('service_charge_rate', 0.00),
            'auto_apply_tax' => OrderSetting::get('auto_apply_tax', true),
            'auto_apply_service_charge' => OrderSetting::get('auto_apply_service_charge', false),
            'currency_symbol' => OrderSetting::get('currency_symbol', '$'),
        ];

        return view('pos.order-history', compact('orders', 'stats', 'orderSettings'));
    }

    /**
     * Show order details
     */
    public function orderDetails(Order $order)
    {
        $order->load(['orderItems.menuItem', 'table', 'customer', 'payments']);

        // If it's an AJAX request, return JSON data for dynamic updates
        if (request()->ajax()) {
            $orderItemsHtml = '';

            if ($order->orderItems->count() > 0) {
                foreach ($order->orderItems as $orderItem) {
                    $orderItemsHtml .= '<div class="flex justify-between items-center border-b pb-2" data-item-id="' . $orderItem->id . '">';
                    $orderItemsHtml .= '<div class="flex-1">';

                    // Handle case where menu item might be null (deleted menu item)
                    if ($orderItem->menuItem) {
                        $orderItemsHtml .= '<h5 class="font-medium text-sm">' . $orderItem->menuItem->name . '</h5>';
                    } else {
                        $orderItemsHtml .= '<h5 class="font-medium text-sm text-red-600">[Deleted Item - ID: ' . $orderItem->menu_item_id . ']</h5>';
                        \Log::warning('Order item references deleted menu item', [
                            'order_item_id' => $orderItem->id,
                            'menu_item_id' => $orderItem->menu_item_id,
                            'order_id' => $order->id
                        ]);
                    }

                    if ($orderItem->notes) {
                        $orderItemsHtml .= '<p class="text-xs text-gray-500">' . $orderItem->notes . '</p>';
                    }
                    $orderItemsHtml .= '<p class="text-sm text-gray-600">$' . number_format($orderItem->price_at_time, 2) . ' each</p>';
                    $orderItemsHtml .= '</div>';
                    $orderItemsHtml .= '<div class="flex items-center space-x-2">';
                    $orderItemsHtml .= '<button onclick="updateQuantity(' . $orderItem->id . ', ' . ($orderItem->quantity - 1) . ')" class="bg-gray-200 hover:bg-gray-300 text-gray-700 w-6 h-6 rounded text-xs">-</button>';
                    $orderItemsHtml .= '<span class="quantity-' . $orderItem->id . ' font-medium">' . $orderItem->quantity . '</span>';
                    $orderItemsHtml .= '<button onclick="updateQuantity(' . $orderItem->id . ', ' . ($orderItem->quantity + 1) . ')" class="bg-gray-200 hover:bg-gray-300 text-gray-700 w-6 h-6 rounded text-xs">+</button>';
                    $orderItemsHtml .= '<button onclick="removeItem(' . $orderItem->id . ')" class="bg-red-500 hover:bg-red-600 text-white w-6 h-6 rounded text-xs">×</button>';
                    $orderItemsHtml .= '</div>';
                    $orderItemsHtml .= '</div>';
                }
            } else {
                $orderItemsHtml = '<p class="text-gray-500 text-center py-4">No items in order</p>';
            }

            return response()->json([
                'success' => true,
                'orderItemsHtml' => $orderItemsHtml,
                'subtotal' => number_format($order->subtotal_amount ?: $order->subtotal, 2),
                'discount' => number_format($order->discount_amount, 2),
                'tax' => number_format($order->tax_amount, 2),
                'total' => number_format($order->total_amount, 2),
                'hasDiscount' => $order->discount_amount > 0
            ]);
        }

        // Get order settings for the view
        $orderSettings = [
            'tax_rate' => OrderSetting::get('tax_rate', 0.10),
            'tax_charging_method' => OrderSetting::get('tax_charging_method', 'customer_pays'),
            'service_charge_rate' => OrderSetting::get('service_charge_rate', 0.00),
            'auto_apply_tax' => OrderSetting::get('auto_apply_tax', true),
            'auto_apply_service_charge' => OrderSetting::get('auto_apply_service_charge', false),
            'currency_symbol' => OrderSetting::get('currency_symbol', '$'),
        ];

        return view('pos.order-details', compact('order', 'orderSettings'));
    }

    /**
     * Print kitchen ticket
     */
    public function printKitchen(Order $order)
    {
        $order->load(['orderItems.menuItem', 'table']);

        return view('pos.kitchen-ticket', compact('order'));
    }

    /**
     * Print customer receipt
     */
    public function printReceipt(Order $order)
    {
        $order->load(['orderItems.menuItem', 'table', 'payments']);

        // Get order settings for the view
        $orderSettings = [
            'tax_rate' => OrderSetting::get('tax_rate', 0.10),
            'tax_charging_method' => OrderSetting::get('tax_charging_method', 'customer_pays'),
            'service_charge_rate' => OrderSetting::get('service_charge_rate', 0.00),
            'auto_apply_tax' => OrderSetting::get('auto_apply_tax', true),
            'auto_apply_service_charge' => OrderSetting::get('auto_apply_service_charge', false),
            'currency_symbol' => OrderSetting::get('currency_symbol', '$'),
        ];

        return view('pos.receipt', compact('order', 'orderSettings'));
    }

    /**
     * Validate coupon code
     */
    public function validateCoupon(Request $request)
    {
        $request->validate([
            'coupon_code' => 'required|string',
            'order_amount' => 'required|numeric|min:0',
        ]);

        $coupon = \App\Models\Coupon::where('code', $request->coupon_code)->first();

        if (!$coupon) {
            return response()->json([
                'success' => false,
                'message' => 'Coupon code not found.'
            ]);
        }

        if (!$coupon->isValid($request->order_amount)) {
            return response()->json([
                'success' => false,
                'message' => 'Coupon is not valid or has expired.'
            ]);
        }

        $discountAmount = $coupon->calculateDiscount($request->order_amount);

        return response()->json([
            'success' => true,
            'coupon' => $coupon,
            'discount_amount' => $discountAmount
        ]);
    }

    /**
     * Get customer points
     */
    public function getCustomerPoints($customerId)
    {
        $customer = \App\Models\Customer::find($customerId);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found.'
            ]);
        }

        return response()->json([
            'success' => true,
            'points' => $customer->points ?? 0
        ]);
    }

    /**
     * Get table information
     */
    public function getTableInfo($tableId)
    {
        $table = TableLayout::find($tableId);

        if (!$table) {
            return response()->json([
                'success' => false,
                'message' => 'Table not found.'
            ]);
        }

        return response()->json([
            'success' => true,
            'table' => [
                'id' => $table->id,
                'name' => $table->name,
                'status' => $table->status,
                'has_order' => $table->currentOrder ? true : false
            ]
        ]);
    }

    /**
     * Get existing order details for a table
     */
    public function getTableOrder($tableId)
    {
        $table = TableLayout::with(['currentOrder.orderItems.menuItem', 'currentOrder.customer'])->find($tableId);

        if (!$table || !$table->currentOrder) {
            return response()->json([
                'success' => false,
                'message' => 'No active order found for this table.'
            ]);
        }

        $order = $table->currentOrder;

        // Format order items for the cart
        $cartItems = $order->orderItems->map(function ($orderItem) {
            // Handle case where menu item might be null (deleted menu item)
            if ($orderItem->menuItem) {
                return [
                    'id' => $orderItem->menuItem->id,
                    'name' => $orderItem->menuItem->name,
                    'price' => (float) $orderItem->price_at_time,
                    'quantity' => $orderItem->quantity,
                    'notes' => $orderItem->notes ?? '',
                    'order_item_id' => $orderItem->id // Include for updating existing items
                ];
            } else {
                // Return placeholder data for deleted menu items
                \Log::warning('Loading order with deleted menu item', [
                    'order_item_id' => $orderItem->id,
                    'menu_item_id' => $orderItem->menu_item_id,
                    'order_id' => $orderItem->order_id
                ]);

                return [
                    'id' => $orderItem->menu_item_id, // Use the original menu item ID
                    'name' => '[Deleted Item - ID: ' . $orderItem->menu_item_id . ']',
                    'price' => (float) $orderItem->price_at_time,
                    'quantity' => $orderItem->quantity,
                    'notes' => $orderItem->notes ?? '',
                    'order_item_id' => $orderItem->id,
                    'is_deleted' => true // Flag to indicate this is a deleted item
                ];
            }
        });

        return response()->json([
            'success' => true,
            'order' => [
                'id' => $order->id,
                'table_id' => $table->id,
                'table_name' => $table->name,
                'customer_id' => $order->customer_id,
                'customer_name' => $order->customer ? $order->customer->name : 'Guest Customer',
                'order_type' => $order->order_type,
                'status' => $order->status,
                'subtotal' => (float) $order->subtotal,
                'tax' => (float) $order->tax,
                'total' => (float) $order->total,
                'discount_amount' => (float) ($order->discount_amount ?? 0),
                'items' => $cartItems
            ]
        ]);
    }

    /**
     * Apply discount to existing order
     */
    public function applyOrderDiscount(Request $request, Order $order)
    {
        $request->validate([
            'discount_type' => 'required|in:percentage,fixed,coupon,points',
            'discount_value' => 'required|numeric|min:0',
            'coupon_code' => 'nullable|string',
            'points_used' => 'nullable|integer|min:0',
        ]);

        try {
            DB::transaction(function () use ($request, $order) {
                // Handle points discount - deduct points from customer
                if ($request->discount_type === 'points' && $request->points_used > 0) {
                    $customer = $order->customer;
                    if (!$customer->usePoints($request->points_used)) {
                        throw new \Exception('Not enough points available.');
                    }
                }

                // Handle coupon discount - mark coupon as used
                if ($request->discount_type === 'coupon' && !empty($request->coupon_code)) {
                    $coupon = \App\Models\Coupon::where('code', $request->coupon_code)->first();
                    if ($coupon) {
                        $coupon->markAsUsed();
                    }
                }

                // Apply discount to order
                $order->applyDiscount(
                    $request->discount_type,
                    $request->discount_value,
                    $request->coupon_code,
                    $request->points_used ?? 0
                );
            });

            return response()->json([
                'success' => true,
                'message' => 'Discount applied successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error applying discount: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove discount from existing order
     */
    public function removeOrderDiscount(Order $order)
    {
        try {
            DB::transaction(function () use ($order) {
                // If points were used, refund them to customer
                if ($order->discount_type === 'points' && $order->points_used > 0) {
                    $customer = $order->customer;
                    $customer->addPoints($order->points_used);
                }

                // Remove discount from order
                $order->update([
                    'discount_type' => 'none',
                    'discount_value' => 0,
                    'discount_amount' => 0,
                    'coupon_code' => null,
                    'points_used' => 0,
                ]);

                // Recalculate total
                $order->recalculateTotal();
            });

            return response()->json([
                'success' => true,
                'message' => 'Discount removed successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error removing discount: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update an existing order with new items
     */
    public function updateOrder(Request $request, $orderId)
    {
        try {
            $request->validate([
                'items' => 'required|array|min:1',
                'items.*.menu_item_id' => 'required|exists:menu_items,id',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.notes' => 'nullable|string|max:255',
                'customer_id' => 'nullable|exists:customers,id',
                'order_type' => 'required|in:dine_in,take_away,delivery',
                'party_size' => 'required|integer|min:1|max:20',
            ], [
                'items.required' => 'Please add at least one item to the order.',
                'items.min' => 'Please add at least one item to the order.',
                'items.*.menu_item_id.required' => 'Each item must have a valid menu item.',
                'items.*.menu_item_id.exists' => 'One or more selected items are not available.',
                'items.*.quantity.required' => 'Each item must have a quantity.',
                'items.*.quantity.integer' => 'Item quantities must be whole numbers.',
                'items.*.quantity.min' => 'Item quantities must be at least 1.',
                'customer_id.exists' => 'The selected customer is not valid.',
                'order_type.required' => 'Please select an order type.',
                'order_type.in' => 'Please select a valid order type.',
                'party_size.required' => 'Please specify the party size.',
                'party_size.integer' => 'Party size must be a number.',
                'party_size.min' => 'Party size must be at least 1 guest.',
                'party_size.max' => 'Party size cannot exceed 20 guests.',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->validator->errors()->all())
            ], 422);
        }

        try {
            DB::beginTransaction();

            $order = Order::findOrFail($orderId);

            // Update order details
            if ($request->has('customer_id')) {
                $order->customer_id = $request->customer_id;
            }
            $order->order_type = $request->order_type;
            $order->party_size = $request->party_size;

            // Clear existing order items
            $order->orderItems()->delete();

            // Add new items
            $subtotal = 0;
            foreach ($request->items as $item) {
                $menuItem = $this->productService->getMenuItem($item['menu_item_id']);

                if (!$menuItem) {
                    throw new \Exception("Menu item not found: " . $item['menu_item_id']);
                }

                $itemTotal = $menuItem->price * $item['quantity'];
                $subtotal += $itemTotal;

                $order->orderItems()->create([
                    'menu_item_id' => $item['menu_item_id'],
                    'quantity' => $item['quantity'],
                    'price_at_time' => $menuItem->price,
                    'notes' => $item['notes'] ?? null
                ]);
            }

            // Handle discount
            $discountAmount = 0;
            if ($request->has('discount') && $request->discount['amount'] > 0) {
                $discountAmount = $request->discount['amount'];
            }

            // Calculate totals using OrderSetting
            $discountedSubtotal = $subtotal - $discountAmount;
            
            // Get tax and service charge settings
            $taxRate = OrderSetting::get('tax_rate', 0.10);
            $taxChargingMethod = OrderSetting::get('tax_charging_method', 'customer_pays');
            $serviceChargeRate = OrderSetting::get('service_charge_rate', 0.00);
            $autoApplyTax = OrderSetting::get('auto_apply_tax', true);
            $autoApplyService = OrderSetting::get('auto_apply_service_charge', false);

            // Calculate tax
            $tax = 0;
            if ($autoApplyTax) {
                if ($taxChargingMethod === 'customer_pays') {
                    $tax = $discountedSubtotal * $taxRate;
                }
                // For business_absorbs, tax = 0 (tax is included in price)
            }

            // Calculate service charge
            $serviceCharge = $autoApplyService ? ($discountedSubtotal * $serviceChargeRate) : 0;

            $total = $discountedSubtotal + $tax + $serviceCharge;

            // Update order totals
            $order->update([
                'subtotal_amount' => $subtotal,
                'discount_amount' => $discountAmount,
                'tax_amount' => $tax,
                'service_charge_amount' => $serviceCharge,
                'total_amount' => $total
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order updated successfully!',
                'order_id' => $order->id,
                'table_name' => $order->table ? $order->table->name : 'No Table'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Error updating order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Manual cleanup of table statuses (for fixing existing inconsistencies)
     */
    public function cleanupTables()
    {
        $this->cleanupTableStatuses();

        return response()->json([
            'success' => true,
            'message' => 'Table statuses cleaned up successfully.'
        ]);
    }

    /**
     * Add discount to order
     */
    public function addDiscount(Request $request, Order $order)
    {
        $request->validate([
            'type' => 'required|in:percentage,fixed,coupon,points',
            'value' => 'required|numeric|min:0',
            'coupon_code' => 'nullable|string',
            'points_used' => 'nullable|integer|min:0',
            'description' => 'nullable|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            // Load order with relationships
            $order->load(['orderItems', 'activeDiscounts']);

            // Add the discount
            $discount = $order->addDiscount(
                $request->type,
                $request->value,
                $request->coupon_code,
                $request->points_used,
                $request->description
            );

            // Update order totals
            $this->updateOrderTotal($order);

            DB::commit();

            // Reload order with fresh data
            $order->load(['activeDiscounts']);

            return response()->json([
                'success' => true,
                'message' => 'Discount applied successfully',
                'discount' => $discount->load([]),
                'order_total' => $order->total_amount,
                'total_discount' => $order->getTotalDiscountAmount()
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Error applying discount: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove discount from order
     */
    public function removeDiscount(Request $request, Order $order)
    {
        $request->validate([
            'discount_id' => 'required|exists:order_discounts,id',
        ]);

        try {
            DB::beginTransaction();

            // Remove the discount
            $order->removeDiscount($request->discount_id);

            // Update order totals
            $this->updateOrderTotal($order);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Discount removed successfully',
                'order_total' => $order->fresh()->total_amount,
                'total_discount' => $order->getTotalDiscountAmount()
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Error removing discount: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get order discounts
     */
    public function getOrderDiscounts(Order $order)
    {
        // Load order with relationships
        $order->load(['activeDiscounts']);

        $discounts = $order->activeDiscounts->map(function ($discount) {
            return [
                'id' => $discount->id,
                'type' => $discount->type,
                'value' => $discount->value,
                'amount' => $discount->amount,
                'coupon_code' => $discount->coupon_code,
                'points_used' => $discount->points_used,
                'description' => $discount->description,
                'formatted_description' => $discount->formatted_description,
                'type_label' => $discount->type_label,
            ];
        });

        return response()->json([
            'success' => true,
            'discounts' => $discounts,
            'total_discount' => $order->getTotalDiscountAmount()
        ]);
    }

    /**
     * Check if the current outlet needs table functionality
     */
    private function needsTableFunctionality(): bool
    {
        // First check session (from login)
        if (session()->has('pos_needs_tables')) {
            return session('pos_needs_tables');
        }

        // Fallback to checking outlet category
        $outletCategory = session('api_outlet_category') ?: \Cache::get('pos_outlet_category');

        if ($outletCategory) {
            $needsTables = $this->determineTableRequirement($outletCategory);
            session(['pos_needs_tables' => $needsTables]);
            return $needsTables;
        }

        // Default to requiring tables if no outlet information is available
        return true;
    }

    /**
     * Determine if outlet requires table functionality based on category
     */
    private function determineTableRequirement(string $category): bool
    {
        $category = strtolower(trim($category));

        // FnB (Food & Beverage) outlets need tables
        if ($category === 'fnb' || str_contains($category, 'food') || str_contains($category, 'beverage')) {
            return true;
        }

        // VOO (Viera Oleh-Oleh) outlets don't need tables
        if ($category === 'voo' || str_contains($category, 'oleh-oleh') || str_contains($category, 'souvenir')) {
            return false;
        }

        // Default to requiring tables for unknown categories
        return true;
    }

    /**
     * Sync a single order to API
     */
    public function syncOrder(Order $order)
    {
        try {
            $orderSyncService = app(OrderSyncService::class);
            $result = $orderSyncService->syncOrder($order);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Order synced successfully to API'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync all unsynced orders to API
     */
    public function syncAllOrders()
    {
        try {
            $orderSyncService = app(OrderSyncService::class);
            $result = $orderSyncService->syncAllUnsyncedOrders();

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Batch sync failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get sync statistics
     */
    public function getSyncStats()
    {
        try {
            $orderSyncService = app(OrderSyncService::class);
            $stats = $orderSyncService->getSyncStats();

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get sync stats: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync a single transaction to API
     */
    public function syncTransaction(Order $order)
    {
        try {
            $transactionSyncService = app(TransactionSyncService::class);
            $result = $transactionSyncService->syncTransaction($order);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Transaction synced successfully to API'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync all unsynced transactions to API
     */
    public function syncAllTransactions()
    {
        try {
            $transactionSyncService = app(TransactionSyncService::class);
            $result = $transactionSyncService->syncAllUnsyncedTransactions();

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Batch sync failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get transaction sync statistics
     */
    public function getTransactionSyncStats()
    {
        try {
            $transactionSyncService = app(TransactionSyncService::class);
            $stats = $transactionSyncService->getSyncStats();

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get transaction sync stats: ' . $e->getMessage()
            ], 500);
        }
    }
}
