<?php

namespace App\Services;

use App\Models\MenuCategory;
use App\Models\MenuItem;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Carbon\Carbon;

/**
 * Product Service for managing product data from API or local database
 * Provides a unified interface for product access with fallback capabilities
 */
class ProductService
{
    private PosApiService $posApiService;
    private string $productCacheKey;
    private int $productCacheDuration;
    private bool $useLocalFallback;

    public function __construct(PosApiService $posApiService)
    {
        $this->posApiService = $posApiService;
        $this->productCacheKey = config('pos.product_cache_key', 'pos_products');
        $this->productCacheDuration = config('pos.product_cache_duration', 300);
        $this->useLocalFallback = config('pos.use_local_fallback', true);
    }

    /**
     * Get outlet-specific cache key
     *
     * @return string
     */
    private function getOutletSpecificCacheKey(): string
    {
        $outletId = session('api_outlet_id');
        return $outletId ? $this->productCacheKey . '_outlet_' . $outletId : $this->productCacheKey;
    }

    /**
     * Get all categories with their menu items
     * First tries API, falls back to local database if configured
     * 
     * @param bool $forceRefresh
     * @return Collection
     */
    public function getCategoriesWithItems(bool $forceRefresh = false): Collection
    {
        $cacheKey = $this->getOutletSpecificCacheKey();

        if ($forceRefresh) {
            Cache::forget($cacheKey);
        }

        return Cache::remember($cacheKey, $this->productCacheDuration, function () {
            // TEMPORARY: Use local database to ensure correct IDs after sync
            Log::info('ProductService: Using local database to ensure correct IDs');
            return $this->getLocalCategoriesWithItems();

            // ORIGINAL CODE (temporarily disabled):
            // Try to get products from API first
            // $apiResult = $this->posApiService->fetchProducts();
            //
            // if ($apiResult['success']) {
            //     Log::info('ProductService: Using API data for products');
            //     return $this->transformApiDataToCategories($apiResult['data']);
            // }
            //
            // Log::warning('ProductService: API failed, checking fallback options', [
            //     'api_error' => $apiResult['error'] ?? 'Unknown error',
            //     'use_local_fallback' => $this->useLocalFallback
            // ]);
            //
            // // Fallback to local database if enabled
            // if ($this->useLocalFallback) {
            //     Log::info('ProductService: Using local database fallback');
            //     return $this->getLocalCategoriesWithItems();
            // }
            //
            // // No fallback, return empty collection
            // Log::error('ProductService: No data source available');
            // return collect();
        });
    }

    /**
     * Get categories with items from local database
     * 
     * @return Collection
     */
    public function getLocalCategoriesWithItems(): Collection
    {
        return MenuCategory::with('menuItems')->get();
    }

    /**
     * Transform API data to match local category structure
     *
     * @param array $apiData
     * @return Collection
     */
    private function transformApiDataToCategories(array $apiData): Collection
    {
        // Handle different API response structures
        $products = $apiData['products'] ?? $apiData['data'] ?? [];
        $categoriesMap = [];

        // Group products by category
        foreach ($products as $product) {
            $categoryName = $product['category']['name'] ?? $product['category'] ?? 'Uncategorized';
            $categoryId = $product['category']['id'] ?? null;

            if (!isset($categoriesMap[$categoryName])) {
                $categoriesMap[$categoryName] = [
                    'id' => $categoryId ?? 'api_' . md5($categoryName),
                    'name' => $categoryName,
                    'menuItems' => []
                ];
            }

            // Transform product to menu item format with all new fields
            $categoriesMap[$categoryName]['menuItems'][] = [
                'id' => $product['id'],
                'api_id' => $product['id'], // Map API ID
                'name' => $product['name'] ?? 'Unknown Product',
                'sku' => $product['sku'] ?? null,
                'barcode' => $product['barcode'] ?? null,
                'description' => $product['description'] ?? '',
                'price' => floatval($product['price'] ?? 0),
                'cost_price' => floatval($product['cost_price'] ?? 0),
                'category_id' => $categoryId,
                'stock_quantity' => intval($product['stock_quantity'] ?? 0),
                'is_active' => $product['is_active'] ?? true,
                'is_food_item' => $product['is_food_item'] ?? true,
                'image' => $product['image'] ?? $product['image_path'] ?? null, // Handle both field names
                'created_at' => $product['created_at'] ?? null,
                'updated_at' => $product['updated_at'] ?? null,
                // Mark as API sourced for identification
                '_source' => 'api'
            ];
        }

        // Convert to collection with proper structure
        return collect($categoriesMap)->map(function ($category) {
            return (object) [
                'id' => $category['id'],
                'name' => $category['name'],
                'menuItems' => collect($category['menuItems'])->map(function ($item) {
                    return (object) $item;
                }),
                '_source' => 'api'
            ];
        })->values();
    }

    /**
     * Get a specific menu item by ID
     * Tries API first, then local database
     * 
     * @param int|string $itemId
     * @return object|null
     */
    public function getMenuItem($itemId): ?object
    {
        // First check in cached categories
        $categories = $this->getCategoriesWithItems();
        
        foreach ($categories as $category) {
            foreach ($category->menuItems as $item) {
                if ($item->id == $itemId) {
                    return $item;
                }
            }
        }

        // If not found and using local fallback, try local database directly
        if ($this->useLocalFallback) {
            $localItem = MenuItem::find($itemId);
            if ($localItem) {
                return (object) $localItem->toArray();
            }
        }

        return null;
    }

    /**
     * Clear product cache
     *
     * @return void
     */
    public function clearCache(): void
    {
        $cacheKey = $this->getOutletSpecificCacheKey();
        Cache::forget($cacheKey);

        // Also clear the base cache key to ensure complete cleanup
        Cache::forget($this->productCacheKey);

        Log::info('ProductService: Product cache cleared', [
            'outlet_specific_key' => $cacheKey,
            'base_key' => $this->productCacheKey
        ]);
    }

    /**
     * Get data source information
     * 
     * @return array
     */
    public function getDataSourceInfo(): array
    {
        $categories = $this->getCategoriesWithItems();
        $firstCategory = $categories->first();
        
        $cacheKey = $this->getOutletSpecificCacheKey();

        return [
            'source' => $firstCategory->_source ?? 'unknown',
            'total_categories' => $categories->count(),
            'total_items' => $categories->sum(function ($category) {
                return $category->menuItems->count();
            }),
            'cache_key' => $cacheKey,
            'base_cache_key' => $this->productCacheKey,
            'outlet_id' => session('api_outlet_id'),
            'cache_duration' => $this->productCacheDuration,
            'use_local_fallback' => $this->useLocalFallback,
            'cached_at' => Cache::get($cacheKey . '_timestamp', 'Not cached')
        ];
    }

    /**
     * Force refresh from API
     * 
     * @return Collection
     */
    public function refreshFromApi(): Collection
    {
        return $this->getCategoriesWithItems(true);
    }

    /**
     * Test API connectivity
     *
     * @return array
     */
    public function testApiConnection(): array
    {
        return $this->posApiService->testConnection();
    }

    /**
     * Synchronize products from API to local database
     * This creates/updates local database records with API data
     *
     * @param bool $forceRefresh Force refresh from API
     * @return array Sync result with statistics
     */
    public function syncProductsToDatabase(bool $forceRefresh = false): array
    {
        // Prevent concurrent syncs
        $lockKey = 'product_sync_lock';
        if (Cache::has($lockKey)) {
            Log::warning('ProductService: Sync already in progress, skipping');
            return [
                'success' => false,
                'error' => 'Sync already in progress. Please wait for the current sync to complete.',
                'stats' => [
                    'categories_created' => 0,
                    'categories_updated' => 0,
                    'items_created' => 0,
                    'items_updated' => 0,
                    'items_deactivated' => 0
                ]
            ];
        }

        // Set sync lock (expires in 5 minutes)
        Cache::put($lockKey, true, 300);

        Log::info('ProductService: Starting product synchronization', [
            'force_refresh' => $forceRefresh,
            'lock_set' => true
        ]);

        try {
            // Get fresh data from API
            $apiResult = $this->posApiService->fetchProducts();

            if (!$apiResult['success']) {
                Log::error('ProductService: API fetch failed during sync', [
                    'error' => $apiResult['error'] ?? 'Unknown error'
                ]);

                return [
                    'success' => false,
                    'error' => 'Failed to fetch data from API: ' . ($apiResult['error'] ?? 'Unknown error'),
                    'stats' => [
                        'categories_created' => 0,
                        'categories_updated' => 0,
                        'items_created' => 0,
                        'items_updated' => 0,
                        'items_deactivated' => 0
                    ]
                ];
            }

            $products = $apiResult['data']['products'] ?? $apiResult['data']['data'] ?? [];

            if (empty($products)) {
                Log::warning('ProductService: No products found in API response');
                return [
                    'success' => true,
                    'message' => 'No products found in API response',
                    'stats' => [
                        'categories_created' => 0,
                        'categories_updated' => 0,
                        'items_created' => 0,
                        'items_updated' => 0,
                        'items_deactivated' => 0
                    ]
                ];
            }

            $result = DB::transaction(function () use ($products) {
                return $this->performDatabaseSync($products);
            });

            return $result;

        } catch (\Exception $e) {
            Log::error('ProductService: Sync failed with exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Sync failed: ' . $e->getMessage(),
                'stats' => [
                    'categories_created' => 0,
                    'categories_updated' => 0,
                    'items_created' => 0,
                    'items_updated' => 0,
                    'items_deactivated' => 0
                ]
            ];
        } finally {
            // Always release the sync lock
            Cache::forget($lockKey);
            Log::info('ProductService: Sync lock released');
        }
    }

    /**
     * Perform smart database sync with API data (update existing, create new)
     *
     * @param array $products
     * @return array
     */
    private function performDatabaseSync(array $products): array
    {
        Log::info('ProductService: Starting smart database sync', [
            'products_count' => count($products)
        ]);

        $stats = [
            'categories_created' => 0,
            'categories_updated' => 0,
            'items_created' => 0,
            'items_updated' => 0,
            'items_deactivated' => 0
        ];

        // Step 1: Track API IDs to identify items to deactivate later
        $apiCategoryIds = [];
        $apiItemIds = [];

        // Step 2: Group products by category and collect API IDs
        $categoriesMap = [];
        foreach ($products as $product) {
            $categoryName = $product['category']['name'] ?? $product['category'] ?? 'Uncategorized';
            $categoryApiId = $product['category']['id'] ?? null;
            $productApiId = $product['id'] ?? null;

            // Track API IDs
            if ($categoryApiId) {
                $apiCategoryIds[] = $categoryApiId;
            }
            if ($productApiId) {
                $apiItemIds[] = $productApiId;
            }

            if (!isset($categoriesMap[$categoryName])) {
                $categoriesMap[$categoryName] = [
                    'api_id' => $categoryApiId,
                    'name' => $categoryName,
                    'products' => []
                ];
            }

            $categoriesMap[$categoryName]['products'][] = $product;
        }

        // Step 3: Sync categories (update existing or create new)
        Log::info('ProductService: Syncing categories');

        foreach ($categoriesMap as $categoryName => $categoryData) {
            $categoryApiId = $categoryData['api_id'];

            // Try to find existing category by API ID or name
            $category = null;
            if ($categoryApiId) {
                $category = MenuCategory::where('api_id', $categoryApiId)->first();
            }
            if (!$category) {
                $category = MenuCategory::where('name', $categoryName)->first();
            }

            if ($category) {
                // Update existing category
                $category->update([
                    'name' => $categoryName,
                    'api_id' => $categoryApiId
                ]);
                $stats['categories_updated']++;
                Log::info('ProductService: Updated category', ['name' => $categoryName, 'id' => $category->id]);
            } else {
                // Create new category
                $category = MenuCategory::create([
                    'name' => $categoryName,
                    'api_id' => $categoryApiId
                ]);
                $stats['categories_created']++;
                Log::info('ProductService: Created category', ['name' => $categoryName, 'id' => $category->id]);
            }

            // Step 4: Sync products for this category
            foreach ($categoryData['products'] as $product) {
                $productApiId = $product['id'] ?? null;
                $productName = $product['name'] ?? 'Unnamed Product';

                // Try to find existing menu item by API ID
                $menuItem = null;
                if ($productApiId) {
                    $menuItem = MenuItem::where('api_id', $productApiId)->first();
                }

                $productData = [
                    'name' => $productName,
                    'sku' => $product['sku'] ?? null,
                    'barcode' => $product['barcode'] ?? null,
                    'description' => $product['description'] ?? null,
                    'price' => floatval($product['price'] ?? 0),
                    'cost_price' => floatval($product['cost_price'] ?? 0),
                    'category_id' => $category->id,
                    'stock_quantity' => intval($product['stock_quantity'] ?? 0),
                    'is_active' => $product['is_active'] ?? $product['is_available'] ?? true, // Handle both field names
                    'is_food_item' => $product['is_food_item'] ?? true,
                    'image' => $product['image'] ?? $product['image_path'] ?? null, // Handle both field names
                    'api_id' => $productApiId,
                ];

                if ($menuItem) {
                    // Update existing menu item
                    $menuItem->update($productData);
                    $stats['items_updated']++;
                    Log::debug('ProductService: Updated menu item', [
                        'id' => $menuItem->id,
                        'name' => $menuItem->name,
                        'api_id' => $productApiId
                    ]);
                } else {
                    // Create new menu item
                    $menuItem = MenuItem::create($productData);
                    $stats['items_created']++;
                    Log::debug('ProductService: Created menu item', [
                        'id' => $menuItem->id,
                        'name' => $menuItem->name,
                        'api_id' => $productApiId
                    ]);
                }
            }
        }

        // Step 5: Optionally deactivate items not in API (commented out to preserve existing orders)
        // Uncomment if you want to deactivate items that are no longer in the API
        /*
        if (!empty($apiItemIds)) {
            $deactivatedCount = MenuItem::whereNotNull('api_id')
                ->whereNotIn('api_id', $apiItemIds)
                ->update(['is_available' => false]);
            $stats['items_deactivated'] = $deactivatedCount;
            Log::info('ProductService: Deactivated items not in API', ['count' => $deactivatedCount]);
        }
        */

        // Clear cache after successful sync
        $this->clearCache();

        Log::info('ProductService: Smart sync completed successfully', $stats);

        return [
            'success' => true,
            'message' => 'Products synchronized successfully (smart update)',
            'stats' => $stats,
            'processed_categories' => count($categoriesMap),
            'processed_items' => array_sum(array_map(fn($cat) => count($cat['products']), $categoriesMap))
        ];
    }

    /**
     * Sync individual menu item to database - Improved version
     *
     * @param array $product
     * @param int $categoryId
     * @return array
     */
    private function syncMenuItem(array $product, int $categoryId): array
    {
        $itemData = [
            'category_id' => $categoryId,
            'name' => trim($product['name'] ?? 'Unknown Product'),
            'sku' => $product['sku'] ?? null,
            'barcode' => $product['barcode'] ?? null,
            'description' => trim($product['description'] ?? ''),
            'price' => floatval($product['price'] ?? 0),
            'cost_price' => floatval($product['cost_price'] ?? 0),
            'stock_quantity' => intval($product['stock_quantity'] ?? 0),
            'is_active' => $product['is_active'] ?? $product['is_available'] ?? true,
            'is_food_item' => $product['is_food_item'] ?? true,
            'image' => $product['image'] ?? $product['image_path'] ?? null,
        ];

        $apiId = $product['id'] ?? null;
        $existingItem = null;

        Log::debug('ProductService: Syncing menu item', [
            'name' => $itemData['name'],
            'api_id' => $apiId,
            'category_id' => $categoryId
        ]);

        // Strategy 1: Find by API ID (most reliable)
        if ($apiId) {
            $existingItem = MenuItem::where('api_id', $apiId)->first();
            if ($existingItem) {
                Log::debug('ProductService: Found item by API ID', ['id' => $existingItem->id]);
            }
        }

        // Strategy 2: Find by exact name and category match
        if (!$existingItem) {
            $existingItem = MenuItem::where('name', $itemData['name'])
                                  ->where('category_id', $categoryId)
                                  ->first();
            if ($existingItem) {
                Log::debug('ProductService: Found item by name and category', ['id' => $existingItem->id]);
            }
        }

        // Strategy 3: Find by similar name (fuzzy matching) in same category
        if (!$existingItem) {
            $similarItems = MenuItem::where('category_id', $categoryId)
                                  ->get()
                                  ->filter(function ($item) use ($itemData) {
                                      $similarity = 0;
                                      similar_text(
                                          strtolower(trim($item->name)),
                                          strtolower(trim($itemData['name'])),
                                          $similarity
                                      );
                                      return $similarity > 85; // 85% similarity threshold
                                  })
                                  ->first();

            if ($similarItems) {
                $existingItem = $similarItems;
                Log::debug('ProductService: Found item by similarity', [
                    'id' => $existingItem->id,
                    'existing_name' => $existingItem->name,
                    'new_name' => $itemData['name']
                ]);
            }
        }

        if ($existingItem) {
            // Update existing item with all data
            $updateData = $itemData;
            if ($apiId && !$existingItem->api_id) {
                $updateData['api_id'] = $apiId;
            }

            // Mark as synced since this product comes from API
            $updateData['is_synced_to_api'] = true;
            $updateData['last_api_sync_at'] = now();

            $existingItem->update($updateData);

            Log::info('ProductService: Updated menu item from API', [
                'id' => $existingItem->id,
                'name' => $itemData['name'],
                'api_id' => $apiId,
                'marked_as_synced' => true
            ]);

            return ['created' => 0, 'updated' => 1];
        } else {
            // Create new item
            if ($apiId) {
                $itemData['api_id'] = $apiId;
            }

            // Mark as synced since this product comes from API
            $itemData['is_synced_to_api'] = true;
            $itemData['last_api_sync_at'] = now();

            $newItem = MenuItem::create($itemData);

            Log::info('ProductService: Created new menu item from API', [
                'id' => $newItem->id,
                'name' => $itemData['name'],
                'category_id' => $categoryId,
                'api_id' => $apiId,
                'marked_as_synced' => true
            ]);

            return ['created' => 1, 'updated' => 0];
        }
    }

    /**
     * Get sync status and statistics
     *
     * @return array
     */
    public function getSyncStatus(): array
    {
        $apiResult = $this->posApiService->testConnection();
        $localStats = $this->getLocalDatabaseStats();
        $dataSourceInfo = $this->getDataSourceInfo();

        return [
            'api_connected' => $apiResult['success'] ?? false,
            'api_status' => $apiResult['message'] ?? 'Unknown',
            'last_sync' => Cache::get('product_last_sync', 'Never'),
            'data_source' => $dataSourceInfo['source'],
            'local_stats' => $localStats,
            'api_stats' => $dataSourceInfo,
            'sync_available' => $apiResult['success'] ?? false
        ];
    }

    /**
     * Get local database statistics
     *
     * @return array
     */
    private function getLocalDatabaseStats(): array
    {
        return [
            'total_categories' => MenuCategory::count(),
            'total_items' => MenuItem::count(),
            'items_with_api_id' => MenuItem::whereNotNull('api_id')->count(),
            'items_without_api_id' => MenuItem::whereNull('api_id')->count(),
        ];
    }

    /**
     * Force sync and update cache timestamp
     *
     * @return array
     */
    public function forceSyncFromApi(): array
    {
        $result = $this->syncProductsToDatabase(true);

        if ($result['success']) {
            Cache::put('product_last_sync', now()->format('Y-m-d H:i:s'), 86400);
        }

        return $result;
    }
}
