# Outlet-Specific Product Filtering Implementation

## Overview
This implementation adds outlet-specific filtering to the POS API product fetching functionality. The system now captures outlet ID from login responses and uses it to filter products by outlet.

## Changes Made

### 1. Authentication Enhancement (`app/Http/Requests/Auth/LoginRequest.php`)
- **Outlet ID Capture**: Modified `authenticateWithApi()` to capture outlet data from API response
- **Session Storage**: Added outlet ID storage in session (`api_outlet_id`)
- **Response Enhancement**: Updated return array to include outlet data
- **Logging**: Added logging for outlet ID capture success/failure

### 2. POS API Service Enhancement (`app/Services/PosApiService.php`)
- **Outlet ID Caching**: Added outlet ID caching during authentication
- **URL Building**: Modified `fetchProducts()` to append `?outlet_id={id}` parameter
- **Outlet ID Retrieval**: Added `getOutletId()` method to get outlet ID from session/cache
- **Cache Management**: Updated `clearAuthToken()` to also clear outlet ID
- **Fallback Support**: Maintains backward compatibility when outlet ID is not available

### 3. Product Service Enhancement (`app/Services/ProductService.php`)
- **Outlet-Specific Caching**: Added `getOutletSpecificCacheKey()` method
- **Cache Key Generation**: Cache keys now include outlet ID (e.g., `pos_products_outlet_1`)
- **Cache Management**: Updated cache clearing to handle outlet-specific keys
- **Data Source Info**: Enhanced info to show outlet-specific cache details

### 4. Controller Enhancement (`app/Http/Controllers/PosApiController.php`)
- **Cached Products**: Updated `fetchProductsWithCache()` to use outlet-specific cache keys
- **Session Integration**: Retrieves outlet ID from session for caching

### 5. Authentication Guard Enhancement (`app/Guards/ApiSessionGuard.php`)
- **Logout Cleanup**: Added outlet ID removal during logout process

### 6. Test Interface Enhancement (`resources/views/pos-api-test.blade.php`)
- **Session Information**: Added outlet ID display in test interface
- **Debug Information**: Shows current outlet ID and authentication status

### 7. Testing Command (`app/Console/Commands/TestOutletIdCommand.php`)
- **Comprehensive Testing**: New command to test outlet ID functionality
- **Authentication Testing**: Verifies outlet ID capture during login
- **Product Fetching**: Tests outlet-specific product filtering
- **Cache Verification**: Checks cache functionality with outlet IDs

### 8. Configuration Documentation (`config/pos.php`)
- **Documentation**: Added comments explaining outlet-specific functionality

## API Endpoint Changes

### Before
```
GET http://viera-filament.test/api/pos/sync/products
```

### After
```
GET http://viera-filament.test/api/pos/sync/products?outlet_id={outlet_id}
```

## Session Data Structure

The system now stores the following in the session:
- `api_token`: Authentication token
- `api_user`: User information
- `api_authenticated`: Authentication status
- `api_outlet_id`: **NEW** - Outlet ID for filtering

## Cache Key Structure

### Before
- `pos_products`: General product cache

### After
- `pos_products`: Fallback cache (when no outlet ID)
- `pos_products_outlet_{id}`: Outlet-specific cache

## Testing

### Manual Testing
1. Visit `/pos-api-test` to see session information including outlet ID
2. Use the test interface to verify product fetching with outlet filtering

### Command Line Testing
```bash
php artisan pos:test-outlet-id
```

## Backward Compatibility

The implementation maintains full backward compatibility:
- If no outlet ID is available, the system works as before
- Cache keys fall back to the original format when outlet ID is missing
- API calls work without outlet_id parameter if not available

## Expected Login Response Format

The system expects the login API to return:
```json
{
    "token": "authentication_token",
    "user": {
        "name": "User Name",
        "email": "<EMAIL>"
    },
    "outlet": {
        "id": 1,
        "name": "Main Store"
    }
}
```

## Benefits

1. **Outlet-Specific Products**: Products are now filtered by outlet
2. **Correct Pricing**: Each outlet can have different pricing
3. **Inventory Accuracy**: Inventory reflects outlet-specific stock
4. **Performance**: Outlet-specific caching improves performance
5. **Scalability**: Supports multi-outlet operations
6. **Backward Compatible**: Works with existing systems

## Next Steps

1. Test with actual API responses containing outlet data
2. Verify product filtering works correctly for different outlets
3. Monitor cache performance with outlet-specific keys
4. Consider adding outlet selection UI for admin users
