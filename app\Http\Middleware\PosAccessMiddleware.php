<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PosAccessMiddleware
{
    /**
     * Handle an incoming request.
     * Check if user has proper role and jabatan to access POS features.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Check if user can access admin features (includes role and jabatan validation)
        if (!$user->canAccessAdminFeatures()) {

            // Show Indonesian error message for POS access
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Anda tidak memiliki akses ke POS'], 403);
            }

            // For web requests: logout user and redirect to login with popup message
            auth()->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('login')->with('error', 'Anda tidak memiliki akses ke POS');
        }

        return $next($request);
    }
}
