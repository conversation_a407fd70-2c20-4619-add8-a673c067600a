<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Customer extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'phone',
        'email',
        'date_of_birth',
        'gender',
        'customer_segment',
        'address',
        'detailed_address',
        'postal_code',
        'points',
        'loyalty_points',
        'active_status',
        'notes',
        'api_id',
        'is_synced_to_api',
        'last_api_sync_at',
    ];

    protected function casts(): array
    {
        return [
            'date_of_birth' => 'date',
            'active_status' => 'boolean',
            'points' => 'integer',
            'loyalty_points' => 'integer',
            'is_synced_to_api' => 'boolean',
            'last_api_sync_at' => 'datetime',
        ];
    }

    /**
     * Add points to customer
     */
    public function addPoints($points)
    {
        $this->increment('points', $points);
    }

    /**
     * Use points for discount
     */
    public function usePoints($points)
    {
        if ($this->points >= $points) {
            $this->decrement('points', $points);
            return true;
        }
        return false;
    }

    /**
     * Get the orders for this customer.
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }
}
