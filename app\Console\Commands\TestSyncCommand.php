<?php

namespace App\Console\Commands;

use App\Services\ProductService;
use Illuminate\Console\Command;

class TestSyncCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:test-sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the sync functionality to debug partial sync issues';

    /**
     * Execute the console command.
     */
    public function handle(ProductService $productService): int
    {
        $this->info('🔄 Testing Sync Functionality');
        $this->newLine();

        // Test the sync process
        $this->info('📦 Starting Product Sync...');
        $syncResult = $productService->forceSyncFromApi();

        $this->newLine();
        $this->info('📊 Sync Results:');
        $this->info('Success: ' . ($syncResult['success'] ? 'YES' : 'NO'));
        
        if (isset($syncResult['error'])) {
            $this->error('Error: ' . $syncResult['error']);
        }

        if (isset($syncResult['stats'])) {
            $stats = $syncResult['stats'];
            $this->info('Statistics:');
            $this->info('  Categories Created: ' . $stats['categories_created']);
            $this->info('  Categories Updated: ' . $stats['categories_updated']);
            $this->info('  Items Created: ' . $stats['items_created']);
            $this->info('  Items Updated: ' . $stats['items_updated']);
            $this->info('  Items Deactivated: ' . ($stats['items_deactivated'] ?? 0));
        }

        $this->newLine();

        // Check what's in the database now
        $this->info('📋 Database Status:');
        $categories = \App\Models\MenuCategory::count();
        $items = \App\Models\MenuItem::count();
        $itemsWithApiId = \App\Models\MenuItem::whereNotNull('api_id')->count();
        
        $this->info("  Total Categories: {$categories}");
        $this->info("  Total Items: {$items}");
        $this->info("  Items with API ID: {$itemsWithApiId}");

        // Show some sample data
        $this->newLine();
        $this->info('📝 Sample Data:');
        $sampleItems = \App\Models\MenuItem::with('category')->limit(5)->get();
        foreach ($sampleItems as $item) {
            $categoryName = $item->category ? $item->category->name : 'N/A';
            $apiId = $item->api_id ? $item->api_id : 'N/A';
            $this->info("  - {$item->name} (Price: {$item->price}, Category: {$categoryName}, API ID: {$apiId})");
        }

        return 0;
    }
}
