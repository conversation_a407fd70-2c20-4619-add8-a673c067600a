<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\MenuCategoryController;
use App\Http\Controllers\MenuItemController;
use App\Http\Controllers\TableLayoutController;
use App\Http\Controllers\POSController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\OrderSettingsController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;

Route::get('/', function () {
    return view('welcome');
//     $response = Http::accept('application/json')->post('http://viera-filament.test/api/pos/login',['email'=>'<EMAIL>','password'=>'M4$ukaja','device_name'=>'POS','location_id'=>null]);
// $data = $response->json();
// dd($response->json()['token']);
});

Route::get('/dashboard', function () {
    $user = auth()->user();

    // Check if user can access POS features
    if ($user && $user->canAccessAdminFeatures()) {
        // Redirect authorized users directly to POS
        return redirect()->route('pos.index');
    }

    // For unauthorized users: logout and redirect to login with popup message
    auth()->logout();
    request()->session()->invalidate();
    request()->session()->regenerateToken();

    return redirect()->route('login')->with('error', 'Anda tidak memiliki akses ke POS');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Menu Management Routes (Admin only)
    Route::resource('menu-categories', MenuCategoryController::class);
    Route::resource('menu-items', MenuItemController::class);

    // API Sync Routes (Admin only)
    Route::post('/menu-categories/sync', [MenuCategoryController::class, 'sync'])->name('menu-categories.sync');
    Route::get('/menu-categories/sync-status', [MenuCategoryController::class, 'syncStatus'])->name('menu-categories.sync-status');
    Route::post('/menu-items/sync', [MenuItemController::class, 'sync'])->name('menu-items.sync');
    Route::get('/menu-items/sync-status', [MenuItemController::class, 'syncStatus'])->name('menu-items.sync-status');
    Route::post('/menu-items/manual-sync', [MenuItemController::class, 'manualSync'])->name('menu-items.manual-sync');
    Route::post('/menu-items/{menuItem}/upload-to-api', [MenuItemController::class, 'uploadToApi'])->name('menu-items.upload-to-api');


    // Table Management Routes
    Route::resource('tables', TableLayoutController::class);

    // Settings Routes
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');

    // Order Settings Routes
    Route::get('/settings/order-settings', [OrderSettingsController::class, 'index'])->name('settings.order-settings');
    Route::put('/settings/order-settings', [OrderSettingsController::class, 'update'])->name('settings.order-settings.update');
    Route::post('/settings/order-settings/reset', [OrderSettingsController::class, 'reset'])->name('settings.order-settings.reset');
    Route::get('/api/settings/{key}', [OrderSettingsController::class, 'getSetting'])->name('api.settings.get');

    // Customer Management Routes (under settings)
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/customers', [CustomerController::class, 'index'])->name('customers.index');
        Route::get('/customers/create', [CustomerController::class, 'create'])->name('customers.create');
        Route::post('/customers', [CustomerController::class, 'store'])->name('customers.store');
        Route::get('/customers/{customer}/edit', [CustomerController::class, 'edit'])->name('customers.edit');
        Route::put('/customers/{customer}', [CustomerController::class, 'update'])->name('customers.update');
        Route::delete('/customers/{customer}', [CustomerController::class, 'destroy'])->name('customers.destroy');
        Route::post('/customers/sync', [CustomerController::class, 'sync'])->name('customers.sync');
        Route::post('/customers/sync-to-api', [CustomerController::class, 'syncToApi'])->name('customers.sync-to-api');
        Route::get('/customers/sync-status', [CustomerController::class, 'syncStatus'])->name('customers.sync-status');
    });



    // Payment Routes (require regular auth only)
    Route::get('/payment/{order}', [PaymentController::class, 'show'])->name('pos.payment');
    Route::post('/payment/{order}/process', [PaymentController::class, 'process'])->name('payments.process');
    Route::get('/payment/{order}/receipt', [PaymentController::class, 'receipt'])->name('payments.receipt');
    Route::post('/payment/calculate-change', [PaymentController::class, 'calculateChange'])->name('payments.calculate-change');
});

// POS Routes (API authentication only - no local auth required)
Route::get('/pos', [POSController::class, 'index'])->name('pos.index');
Route::post('/pos/sync-products', [POSController::class, 'syncProducts'])->name('pos.sync-products');
Route::post('/pos/create-order', [POSController::class, 'createOrder'])->name('pos.create-order');
Route::post('/pos/create-order-with-customer', [POSController::class, 'createOrderWithCustomer'])->name('pos.create-order-with-customer');
Route::post('/pos/create-menu-order', [POSController::class, 'createMenuOrder'])->name('pos.create-menu-order');
Route::post('/pos/create-menu-order-with-customer', [POSController::class, 'createMenuOrderWithCustomer'])->name('pos.create-menu-order-with-customer');
Route::post('/pos/add-customer', [POSController::class, 'addCustomer'])->name('pos.add-customer');
Route::get('/pos/order/{order}', [POSController::class, 'showOrder'])->name('pos.order');
Route::post('/pos/order/{order}/add-item', [POSController::class, 'addItem'])->name('pos.add-item');
Route::put('/pos/order/{order}/item/{orderItem}', [POSController::class, 'updateItem'])->name('pos.update-item');
Route::delete('/pos/order/{order}/item/{orderItem}', [POSController::class, 'removeItem'])->name('pos.remove-item');

// Order Management Routes
Route::post('/pos/order/{order}/hold', [POSController::class, 'holdOrder'])->name('pos.hold-order');
Route::post('/pos/order/{order}/cancel', [POSController::class, 'cancelOrder'])->name('pos.cancel-order');
Route::post('/pos/order/{order}/complete', [POSController::class, 'completeOrder'])->name('pos.complete-order');

Route::get('/pos/ongoing-orders', [POSController::class, 'ongoingOrders'])->name('pos.ongoing-orders');
Route::get('/pos/order-history', [POSController::class, 'orderHistory'])->name('pos.order-history');
Route::get('/pos/order-details/{order}', [POSController::class, 'orderDetails'])->name('pos.order-details');
Route::get('/pos/kitchen/{order}', [POSController::class, 'printKitchen'])->name('pos.kitchen');
Route::get('/pos/receipt/{order}', [POSController::class, 'printReceipt'])->name('pos.receipt');

// Discount API Routes (Legacy - keeping for backward compatibility)
Route::post('/pos/validate-coupon', [POSController::class, 'validateCoupon'])->name('pos.validate-coupon');
Route::get('/pos/customer-points/{customer}', [POSController::class, 'getCustomerPoints'])->name('pos.customer-points');

// Table API Routes
Route::get('/pos/table-info/{table}', [POSController::class, 'getTableInfo'])->name('pos.table-info');
Route::get('/pos/table-order/{table}', [POSController::class, 'getTableOrder'])->name('pos.table-order');
Route::post('/pos/update-order/{order}', [POSController::class, 'updateOrder'])->name('pos.update-order');
Route::post('/pos/cleanup-tables', [POSController::class, 'cleanupTables'])->name('pos.cleanup-tables');

// Discount Management Routes
Route::post('/pos/order/{order}/add-discount', [POSController::class, 'addDiscount'])->name('pos.add-discount');
Route::post('/pos/order/{order}/remove-discount', [POSController::class, 'removeDiscount'])->name('pos.remove-discount');
Route::get('/pos/order/{order}/discounts', [POSController::class, 'getOrderDiscounts'])->name('pos.get-discounts');

// Order Sync Routes
Route::post('/pos/order/{order}/sync', [POSController::class, 'syncOrder'])->name('pos.sync-order');
Route::post('/pos/sync-all-orders', [POSController::class, 'syncAllOrders'])->name('pos.sync-all-orders');
Route::get('/pos/sync-stats', [POSController::class, 'getSyncStats'])->name('pos.sync-stats');

// Transaction Sync Routes
Route::post('/pos/order/{order}/sync-transaction', [POSController::class, 'syncTransaction'])->name('pos.sync-transaction');
Route::post('/pos/sync-all-transactions', [POSController::class, 'syncAllTransactions'])->name('pos.sync-all-transactions');
Route::get('/pos/transaction-sync-stats', [POSController::class, 'getTransactionSyncStats'])->name('pos.transaction-sync-stats');

// Analytics Routes
Route::middleware(['auth', 'pos.access'])->prefix('analytics')->name('analytics.')->group(function () {
    Route::get('/', [App\Http\Controllers\AnalyticsController::class, 'index'])->name('index');
    Route::get('/revenue', [App\Http\Controllers\AnalyticsController::class, 'revenue'])->name('revenue');
    Route::get('/customers', [App\Http\Controllers\AnalyticsController::class, 'customers'])->name('customers');
    Route::get('/transactions', [App\Http\Controllers\AnalyticsController::class, 'transactions'])->name('transactions');
    Route::get('/products', [App\Http\Controllers\AnalyticsController::class, 'products'])->name('products');
    Route::get('/categories', [App\Http\Controllers\AnalyticsController::class, 'categories'])->name('categories');
    Route::get('/sync', [App\Http\Controllers\AnalyticsController::class, 'sync'])->name('sync');
    Route::post('/export', [App\Http\Controllers\AnalyticsController::class, 'export'])->name('export');
});

// Settings Routes
Route::middleware(['auth'])->prefix('settings')->name('settings.')->group(function () {
    // Tax Settings
    Route::prefix('tax')->name('tax.')->group(function () {
        Route::get('/', [App\Http\Controllers\TaxSettingController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\TaxSettingController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\TaxSettingController::class, 'store'])->name('store');
        Route::get('/{taxSetting}', [App\Http\Controllers\TaxSettingController::class, 'show'])->name('show');
        Route::get('/{taxSetting}/edit', [App\Http\Controllers\TaxSettingController::class, 'edit'])->name('edit');
        Route::put('/{taxSetting}', [App\Http\Controllers\TaxSettingController::class, 'update'])->name('update');
        Route::delete('/{taxSetting}', [App\Http\Controllers\TaxSettingController::class, 'destroy'])->name('destroy');
        Route::post('/{taxSetting}/activate', [App\Http\Controllers\TaxSettingController::class, 'activate'])->name('activate');
    });

    // Order Types Settings
    Route::resource('order-types', App\Http\Controllers\Settings\OrderTypeController::class);
    Route::post('/order-types/update-order', [App\Http\Controllers\Settings\OrderTypeController::class, 'updateOrder'])->name('order-types.update-order');
    Route::post('/order-types/{orderType}/toggle-status', [App\Http\Controllers\Settings\OrderTypeController::class, 'toggleStatus'])->name('order-types.toggle-status');

    // Payment Methods Settings
    Route::resource('payment-methods', App\Http\Controllers\Settings\PaymentMethodController::class);
    Route::post('/payment-methods/update-order', [App\Http\Controllers\Settings\PaymentMethodController::class, 'updateOrder'])->name('payment-methods.update-order');
    Route::post('/payment-methods/{paymentMethod}/toggle-status', [App\Http\Controllers\Settings\PaymentMethodController::class, 'toggleStatus'])->name('payment-methods.toggle-status');
});

// Payment Routes (no auth required)
Route::get('/payment/{order}', [PaymentController::class, 'show'])->name('pos.payment');
Route::post('/payment/{order}/process', [PaymentController::class, 'process'])->name('payments.process');
Route::get('/payment/{order}/receipt', [PaymentController::class, 'receipt'])->name('payments.receipt');
Route::post('/payment/calculate-change', [PaymentController::class, 'calculateChange'])->name('payments.calculate-change');

// Network health check endpoint
Route::get('/api/health-check', function () {
    try {
        // Check database connection
        $dbStatus = true;
        try {
            DB::connection()->getPdo();
        } catch (\Exception $e) {
            $dbStatus = false;
        }

        // Check API connectivity if user is authenticated
        $apiStatus = true;
        $apiLatency = null;

        if (auth()->check()) {
            try {
                $startTime = microtime(true);
                $apiUrl = config('pos.api_base_url') . '/status';
                $token = session('api_token');

                if ($token) {
                    $response = Http::timeout(5)
                        ->withToken($token)
                        ->get($apiUrl);

                    $apiLatency = round((microtime(true) - $startTime) * 1000);
                    $apiStatus = $response->successful();
                }
            } catch (\Exception $e) {
                $apiStatus = false;
            }
        }

        return response()->json([
            'status' => 'ok',
            'timestamp' => now()->toISOString(),
            'services' => [
                'database' => $dbStatus ? 'up' : 'down',
                'api' => $apiStatus ? 'up' : 'down'
            ],
            'latency' => $apiLatency,
            'authenticated' => auth()->check()
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'Health check failed',
            'timestamp' => now()->toISOString()
        ], 500);
    }
})->name('health-check');

// Test network validation route
Route::get('/test-network-validation', function () {
    try {
        $productSyncService = new \App\Services\ProductSyncService();
        $networkAvailable = $productSyncService->isNetworkAvailable();

        return response()->json([
            'network_available' => $networkAvailable,
            'environment' => app()->environment(),
            'network_validation_disabled' => config('pos.disable_network_validation', false),
            'product_creation_allowed' => $networkAvailable,
            'message' => $networkAvailable
                ? 'Network validation passed - product creation allowed'
                : 'Network validation failed - product creation blocked',
            'timestamp' => now()->toISOString()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'network_available' => false,
            'product_creation_allowed' => false
        ], 500);
    }
})->middleware('auth');



// Network Monitor Demo Route
Route::get('/network-demo', function () {
    return view('network-demo');
})->middleware('auth')->name('network-demo');



// Sync diagnostics page
Route::get('/sync-diagnostics', function () {
    $productService = app(\App\Services\ProductService::class);
    $syncStatus = $productService->getSyncStatus();

    // Get detailed database stats
    $stats = [
        'categories' => \App\Models\MenuCategory::count(),
        'items' => \App\Models\MenuItem::count(),
        'items_with_api_id' => \App\Models\MenuItem::whereNotNull('api_id')->count(),
        'items_without_api_id' => \App\Models\MenuItem::whereNull('api_id')->count(),
        'cache_status' => [
            'product_cache_exists' => \Cache::has('products_cache'),
            'last_sync' => \Cache::get('product_last_sync', 'Never'),
            'sync_lock_active' => \Cache::has('product_sync_lock')
        ]
    ];

    return view('sync-diagnostics', compact('syncStatus', 'stats'));
})->name('sync.diagnostics')->middleware(['auth']);

// Debug route to check session data
Route::get('/debug-session', function () {
    return response()->json([
        'session_data' => [
            'api_token' => session('api_token') ? 'present' : 'missing',
            'api_user' => session('api_user'),
            'api_outlet_id' => session('api_outlet_id'),
            'api_outlet_category' => session('api_outlet_category'),
            'pos_needs_tables' => session('pos_needs_tables'),
        ],
        'cache_data' => [
            'pos_outlet_id' => \Cache::get('pos_outlet_id'),
            'pos_outlet_category' => \Cache::get('pos_outlet_category'),
        ]
    ]);
})->middleware('auth');

// Debug route to test sync manually
Route::get('/debug-sync', function () {
    $productService = app(\App\Services\ProductService::class);
    $customerService = app(\App\Services\CustomerService::class);

    $productResult = $productService->forceSyncFromApi();
    $customerResult = $customerService->forceSyncFromApi();

    // User sync disabled - using API-only authentication
    $userResult = [
        'success' => true,
        'message' => 'User sync skipped - using API-only authentication',
        'stats' => ['users_created' => 0, 'users_updated' => 0]
    ];

    return response()->json([
        'product_sync' => $productResult,
        'customer_sync' => $customerResult,
        'user_sync' => $userResult,
        'session_data' => [
            'api_token' => session('api_token') ? 'present' : 'missing',
            'api_outlet_id' => session('api_outlet_id'),
        ],
        'cache_data' => [
            'pos_outlet_id' => \Cache::get('pos_outlet_id'),
            'pos_api_token' => \Cache::get('pos_api_token') ? 'present' : 'missing',
        ]
    ]);
})->middleware('auth');

// Debug route to check authentication
Route::get('/debug-auth', function () {
    $currentUser = auth()->user();

    return response()->json([
        'auth_check' => auth()->check(),
        'auth_id' => auth()->id(),
        'current_user' => $currentUser ? [
            'id' => $currentUser->id,
            'name' => $currentUser->name,
            'email' => $currentUser->email,
        ] : null,
        'api_user' => session('api_user'),
        'session_data' => [
            'user_id' => session('user_id'),
            'login_web' => session('login_web'),
            'api_token' => session('api_token') ? 'present' : 'missing',
            'api_authenticated' => session('api_authenticated'),
            'api_outlet_id' => session('api_outlet_id'),
        ],
        'users_in_db' => \App\Models\User::select('id', 'name', 'email')->get()->toArray()
    ]);
})->middleware('auth');





// Debug route to check menu items
Route::get('/debug-menu-items', function () {
    $items = \App\Models\MenuItem::with('category')->get();

    // If no items, create some sample data
    if ($items->count() === 0) {
        // Create a default category
        $category = \App\Models\MenuCategory::firstOrCreate(['name' => 'Main Dishes']);

        // Create sample menu items
        $sampleItems = [
            ['name' => 'Ayam Goreng', 'price' => 10000],
            ['name' => 'Nasi Goreng', 'price' => 15000],
            ['name' => 'Mie Ayam', 'price' => 12000],
        ];

        foreach ($sampleItems as $itemData) {
            \App\Models\MenuItem::create([
                'name' => $itemData['name'],
                'price' => $itemData['price'],
                'category_id' => $category->id,
                'is_active' => true,
            ]);
        }

        $items = \App\Models\MenuItem::with('category')->get();
    }

    return response()->json([
        'total_items' => $items->count(),
        'items' => $items->map(function($item) {
            return [
                'id' => $item->id,
                'name' => $item->name,
                'price' => $item->price,
                'category' => $item->category->name ?? 'No Category',
                'is_available' => $item->is_available,
            ];
        })
    ]);
})->middleware('auth');

// Debug route to check tables
Route::get('/debug-tables', function () {
    $tables = \App\Models\TableLayout::all();
    return response()->json([
        'total_tables' => $tables->count(),
        'tables' => $tables->map(function($table) {
            return [
                'id' => $table->id,
                'name' => $table->name,
                'status' => $table->status,
                'seats' => $table->seats,
            ];
        })
    ]);
})->middleware('auth');

// Debug route to check foreign keys
Route::get('/debug-foreign-keys', function () {
    return response()->json([
        'current_user' => auth()->user() ? [
            'id' => auth()->id(),
            'name' => auth()->user()->name,
            'exists' => true
        ] : ['exists' => false],
        'users_count' => \App\Models\User::count(),
        'customers_count' => \App\Models\Customer::count(),
        'tables_count' => \App\Models\TableLayout::count(),
        'sample_customer' => \App\Models\Customer::first(),
        'sample_table' => \App\Models\TableLayout::first(),
    ]);
})->middleware('auth');

// Debug route to clean up data
Route::get('/debug-cleanup', function () {
    // Show all users
    $users = \App\Models\User::all(['id', 'name', 'email', 'role']);

    // Show all customers
    $customers = \App\Models\Customer::all(['id', 'name', 'phone', 'email']);

    // Delete guest customers
    $deletedGuests = \App\Models\Customer::where('name', 'Guest Customer')->delete();

    return response()->json([
        'users' => $users,
        'customers_before_cleanup' => $customers,
        'deleted_guest_customers' => $deletedGuests,
        'customers_after_cleanup' => \App\Models\Customer::all(['id', 'name', 'phone', 'email']),
    ]);
})->middleware('auth');

// Debug route to create sample customers
Route::get('/debug-customers', function () {
    $customers = \App\Models\Customer::all(['id', 'name', 'phone', 'email']);

    // If no customers, create some sample ones
    if ($customers->count() === 0) {
        $sampleCustomers = [
            ['name' => 'John Doe', 'phone' => '081234567890', 'email' => '<EMAIL>'],
            ['name' => 'Jane Smith', 'phone' => '081234567891', 'email' => '<EMAIL>'],
            ['name' => 'Bob Wilson', 'phone' => '081234567892', 'email' => '<EMAIL>'],
        ];

        foreach ($sampleCustomers as $customerData) {
            \App\Models\Customer::create($customerData);
        }

        $customers = \App\Models\Customer::all(['id', 'name', 'phone', 'email']);
    }

    return response()->json([
        'total_customers' => $customers->count(),
        'customers' => $customers,
    ]);
})->middleware('auth');

// Manual sync route for testing
Route::get('/manual-sync', function () {
    try {
        $productService = app(\App\Services\ProductService::class);
        $customerService = app(\App\Services\CustomerService::class);

        // Sync products
        $productResult = $productService->forceSyncFromApi();

        // Sync customers
        $customerResult = $customerService->forceSyncFromApi();

        // User sync disabled - using API-only authentication
        $userResult = [
            'success' => true,
            'message' => 'User sync skipped - using API-only authentication',
            'stats' => ['users_created' => 0, 'users_updated' => 0]
        ];

        return response()->json([
            'success' => true,
            'product_sync' => $productResult,
            'customer_sync' => $customerResult,
            'user_sync' => $userResult,
            'local_data_after_sync' => [
                'customers_count' => \App\Models\Customer::count(),
                'menu_items_count' => \App\Models\MenuItem::count(),
                'categories_count' => \App\Models\MenuCategory::count(),
            ]
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->middleware('auth');

require __DIR__.'/auth.php';
