<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Customer') }}
            </h2>
            <a href="{{ route('settings.customers.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Customers
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    
                    <!-- Customer Info Header -->
                    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Editing Customer Information</h3>
                        <p class="text-sm text-gray-600">Update the customer details below. All fields marked with * are required.</p>
                    </div>

                    <!-- Edit Form -->
                    <form method="POST" action="{{ route('settings.customers.update', $customer) }}">
                        @csrf
                        @method('PUT')

                        <!-- Customer Information Section -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Customer Information</h3>

                            <!-- Full Name -->
                            <div class="mb-4">
                                <x-input-label for="name" :value="__('Full Name *')" />
                                <x-text-input id="name"
                                              class="block mt-1 w-full"
                                              type="text"
                                              name="name"
                                              :value="old('name', $customer->name)"
                                              required
                                              autofocus />
                                <x-input-error :messages="$errors->get('name')" class="mt-2" />
                            </div>

                            <!-- Email and Phone in same row -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <!-- Email Address -->
                                <div>
                                    <x-input-label for="email" :value="__('Email Address')" />
                                    <x-text-input id="email"
                                                  class="block mt-1 w-full"
                                                  type="email"
                                                  name="email"
                                                  :value="old('email', $customer->email)" />
                                    <x-input-error :messages="$errors->get('email')" class="mt-2" />
                                </div>

                                <!-- Phone Number -->
                                <div>
                                    <x-input-label for="phone" :value="__('Phone Number *')" />
                                    <x-text-input id="phone"
                                                  class="block mt-1 w-full"
                                                  type="text"
                                                  name="phone"
                                                  :value="old('phone', $customer->phone)"
                                                  required />
                                    <x-input-error :messages="$errors->get('phone')" class="mt-2" />
                                    <p class="mt-1 text-sm text-gray-500">Format: 081234567890</p>
                                </div>
                            </div>

                            <!-- Date of Birth and Gender in same row -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <!-- Date of Birth -->
                                <div>
                                    <x-input-label for="date_of_birth" :value="__('Date of Birth')" />
                                    <x-text-input id="date_of_birth"
                                                  class="block mt-1 w-full"
                                                  type="date"
                                                  name="date_of_birth"
                                                  :value="old('date_of_birth', $customer->date_of_birth?->format('Y-m-d'))" />
                                    <x-input-error :messages="$errors->get('date_of_birth')" class="mt-2" />
                                </div>

                                <!-- Gender -->
                                <div>
                                    <x-input-label for="gender" :value="__('Gender')" />
                                    <select id="gender"
                                            name="gender"
                                            class="block mt-1 w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">Pilih salah satu opsi</option>
                                        <option value="male" {{ old('gender', $customer->gender) == 'male' ? 'selected' : '' }}>Male</option>
                                        <option value="female" {{ old('gender', $customer->gender) == 'female' ? 'selected' : '' }}>Female</option>
                                        <option value="other" {{ old('gender', $customer->gender) == 'other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                    <x-input-error :messages="$errors->get('gender')" class="mt-2" />
                                </div>
                            </div>

                            <!-- Customer Segment -->
                            <div class="mb-4">
                                <x-input-label for="customer_segment" :value="__('Customer Segment')" />
                                <select id="customer_segment"
                                        name="customer_segment"
                                        class="block mt-1 w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">Pilih salah satu opsi</option>
                                    <option value="regular" {{ old('customer_segment', $customer->customer_segment) == 'regular' ? 'selected' : '' }}>Regular</option>
                                    <option value="vip" {{ old('customer_segment', $customer->customer_segment) == 'vip' ? 'selected' : '' }}>VIP</option>
                                    <option value="premium" {{ old('customer_segment', $customer->customer_segment) == 'premium' ? 'selected' : '' }}>Premium</option>
                                </select>
                                <x-input-error :messages="$errors->get('customer_segment')" class="mt-2" />
                            </div>
                        </div>

                        <!-- Address Information Section -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Address Information</h3>

                            <!-- Address -->
                            <div class="mb-4">
                                <x-input-label for="address" :value="__('Address')" />
                                <textarea id="address"
                                          name="address"
                                          rows="3"
                                          class="block mt-1 w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                          placeholder="Enter full address...">{{ old('address', $customer->address) }}</textarea>
                                <x-input-error :messages="$errors->get('address')" class="mt-2" />
                            </div>

                            <!-- Detailed Address -->
                            <div class="mb-4">
                                <x-input-label for="detailed_address" :value="__('Detailed Address')" />
                                <textarea id="detailed_address"
                                          name="detailed_address"
                                          rows="3"
                                          class="block mt-1 w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                          placeholder="Enter detailed address...">{{ old('detailed_address', $customer->detailed_address) }}</textarea>
                                <x-input-error :messages="$errors->get('detailed_address')" class="mt-2" />
                            </div>

                            <!-- Postal Code -->
                            <div class="mb-4">
                                <x-input-label for="postal_code" :value="__('Postal Code')" />
                                <x-text-input id="postal_code"
                                              class="block mt-1 w-full"
                                              type="text"
                                              name="postal_code"
                                              :value="old('postal_code', $customer->postal_code)"
                                              maxlength="10" />
                                <x-input-error :messages="$errors->get('postal_code')" class="mt-2" />
                            </div>
                        </div>

                        <!-- Business Information Section -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Business Information</h3>

                            <!-- Loyalty Points and Active Status in same row -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <!-- Loyalty Points -->
                                <div>
                                    <x-input-label for="loyalty_points" :value="__('Loyalty Points')" />
                                    <x-text-input id="loyalty_points"
                                                  class="block mt-1 w-full"
                                                  type="number"
                                                  name="loyalty_points"
                                                  :value="old('loyalty_points', $customer->loyalty_points ?? 0)"
                                                  min="0" />
                                    <x-input-error :messages="$errors->get('loyalty_points')" class="mt-2" />
                                </div>

                                <!-- Active Status -->
                                <div>
                                    <x-input-label for="active_status" :value="__('Active Status')" />
                                    <div class="flex items-center mt-3">
                                        <label class="inline-flex items-center">
                                            <input type="checkbox"
                                                   id="active_status"
                                                   name="active_status"
                                                   value="1"
                                                   {{ old('active_status', $customer->active_status ?? true) ? 'checked' : '' }}
                                                   class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <span class="ml-2 text-sm text-gray-700">Active Status</span>
                                        </label>
                                    </div>
                                    <x-input-error :messages="$errors->get('active_status')" class="mt-2" />
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="mb-4">
                                <x-input-label for="notes" :value="__('Notes')" />
                                <textarea id="notes"
                                          name="notes"
                                          rows="4"
                                          class="block mt-1 w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                          placeholder="Enter any notes about the customer...">{{ old('notes', $customer->notes) }}</textarea>
                                <x-input-error :messages="$errors->get('notes')" class="mt-2" />
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-3">
                                <button type="submit" 
                                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded transition-colors duration-200">
                                    Update Customer
                                </button>
                                <a href="{{ route('settings.customers.index') }}"
                                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded transition-colors duration-200">
                                    Cancel
                                </a>
                            </div>
                            
                            <!-- Customer Stats -->
                            <div class="text-sm text-gray-500">
                                <p>Customer since: {{ $customer->created_at->format('M d, Y') }}</p>
                                @if($customer->orders()->count() > 0)
                                    <p>Total orders: {{ $customer->orders()->count() }}</p>
                                @endif
                            </div>
                        </div>

                    </form>

                </div>
            </div>
        </div>
    </div>
</x-app-layout>
