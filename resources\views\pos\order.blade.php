<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                @if($order->table)
                    {{ __('Order for ') . $order->table->name }}
                @else
                    {{ __('Order #') . $order->id . ' (' . ucfirst($order->order_type ?? 'Take Away') . ')' }}
                @endif
            </h2>
            <div class="flex space-x-2">
                <span class="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                    Order #{{ $order->id }}
                </span>
                <a href="{{ route('pos.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to POS
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- Menu Items Section -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold mb-4">Menu Items</h3>
                            
                            <!-- Category Tabs -->
                            <div class="border-b border-gray-200 mb-4">
                                <nav class="-mb-px flex space-x-8">
                                    @foreach($categories as $index => $category)
                                        <button onclick="showCategory({{ $category->id }})" 
                                                class="category-tab {{ $index === 0 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                                                data-category="{{ $category->id }}">
                                            {{ $category->name }}
                                        </button>
                                    @endforeach
                                </nav>
                            </div>

                            <!-- Menu Items Grid -->
                            @foreach($categories as $index => $category)
                                <div id="category-{{ $category->id }}" class="category-content {{ $index !== 0 ? 'hidden' : '' }}">
                                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                                        @foreach($category->menuItems as $item)
                                            <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                                @if($item->image_path)
                                                    <img src="{{ asset('storage/' . $item->image_path) }}"
                                                         alt="{{ $item->name }}"
                                                         class="w-full h-32 object-cover rounded-lg mb-3">
                                                @else
                                                    <div class="w-full h-32 bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                                                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                        </svg>
                                                    </div>
                                                @endif

                                                <h4 class="font-semibold text-gray-900 mb-1">{{ $item->name }}</h4>
                                                <p class="text-green-600 font-bold text-lg mb-2">${{ number_format($item->price, 2) }}</p>

                                                @if($item->description)
                                                    <p class="text-sm text-gray-600 mb-3">{{ Str::limit($item->description, 60) }}</p>
                                                @endif

                                                <!-- Simple Click to Add Link -->
                                                <div class="text-center">
                                                    <button onclick="addItemToOrder({{ $item->id }}, '{{ $item->name }}', {{ $item->price }})"
                                                            class="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors duration-200">
                                                        Click to add to cart
                                                    </button>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Order Summary Section -->
                <div class="lg:col-span-1">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg sticky top-6">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold mb-4">Order Summary</h3>

                            <!-- Order Info -->
                            <div class="bg-gray-50 rounded-lg p-3 mb-4 text-sm">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-gray-600">{{ $order->table ? 'Table:' : 'Order Type:' }}</span>
                                    <span class="font-medium">{{ $order->table ? $order->table->name : ucfirst($order->order_type ?? 'Take Away') }}</span>
                                </div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-gray-600">Party Size:</span>
                                    <div class="flex items-center">
                                        <input type="number" id="orderPartySize" min="1" max="20" value="{{ $order->party_size ?? 2 }}"
                                               class="w-16 text-sm border-gray-300 rounded focus:border-blue-500 focus:ring-blue-500 mr-1"
                                               onchange="updateOrderPartySize()">
                                        <span class="text-gray-500">guests</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Customer:</span>
                                    <span class="font-medium">{{ $order->customer->name }}</span>
                                </div>
                            </div>

                            <!-- Order Items -->
                            <div id="order-items" class="space-y-3 mb-4">
                                @forelse($order->orderItems as $orderItem)
                                    <div class="flex justify-between items-center border-b pb-2" data-item-id="{{ $orderItem->id }}">
                                        <div class="flex-1">
                                            <h5 class="font-medium text-sm">{{ $orderItem->menuItem->name }}</h5>
                                            @if($orderItem->notes)
                                                <p class="text-xs text-gray-500">{{ $orderItem->notes }}</p>
                                            @endif
                                            <p class="text-sm text-gray-600">${{ number_format($orderItem->price_at_time, 2) }} each</p>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <button onclick="updateQuantity({{ $orderItem->id }}, {{ $orderItem->quantity - 1 }})" 
                                                    class="bg-gray-200 hover:bg-gray-300 text-gray-700 w-6 h-6 rounded text-xs">-</button>
                                            <span class="quantity-{{ $orderItem->id }} font-medium">{{ $orderItem->quantity }}</span>
                                            <button onclick="updateQuantity({{ $orderItem->id }}, {{ $orderItem->quantity + 1 }})" 
                                                    class="bg-gray-200 hover:bg-gray-300 text-gray-700 w-6 h-6 rounded text-xs">+</button>
                                            <button onclick="removeItem({{ $orderItem->id }})" 
                                                    class="bg-red-500 hover:bg-red-600 text-white w-6 h-6 rounded text-xs">×</button>
                                        </div>
                                    </div>
                                @empty
                                    <p class="text-gray-500 text-center py-4">No items in order</p>
                                @endforelse
                            </div>

                            <!-- Order Total -->
                            <div class="border-t pt-4">
                                <div class="flex justify-between text-sm">
                                    <span>Subtotal:</span>
                                    <span id="subtotal">${{ number_format($order->subtotal_amount ?: $order->subtotal, 2) }}</span>
                                </div>

                                @if($order->discount_amount > 0)
                                    <div class="flex justify-between text-sm text-green-600">
                                        <span>
                                            Discount
                                            @if($order->discount_type === 'percentage')
                                                ({{ $order->discount_value }}%)
                                            @elseif($order->discount_type === 'fixed')
                                                (${{ number_format($order->discount_value, 2) }} off)
                                            @elseif($order->discount_type === 'coupon')
                                                ({{ $order->coupon_code }})
                                            @elseif($order->discount_type === 'points')
                                                ({{ $order->points_used }} points)
                                            @endif:
                                        </span>
                                        <span id="discount">-${{ number_format($order->discount_amount, 2) }}</span>
                                    </div>
                                @endif

                                <div class="flex justify-between text-sm">
                                    <span>Tax ({{ number_format($orderSettings['tax_rate'] * 100, 1) }}%):</span>
                                    <span id="tax">${{ number_format($order->tax_amount, 2) }}</span>
                                </div>
                                <div class="flex justify-between text-lg font-bold border-t pt-2 mt-2">
                                    <span>Total:</span>
                                    <span id="total">${{ number_format($order->total_amount, 2) }}</span>
                                </div>
                            </div>

                            <!-- Discount Management Section -->
                            <div class="mt-6 border-t pt-4">
                                <div class="mb-3">
                                    <button type="button" onclick="toggleOrderDiscountSection()"
                                            class="w-full text-left flex items-center justify-between text-sm font-medium text-blue-600 hover:text-blue-800">
                                        <span>Manage Discount</span>
                                        <svg id="orderDiscountToggleIcon" class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                </div>

                                <div id="orderDiscountSection" class="hidden space-y-3">
                                    <!-- Applied Discounts Display -->
                                    <div id="orderAppliedDiscountsSection" class="hidden">
                                        <h4 class="text-sm font-medium text-gray-700 mb-2">Applied Discounts</h4>
                                        <div id="orderAppliedDiscountsList" class="space-y-2 max-h-32 overflow-y-auto">
                                            <!-- Discounts will be loaded here -->
                                        </div>
                                    </div>

                                    <!-- Discount Type Selection -->
                                    <div>
                                        <label class="block text-xs font-medium text-gray-700 mb-1">Add New Discount</label>
                                        <select id="orderDiscountType" onchange="handleOrderDiscountTypeChange()"
                                                class="w-full text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500">
                                            <option value="none">Select Discount Type</option>
                                            <option value="percentage">Percentage Discount</option>
                                            <option value="fixed">Fixed Amount Discount</option>
                                            <option value="coupon">Coupon Code</option>
                                            <option value="points">Use Points</option>
                                        </select>
                                    </div>

                                    <!-- Percentage Discount -->
                                    <div id="orderPercentageDiscountSection" class="hidden">
                                        <label class="block text-xs font-medium text-gray-700 mb-1">Discount Percentage</label>
                                        <div class="flex items-center space-x-2">
                                            <input type="number" id="orderDiscountPercentage" min="0" max="100" step="0.01"
                                                   class="flex-1 text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
                                                   placeholder="Enter percentage">
                                            <span class="text-sm text-gray-500">%</span>
                                            <button onclick="applyOrderPercentageDiscount()"
                                                    class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs">
                                                Apply
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Fixed Amount Discount -->
                                    <div id="orderFixedDiscountSection" class="hidden">
                                        <label class="block text-xs font-medium text-gray-700 mb-1">Discount Amount</label>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-sm text-gray-500">$</span>
                                            <input type="number" id="orderDiscountAmount" min="0" step="0.01"
                                                   class="flex-1 text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
                                                   placeholder="Enter amount">
                                            <button onclick="applyOrderFixedDiscount()"
                                                    class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs">
                                                Apply
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Coupon Code -->
                                    <div id="orderCouponDiscountSection" class="hidden">
                                        <label class="block text-xs font-medium text-gray-700 mb-1">Coupon Code</label>
                                        <div class="flex items-center space-x-2">
                                            <input type="text" id="orderCouponCode"
                                                   class="flex-1 text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
                                                   placeholder="Enter coupon code">
                                            <button onclick="applyOrderCouponDiscount()"
                                                    class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-xs">
                                                Apply
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Points Discount -->
                                    <div id="orderPointsDiscountSection" class="hidden">
                                        <label class="block text-xs font-medium text-gray-700 mb-1">Use Points</label>
                                        <div class="text-xs text-gray-600 mb-2">
                                            Customer: {{ $order->customer->name }} | Available: <span id="orderCustomerPoints">{{ $order->customer->points ?? 0 }}</span> points (100 points = $1.00)
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <input type="number" id="orderPointsToUse" min="0" max="{{ $order->customer->points ?? 0 }}" step="1"
                                                   class="flex-1 text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
                                                   placeholder="Enter points to use">
                                            <button onclick="applyOrderPointsDiscount()"
                                                    class="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-xs">
                                                Apply
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="mt-6 space-y-2">
                                @if($order->orderItems->count() > 0)
                                    <form action="{{ route('pos.complete-order', $order) }}" method="POST">
                                        @csrf
                                        <button type="submit" class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-4 rounded">
                                            Complete & Process Payment
                                        </button>
                                    </form>
                                @endif

                                <form action="{{ route('pos.hold-order', $order) }}" method="POST">
                                    @csrf
                                    <button type="submit" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded">
                                        Hold Order
                                    </button>
                                </form>

                                <button onclick="showCancelModal()" class="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded">
                                    Cancel Order
                                </button>

                                <a href="{{ route('pos.kitchen', $order) }}" target="_blank" class="block w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded text-center">
                                    Print Kitchen Ticket
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Cancel Order Modal -->
    <div id="cancelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-96">
            <h3 class="text-lg font-semibold mb-4 text-red-600">Cancel Order</h3>
            <form action="{{ route('pos.cancel-order', $order) }}" method="POST">
                @csrf

                <div class="mb-4">
                    <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">Reason for cancellation (optional)</label>
                    <textarea id="reason" name="reason" rows="3"
                              class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                              placeholder="e.g., Customer changed mind, Kitchen error"></textarea>
                </div>

                <div class="flex justify-end space-x-2">
                    <button type="button" onclick="closeCancelModal()"
                            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">Keep Order</button>
                    <button type="submit"
                            class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">Cancel Order</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Simple Add Item Modal -->
    <div id="addItemModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-96 max-w-md mx-4">
            <h3 class="text-lg font-semibold mb-4">Add Item to Order</h3>
            <form id="addItemForm">
                @csrf
                <input type="hidden" id="menuItemId" name="menu_item_id">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Item</label>
                    <p id="itemName" class="text-lg font-semibold text-gray-900"></p>
                    <p id="itemPrice" class="text-green-600 font-bold"></p>
                </div>

                <div class="mb-4">
                    <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                    <input type="number" id="quantity" name="quantity" value="1" min="1"
                           class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>

                <div class="mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Special Notes (Optional)</label>
                    <textarea id="notes" name="notes" rows="2"
                              class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                              placeholder="e.g., No onions, Extra spicy"></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()"
                            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200">
                        Cancel
                    </button>
                    <button type="submit"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors duration-200">
                        Add to Order
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Simple Add Item Modal -->
    <div id="addItemModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-96 max-w-md mx-4">
            <h3 class="text-lg font-semibold mb-4">Add Item to Order</h3>
            <form id="addItemForm">
                @csrf
                <input type="hidden" id="menuItemId" name="menu_item_id">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Item</label>
                    <p id="itemName" class="text-lg font-semibold text-gray-900"></p>
                    <p id="itemPrice" class="text-green-600 font-bold"></p>
                </div>

                <div class="mb-4">
                    <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                    <input type="number" id="quantity" name="quantity" value="1" min="1"
                           class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>

                <div class="mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Special Notes (Optional)</label>
                    <textarea id="notes" name="notes" rows="2"
                              class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                              placeholder="e.g., No onions, Extra spicy"></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()"
                            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200">
                        Cancel
                    </button>
                    <button type="submit"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors duration-200">
                        Add to Order
                    </button>
                </div>
            </form>
        </div>
    </div>

    <style>
        /* Mobile responsive improvements */
        @media (max-width: 768px) {
            .grid.grid-cols-2.md\\:grid-cols-3 {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.75rem;
            }

            .border.rounded-lg.p-4 {
                padding: 0.75rem;
            }

            .w-7.h-7 {
                width: 1.5rem;
                height: 1.5rem;
                font-size: 0.75rem;
            }
        }

        /* Smooth transitions for quantity controls */
        .transition-all {
            transition: all 0.2s ease-in-out;
        }

        /* Button hover effects */
        .hover\\:shadow-md:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* Loading state for buttons */
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    </style>

    <script>
        // Category switching
        function showCategory(categoryId) {
            // Hide all categories
            document.querySelectorAll('.category-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Show selected category
            document.getElementById('category-' + categoryId).classList.remove('hidden');

            // Update tab styles
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('border-blue-500', 'text-blue-600');
                tab.classList.add('border-transparent', 'text-gray-500');
            });

            document.querySelector('[data-category="' + categoryId + '"]').classList.remove('border-transparent', 'text-gray-500');
            document.querySelector('[data-category="' + categoryId + '"]').classList.add('border-blue-500', 'text-blue-600');
        }

        // Add item to order (opens modal)
        function addItemToOrder(itemId, itemName, itemPrice) {
            document.getElementById('menuItemId').value = itemId;
            document.getElementById('itemName').textContent = itemName;
            document.getElementById('itemPrice').textContent = '$' + itemPrice.toFixed(2);
            document.getElementById('quantity').value = 1;
            document.getElementById('notes').value = '';

            document.getElementById('addItemModal').classList.remove('hidden');
            document.getElementById('addItemModal').classList.add('flex');
        }

        // Close modal
        function closeModal() {
            document.getElementById('addItemModal').classList.add('hidden');
            document.getElementById('addItemModal').classList.remove('flex');
        }

        // Handle add item form submission
        document.getElementById('addItemForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;

            // Show loading state
            submitButton.textContent = 'Adding...';
            submitButton.disabled = true;

            fetch('{{ route("pos.add-item", $order) }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeModal();
                    showAddToOrderFeedback();
                    // Update order summary dynamically
                    updateOrderSummary();
                } else {
                    alert('Error adding item to order');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error adding item to order');
            })
            .finally(() => {
                // Reset button state
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            });
        });

        // Update order summary dynamically
        function updateOrderSummary() {
            fetch('{{ route("pos.order-details", $order) }}', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update order items
                    document.getElementById('order-items').innerHTML = data.orderItemsHtml;

                    // Update totals
                    document.getElementById('subtotal').textContent = '$' + data.subtotal;

                    // Update discount display if exists
                    const discountElement = document.getElementById('discount');
                    if (data.hasDiscount && discountElement) {
                        discountElement.textContent = '-$' + data.discount;
                    }

                    document.getElementById('tax').textContent = '$' + data.tax;
                    document.getElementById('total').textContent = '$' + data.total;
                } else {
                    console.error('Failed to update order summary');
                }
            })
            .catch(error => {
                console.error('Error updating order summary:', error);
            });
        }

        // Show visual feedback when item is added
        function showAddToOrderFeedback() {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50 transition-opacity duration-300';
            notification.textContent = 'Item added to order!';
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 2000);
        }

        // Show cancel modal
        function showCancelModal() {
            document.getElementById('cancelModal').classList.remove('hidden');
            document.getElementById('cancelModal').classList.add('flex');
        }

        // Close cancel modal
        function closeCancelModal() {
            document.getElementById('cancelModal').classList.add('hidden');
            document.getElementById('cancelModal').classList.remove('flex');
        }



        // Update item quantity
        function updateQuantity(itemId, newQuantity) {
            if (newQuantity < 1) {
                removeItem(itemId);
                return;
            }

            fetch(`/pos/order/{{ $order->id }}/item/${itemId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ quantity: newQuantity })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update order summary dynamically
                    updateOrderSummary();
                }
            });
        }

        // Remove item from order
        function removeItem(itemId) {
            if (confirm('Remove this item from the order?')) {
                fetch(`/pos/order/{{ $order->id }}/item/${itemId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update order summary dynamically
                        updateOrderSummary();
                    }
                });
            }
        }

        // Order Discount Management Functions
        function toggleOrderDiscountSection() {
            const section = document.getElementById('orderDiscountSection');
            const icon = document.getElementById('orderDiscountToggleIcon');

            if (section.classList.contains('hidden')) {
                section.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                section.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }

        function handleOrderDiscountTypeChange() {
            const discountType = document.getElementById('orderDiscountType').value;

            // Hide all discount sections
            document.getElementById('orderPercentageDiscountSection').classList.add('hidden');
            document.getElementById('orderFixedDiscountSection').classList.add('hidden');
            document.getElementById('orderCouponDiscountSection').classList.add('hidden');
            document.getElementById('orderPointsDiscountSection').classList.add('hidden');

            // Show relevant section
            if (discountType === 'percentage') {
                document.getElementById('orderPercentageDiscountSection').classList.remove('hidden');
            } else if (discountType === 'fixed') {
                document.getElementById('orderFixedDiscountSection').classList.remove('hidden');
            } else if (discountType === 'coupon') {
                document.getElementById('orderCouponDiscountSection').classList.remove('hidden');
            } else if (discountType === 'points') {
                document.getElementById('orderPointsDiscountSection').classList.remove('hidden');
            }
        }

        function applyOrderPercentageDiscount() {
            const percentage = parseFloat(document.getElementById('orderDiscountPercentage').value);

            if (!percentage || percentage < 0 || percentage > 100) {
                alert('Please enter a valid percentage between 0 and 100.');
                return;
            }

            addOrderDiscount('percentage', percentage, null, null, `${percentage}% Discount`);
        }

        function applyOrderFixedDiscount() {
            const amount = parseFloat(document.getElementById('orderDiscountAmount').value);

            if (!amount || amount < 0) {
                alert('Please enter a valid discount amount.');
                return;
            }

            addOrderDiscount('fixed', amount, null, null, `$${amount.toFixed(2)} Off`);
        }

        function applyOrderCouponDiscount() {
            const couponCode = document.getElementById('orderCouponCode').value.trim();

            if (!couponCode) {
                alert('Please enter a coupon code.');
                return;
            }

            // Validate coupon first
            const subtotal = {{ $order->subtotal }};

            fetch('{{ route("pos.validate-coupon") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    coupon_code: couponCode,
                    order_amount: subtotal
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addOrderDiscount('coupon', data.coupon.value, couponCode, null, `Coupon: ${couponCode}`);
                } else {
                    alert(data.message || 'Invalid coupon code.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error validating coupon code.');
            });
        }

        function applyOrderPointsDiscount() {
            const pointsToUse = parseInt(document.getElementById('orderPointsToUse').value);
            const availablePoints = {{ $order->customer->points ?? 0 }};

            if (!pointsToUse || pointsToUse < 1) {
                alert('Please enter a valid number of points.');
                return;
            }

            if (pointsToUse > availablePoints) {
                alert('Not enough points available.');
                return;
            }

            const discountAmount = pointsToUse / 100; // 100 points = $1
            addOrderDiscount('points', discountAmount, null, pointsToUse, `${pointsToUse} Points Used`);
        }

        // Cumulative Discount System for Order Editing
        let orderDiscounts = [];

        function addOrderDiscount(type, value, couponCode = null, pointsUsed = null, description = null) {
            const discountData = {
                type: type,
                value: value,
                coupon_code: couponCode,
                points_used: pointsUsed,
                description: description
            };

            fetch(`/pos/order/{{ $order->id }}/add-discount`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(discountData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadOrderDiscounts();
                    updateOrderSummary();
                    clearOrderDiscountInputs();
                } else {
                    alert('Error applying discount: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error applying discount');
            });
        }

        function removeOrderDiscount(discountId) {
            if (confirm('Are you sure you want to remove this discount?')) {
                fetch(`/pos/order/{{ $order->id }}/remove-discount`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ discount_id: discountId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadOrderDiscounts();
                        updateOrderSummary();
                    } else {
                        alert('Error removing discount: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error removing discount');
                });
            }
        }

        function loadOrderDiscounts() {
            fetch(`/pos/order/{{ $order->id }}/discounts`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        orderDiscounts = data.discounts;
                        displayOrderDiscounts();
                    }
                })
                .catch(error => {
                    console.error('Error loading discounts:', error);
                });
        }

        function displayOrderDiscounts() {
            const section = document.getElementById('orderAppliedDiscountsSection');
            const list = document.getElementById('orderAppliedDiscountsList');

            if (!section || !list) return;

            if (orderDiscounts.length === 0) {
                section.classList.add('hidden');
                return;
            }

            section.classList.remove('hidden');
            list.innerHTML = '';

            orderDiscounts.forEach(discount => {
                const discountElement = document.createElement('div');
                discountElement.className = 'bg-green-50 border border-green-200 rounded p-2 flex items-center justify-between';
                discountElement.innerHTML = `
                    <div class="text-xs">
                        <span class="font-medium text-green-800">${discount.formatted_description}</span>
                        <span class="text-green-600">(-$${parseFloat(discount.amount).toFixed(2)})</span>
                    </div>
                    <button onclick="removeOrderDiscount(${discount.id})"
                            class="text-red-600 hover:text-red-800 text-xs">
                        Remove
                    </button>
                `;
                list.appendChild(discountElement);
            });
        }

        function clearOrderDiscountInputs() {
            document.getElementById('orderDiscountPercentage').value = '';
            document.getElementById('orderDiscountAmount').value = '';
            document.getElementById('orderCouponCode').value = '';
            document.getElementById('orderPointsToUse').value = '';
            document.getElementById('orderDiscountType').value = 'none';
            handleOrderDiscountTypeChange();
        }

        // Update party size
        function updateOrderPartySize() {
            const partySize = parseInt(document.getElementById('orderPartySize').value);

            if (partySize < 1 || partySize > 20) {
                alert('Party size must be between 1 and 20 guests.');
                document.getElementById('orderPartySize').value = {{ $order->party_size ?? 2 }};
                return;
            }

            // Update the order with new party size
            const orderData = {
                party_size: partySize,
                customer_id: {{ $order->customer_id }},
                order_type: '{{ $order->order_type }}',
                items: []
            };

            // Get current items
            @foreach($order->orderItems as $item)
                orderData.items.push({
                    menu_item_id: {{ $item->menu_item_id }},
                    quantity: {{ $item->quantity }},
                    notes: '{{ $item->notes ?? '' }}'
                });
            @endforeach

            fetch('{{ route("pos.update-order", $order) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(orderData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Party size updated successfully
                    console.log('Party size updated');
                } else {
                    alert('Error updating party size: ' + data.message);
                    document.getElementById('orderPartySize').value = {{ $order->party_size ?? 2 }};
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error updating party size');
                document.getElementById('orderPartySize').value = {{ $order->party_size ?? 2 }};
            });
        }

        // Load existing discounts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadOrderDiscounts();
        });
    </script>
</x-app-layout>
