# POS API Configuration
# Add these lines to your .env file for POS API integration

# Base URL for the POS API
POS_API_BASE_URL=http://viera-filament.test/api/pos

# Authentication credentials for the POS API
POS_API_EMAIL=<EMAIL>
POS_API_PASSWORD=M4$ukaja
POS_API_DEVICE_NAME=Oppo
POS_API_LOCATION_ID=

# API timeout in seconds
POS_API_TIMEOUT=30

# Token caching duration in seconds (1 hour = 3600)
POS_TOKEN_CACHE_DURATION=3600

# Token cache key
POS_TOKEN_CACHE_KEY=pos_api_token

# Product caching duration in seconds (5 minutes = 300)
POS_PRODUCT_CACHE_DURATION=300

# Product cache key
POS_PRODUCT_CACHE_KEY=pos_products

# Whether to use local database as fallback when API is unavailable
POS_USE_LOCAL_FALLBACK=false

# Maximum retry attempts for API requests
POS_MAX_RETRY_ATTEMPTS=3

# Retry delay in milliseconds
POS_RETRY_DELAY=1000
