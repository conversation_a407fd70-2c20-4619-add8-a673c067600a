<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MenuItem;
use Illuminate\Support\Facades\Storage;

class MenuItemImageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define placeholder image mappings for each menu item with appropriate food placeholder services
        $imageMapping = [
            // <PERSON><PERSON><PERSON> (Main Dishes)
            'Nasi Goreng Spesial' => 'https://picsum.photos/400/300?random=1',
            'Mie Ayam Bakso' => 'https://picsum.photos/400/300?random=2',
            'A<PERSON>' => 'https://picsum.photos/400/300?random=3',
            'Gado-Gado' => 'https://picsum.photos/400/300?random=4',

            // Minuman (Beverages)
            'Es Teh <PERSON>' => 'https://picsum.photos/400/300?random=5',
            'Es Jeruk' => 'https://picsum.photos/400/300?random=6',
            '<PERSON><PERSON>' => 'https://picsum.photos/400/300?random=7',
            '<PERSON><PERSON>' => 'https://picsum.photos/400/300?random=8',

            // Appetizer
            'Kerupuk' => 'https://picsum.photos/400/300?random=9',
            'Tahu Goreng' => 'https://picsum.photos/400/300?random=10',
            'Tempe Mendoan' => 'https://picsum.photos/400/300?random=11',

            // Dessert
            'Es Krim Vanilla' => 'https://picsum.photos/400/300?random=12',
            'Pisang Goreng' => 'https://picsum.photos/400/300?random=13',
            'Puding Coklat' => 'https://picsum.photos/400/300?random=14',

            // Snack
            'Kentang Goreng' => 'https://picsum.photos/400/300?random=15',
            'Onion Rings' => 'https://picsum.photos/400/300?random=16',
            'Chicken Wings' => 'https://picsum.photos/400/300?random=17',
        ];

        $this->command->info('Starting to create placeholder images for menu items...');

        foreach ($imageMapping as $itemName => $imageUrl) {
            $menuItem = MenuItem::where('name', $itemName)->first();

            if ($menuItem && !$menuItem->image_path) {
                try {
                    $this->command->info("Processing: {$itemName}");

                    // Generate filename
                    $filename = 'menu-items/' . strtolower(str_replace(' ', '-', $itemName)) . '.jpg';

                    // Create a simple placeholder image using GD
                    $this->createPlaceholderImage($itemName, $filename);

                    // Update menu item
                    $menuItem->update(['image_path' => $filename]);

                    $this->command->info("✓ Placeholder image created for: {$itemName}");

                } catch (\Exception $e) {
                    $this->command->error("✗ Error processing {$itemName}: " . $e->getMessage());
                }
            } else if ($menuItem && $menuItem->image_path) {
                $this->command->info("- Image already exists for: {$itemName}");
            } else {
                $this->command->warn("- Menu item not found: {$itemName}");
            }
        }

        $this->command->info('Menu item image seeding completed!');
    }

    /**
     * Create a placeholder image for menu item
     */
    private function createPlaceholderImage($itemName, $filename)
    {
        // Create a 400x300 image with true color
        $width = 400;
        $height = 300;
        $image = imagecreatetruecolor($width, $height);

        // Enable anti-aliasing
        imageantialias($image, true);

        // Define colors based on food category
        $colors = $this->getColorsForItem($itemName);
        $backgroundColor = imagecolorallocate($image, $colors['bg'][0], $colors['bg'][1], $colors['bg'][2]);
        $textColor = imagecolorallocate($image, $colors['text'][0], $colors['text'][1], $colors['text'][2]);
        $accentColor = imagecolorallocate($image, $colors['accent'][0], $colors['accent'][1], $colors['accent'][2]);

        // Create gradient background
        $this->createGradientBackground($image, $width, $height, $colors);

        // Add decorative border
        $borderColor = imagecolorallocate($image, 255, 255, 255);
        imagerectangle($image, 5, 5, $width-6, $height-6, $borderColor);
        imagerectangle($image, 6, 6, $width-7, $height-7, $borderColor);

        // Add food icon/symbol (larger and more prominent)
        $this->addFoodIcon($image, $itemName, $accentColor, $width, $height);

        // Add text with better positioning
        $fontSize = 4;
        $text = strtoupper($itemName);

        // Split long text into multiple lines
        $lines = $this->wrapText($text, 20);
        $lineHeight = imagefontheight($fontSize) + 2;
        $totalTextHeight = count($lines) * $lineHeight;

        $startY = $height - 60 - $totalTextHeight;

        foreach ($lines as $i => $line) {
            $textWidth = imagefontwidth($fontSize) * strlen($line);
            $x = ($width - $textWidth) / 2;
            $y = $startY + ($i * $lineHeight);

            // Add text shadow
            imagestring($image, $fontSize, $x + 1, $y + 1, $line, imagecolorallocate($image, 0, 0, 0));
            imagestring($image, $fontSize, $x, $y, $line, $textColor);
        }

        // Add price placeholder
        $priceText = "MENU ITEM";
        $priceWidth = imagefontwidth(2) * strlen($priceText);
        $priceX = ($width - $priceWidth) / 2;
        $priceY = $height - 25;
        imagestring($image, 2, $priceX, $priceY, $priceText, $textColor);

        // Save image
        $fullPath = storage_path('app/public/' . $filename);

        // Ensure directory exists
        $directory = dirname($fullPath);
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        imagejpeg($image, $fullPath, 95);
        imagedestroy($image);
    }

    /**
     * Create gradient background
     */
    private function createGradientBackground($image, $width, $height, $colors)
    {
        $startColor = $colors['bg'];
        $endColor = $colors['gradient'];

        for ($y = 0; $y < $height; $y++) {
            $ratio = $y / $height;
            $r = $startColor[0] + ($endColor[0] - $startColor[0]) * $ratio;
            $g = $startColor[1] + ($endColor[1] - $startColor[1]) * $ratio;
            $b = $startColor[2] + ($endColor[2] - $startColor[2]) * $ratio;

            $color = imagecolorallocate($image, $r, $g, $b);
            imageline($image, 0, $y, $width, $y, $color);
        }
    }

    /**
     * Wrap text into multiple lines
     */
    private function wrapText($text, $maxLength)
    {
        $words = explode(' ', $text);
        $lines = [];
        $currentLine = '';

        foreach ($words as $word) {
            if (strlen($currentLine . ' ' . $word) <= $maxLength) {
                $currentLine .= ($currentLine ? ' ' : '') . $word;
            } else {
                if ($currentLine) {
                    $lines[] = $currentLine;
                }
                $currentLine = $word;
            }
        }

        if ($currentLine) {
            $lines[] = $currentLine;
        }

        return $lines;
    }

    /**
     * Get colors based on food item category
     */
    private function getColorsForItem($itemName)
    {
        // Define color schemes for different food types with gradient and accent colors
        $colorSchemes = [
            'rice' => [
                'bg' => [255, 248, 220],
                'gradient' => [255, 235, 180],
                'text' => [139, 69, 19],
                'accent' => [205, 133, 63]
            ],
            'noodle' => [
                'bg' => [255, 235, 205],
                'gradient' => [255, 218, 160],
                'text' => [160, 82, 45],
                'accent' => [210, 105, 30]
            ],
            'chicken' => [
                'bg' => [255, 228, 196],
                'gradient' => [255, 200, 150],
                'text' => [205, 133, 63],
                'accent' => [255, 140, 0]
            ],
            'vegetable' => [
                'bg' => [240, 255, 240],
                'gradient' => [200, 255, 200],
                'text' => [34, 139, 34],
                'accent' => [50, 205, 50]
            ],
            'beverage' => [
                'bg' => [230, 230, 250],
                'gradient' => [200, 200, 255],
                'text' => [72, 61, 139],
                'accent' => [100, 149, 237]
            ],
            'snack' => [
                'bg' => [255, 218, 185],
                'gradient' => [255, 190, 140],
                'text' => [210, 105, 30],
                'accent' => [255, 165, 0]
            ],
            'dessert' => [
                'bg' => [255, 192, 203],
                'gradient' => [255, 160, 180],
                'text' => [199, 21, 133],
                'accent' => [255, 20, 147]
            ],
            'default' => [
                'bg' => [245, 245, 245],
                'gradient' => [220, 220, 220],
                'text' => [105, 105, 105],
                'accent' => [128, 128, 128]
            ]
        ];

        // Determine category based on item name
        $itemLower = strtolower($itemName);

        if (strpos($itemLower, 'nasi') !== false) return $colorSchemes['rice'];
        if (strpos($itemLower, 'mie') !== false) return $colorSchemes['noodle'];
        if (strpos($itemLower, 'ayam') !== false) return $colorSchemes['chicken'];
        if (strpos($itemLower, 'gado') !== false || strpos($itemLower, 'tahu') !== false || strpos($itemLower, 'tempe') !== false) return $colorSchemes['vegetable'];
        if (strpos($itemLower, 'es') !== false || strpos($itemLower, 'jus') !== false || strpos($itemLower, 'kopi') !== false) return $colorSchemes['beverage'];
        if (strpos($itemLower, 'kentang') !== false || strpos($itemLower, 'onion') !== false || strpos($itemLower, 'wings') !== false || strpos($itemLower, 'kerupuk') !== false) return $colorSchemes['snack'];
        if (strpos($itemLower, 'krim') !== false || strpos($itemLower, 'pisang') !== false || strpos($itemLower, 'puding') !== false) return $colorSchemes['dessert'];

        return $colorSchemes['default'];
    }

    /**
     * Add food icon to the image
     */
    private function addFoodIcon($image, $itemName, $color, $width, $height)
    {
        $centerX = $width / 2;
        $centerY = $height / 3;

        $itemLower = strtolower($itemName);

        // Set line thickness
        imagesetthickness($image, 3);

        if (strpos($itemLower, 'nasi') !== false) {
            // Draw a bowl with rice
            imageellipse($image, $centerX, $centerY, 80, 50, $color);
            imageellipse($image, $centerX, $centerY - 10, 60, 30, $color);
            // Add rice grains
            for ($i = 0; $i < 8; $i++) {
                $x = $centerX + rand(-25, 25);
                $y = $centerY + rand(-15, 5);
                imageellipse($image, $x, $y, 3, 2, $color);
            }
        } elseif (strpos($itemLower, 'mie') !== false) {
            // Draw noodle bowl with chopsticks
            imageellipse($image, $centerX, $centerY, 80, 50, $color);
            // Noodles (wavy lines)
            for ($i = 0; $i < 5; $i++) {
                $startX = $centerX - 30 + ($i * 15);
                $startY = $centerY - 10;
                imagearc($image, $startX, $startY, 20, 15, 0, 180, $color);
            }
            // Chopsticks
            imageline($image, $centerX + 40, $centerY - 40, $centerX + 50, $centerY - 10, $color);
            imageline($image, $centerX + 45, $centerY - 40, $centerX + 55, $centerY - 10, $color);
        } elseif (strpos($itemLower, 'ayam') !== false) {
            // Draw chicken drumstick
            imageellipse($image, $centerX, $centerY, 30, 60, $color);
            imageellipse($image, $centerX, $centerY - 20, 40, 30, $color);
            // Bone end
            imagerectangle($image, $centerX - 5, $centerY + 25, $centerX + 5, $centerY + 35, $color);
        } elseif (strpos($itemLower, 'gado') !== false || strpos($itemLower, 'tahu') !== false || strpos($itemLower, 'tempe') !== false) {
            // Draw vegetables/salad
            imageellipse($image, $centerX, $centerY, 80, 50, $color);
            // Vegetable pieces
            imageellipse($image, $centerX - 15, $centerY - 5, 15, 10, $color);
            imageellipse($image, $centerX + 10, $centerY - 8, 12, 8, $color);
            imageellipse($image, $centerX - 5, $centerY + 5, 10, 12, $color);
        } elseif (strpos($itemLower, 'es') !== false || strpos($itemLower, 'jus') !== false || strpos($itemLower, 'kopi') !== false) {
            // Draw glass/cup
            imagerectangle($image, $centerX - 25, $centerY - 30, $centerX + 25, $centerY + 30, $color);
            imageline($image, $centerX - 20, $centerY - 25, $centerX + 20, $centerY - 25, $color);
            // Handle for coffee
            if (strpos($itemLower, 'kopi') !== false) {
                imagearc($image, $centerX + 25, $centerY, 20, 30, 270, 90, $color);
            }
            // Ice cubes for cold drinks
            if (strpos($itemLower, 'es') !== false) {
                imagerectangle($image, $centerX - 10, $centerY - 15, $centerX - 5, $centerY - 10, $color);
                imagerectangle($image, $centerX + 5, $centerY - 10, $centerX + 10, $centerY - 5, $color);
            }
        } elseif (strpos($itemLower, 'kentang') !== false || strpos($itemLower, 'onion') !== false || strpos($itemLower, 'wings') !== false || strpos($itemLower, 'kerupuk') !== false) {
            // Draw snack items
            if (strpos($itemLower, 'kentang') !== false) {
                // French fries
                for ($i = 0; $i < 6; $i++) {
                    $x = $centerX - 25 + ($i * 10);
                    imagerectangle($image, $x, $centerY - 20, $x + 3, $centerY + 20, $color);
                }
            } else {
                // Generic snack
                imageellipse($image, $centerX, $centerY, 60, 40, $color);
                imageellipse($image, $centerX - 15, $centerY - 5, 20, 15, $color);
                imageellipse($image, $centerX + 15, $centerY + 5, 18, 12, $color);
            }
        } elseif (strpos($itemLower, 'krim') !== false || strpos($itemLower, 'pisang') !== false || strpos($itemLower, 'puding') !== false) {
            // Draw dessert
            if (strpos($itemLower, 'krim') !== false) {
                // Ice cream cone
                imageline($image, $centerX - 15, $centerY + 20, $centerX, $centerY + 40, $color);
                imageline($image, $centerX + 15, $centerY + 20, $centerX, $centerY + 40, $color);
                imageline($image, $centerX - 15, $centerY + 20, $centerX + 15, $centerY + 20, $color);
                imageellipse($image, $centerX, $centerY, 30, 30, $color);
            } else {
                // Generic dessert
                imageellipse($image, $centerX, $centerY, 60, 40, $color);
                imageellipse($image, $centerX, $centerY - 15, 40, 20, $color);
            }
        } else {
            // Default: draw a plate with food
            imageellipse($image, $centerX, $centerY, 80, 20, $color);
            imageellipse($image, $centerX, $centerY - 10, 50, 30, $color);
        }

        // Reset line thickness
        imagesetthickness($image, 1);
    }
}
