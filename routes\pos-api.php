<?php

use App\Http\Controllers\PosApiController;
use Illuminate\Support\Facades\Route;

/**
 * POS API Routes
 * Routes for interacting with external POS API
 */

Route::prefix('pos-api')->name('pos-api.')->group(function () {
    
    // Basic product fetching
    Route::get('/products', [PosApiController::class, 'fetchProducts'])
        ->name('fetch-products');
    
    // Product fetching with cache
    Route::get('/products/cached', [PosApiController::class, 'fetchProductsWithCache'])
        ->name('fetch-products-cached');
    
    // Get formatted products api link beda
    Route::get('/products/formatted', [PosApiController::class, 'getFormattedProducts'])
        ->name('formatted-products');
    
    // Test API connection
    Route::get('/test-connection', [PosApiController::class, 'testConnection'])
        ->name('test-connection');
    
    // Sync products to local database
    Route::post('/sync-products', [PosApiController::class, 'syncProducts'])
        ->name('sync-products');
    
    // Get API status and statistics
    Route::get('/status', [PosApiController::class, 'getApiStatus'])
        ->name('status');
    
});

// Test page route
Route::get('/pos-api-test', function () {
    return view('pos-api-test');
})->name('pos-api-test');

/**
 * Alternative: Add to existing web.php or api.php routes
 *
 * For web.php (if you want web middleware):
 * Route::middleware(['web', 'auth'])->group(function () {
 *     // ... routes here
 * });
 *
 * For api.php (if you want API middleware):
 * Route::middleware(['api'])->group(function () {
 *     // ... routes here
 * });
 */
