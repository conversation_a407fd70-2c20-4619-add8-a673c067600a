<?php

namespace App\Console\Commands;

use App\Services\ProductService;
use App\Services\PosApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class TestPosIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:test-integration {--clear-cache : Clear all POS caches before testing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test POS API integration and display status information';

    private ProductService $productService;
    private PosApiService $posApiService;

    public function __construct(ProductService $productService, PosApiService $posApiService)
    {
        parent::__construct();
        $this->productService = $productService;
        $this->posApiService = $posApiService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Testing POS API Integration...');
        $this->newLine();

        // Clear cache if requested
        if ($this->option('clear-cache')) {
            $this->info('🧹 Clearing POS caches...');
            $this->productService->clearCache();
            $this->posApiService->clearAuthToken();
            $this->info('✅ Caches cleared');
            $this->newLine();
        }

        // Test API connection
        $this->info('🌐 Testing API Connection...');
        $connectionResult = $this->posApiService->testConnection();
        
        if ($connectionResult['success']) {
            $this->info('✅ API Connection: SUCCESS');
            $this->info("   Status Code: {$connectionResult['status_code']}");
            if (isset($connectionResult['response_time'])) {
                $this->info("   Response Time: {$connectionResult['response_time']}ms");
            }
        } else {
            $this->error('❌ API Connection: FAILED');
            $this->error("   Error: {$connectionResult['error']}");
        }
        $this->newLine();

        // Test authentication
        $this->info('🔐 Testing Authentication...');
        $authResult = $this->posApiService->authenticate();
        
        if ($authResult['success']) {
            $this->info('✅ Authentication: SUCCESS');
            $this->info("   Token Length: " . strlen($authResult['token']));
        } else {
            $this->error('❌ Authentication: FAILED');
            $this->error("   Error: {$authResult['error']}");
        }
        $this->newLine();

        // Test product fetching
        $this->info('📦 Testing Product Fetching...');
        $categories = $this->productService->getCategoriesWithItems();
        
        if ($categories->count() > 0) {
            $this->info('✅ Product Fetching: SUCCESS');
            $this->info("   Categories Found: {$categories->count()}");
            
            $totalItems = $categories->sum(function ($category) {
                return $category->menuItems->count();
            });
            $this->info("   Total Items: {$totalItems}");
            
            // Show data source info
            $sourceInfo = $this->productService->getDataSourceInfo();
            $this->info("   Data Source: {$sourceInfo['source']}");
            
        } else {
            $this->error('❌ Product Fetching: FAILED');
            $this->error('   No categories found');
        }
        $this->newLine();

        // Display configuration
        $this->info('⚙️  Configuration:');
        $this->info('   API Base URL: ' . config('pos.api_base_url'));
        $this->info('   API Email: ' . (config('pos.api_email') ? '✅ Set' : '❌ Not Set'));
        $this->info('   API Password: ' . (config('pos.api_password') ? '✅ Set' : '❌ Not Set'));
        $this->info('   Device Name: ' . (config('pos.api_device_name') ? '✅ Set' : '❌ Not Set'));
        $this->info('   Use Local Fallback: ' . (config('pos.use_local_fallback') ? '✅ Yes' : '❌ No'));
        $this->info('   Token Cache Duration: ' . config('pos.token_cache_duration') . ' seconds');
        $this->info('   Product Cache Duration: ' . config('pos.product_cache_duration') . ' seconds');
        $this->newLine();

        // Display cache status
        $this->info('💾 Cache Status:');
        $tokenCached = Cache::has(config('pos.token_cache_key'));
        $productsCached = Cache::has(config('pos.product_cache_key'));
        
        $this->info('   Auth Token: ' . ($tokenCached ? '✅ Cached' : '❌ Not Cached'));
        $this->info('   Products: ' . ($productsCached ? '✅ Cached' : '❌ Not Cached'));
        $this->newLine();

        // Show sample data
        if ($categories->count() > 0) {
            $this->info('📋 Sample Data:');
            $firstCategory = $categories->first();
            $this->info("   First Category: {$firstCategory->name}");
            
            if ($firstCategory->menuItems->count() > 0) {
                $firstItem = $firstCategory->menuItems->first();
                $this->info("   First Item: {$firstItem->name} - \${$firstItem->price}");
            }
        }

        $this->newLine();
        $this->info('🎉 Integration test completed!');
        
        return Command::SUCCESS;
    }
}
