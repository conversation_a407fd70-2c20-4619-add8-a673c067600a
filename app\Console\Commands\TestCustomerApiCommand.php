<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestCustomerApiCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:test-customer-api';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test customer API endpoint directly';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔄 Testing Customer API Endpoint');
        $this->newLine();

        // Get auth token from session/cache
        $token = session('api_token') ?? \Cache::get('pos_api_token');
        
        if (!$token) {
            $this->error('❌ No API token found. Please login first.');
            return 1;
        }

        $this->info('✅ API Token found: ' . substr($token, 0, 20) . '...');
        $this->newLine();

        // Test customer API endpoint
        $baseUrl = config('pos.api_base_url', 'http://viera-filament.test/api/pos');
        $url = $baseUrl . '/sync/customers';
        
        $this->info("🌐 Testing URL: {$url}");
        $this->info("⏱️  Timeout: 30 seconds");
        $this->newLine();

        try {
            $startTime = microtime(true);
            
            $response = Http::timeout(30)
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $token,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ])
                ->get($url);

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            $this->info("⏱️  Request completed in {$duration}ms");
            $this->newLine();

            if ($response->successful()) {
                $data = $response->json();
                $this->info('✅ API Response: SUCCESS');
                $this->info('📊 Status Code: ' . $response->status());
                
                if (isset($data['customers'])) {
                    $count = count($data['customers']);
                    $this->info("👥 Customers found: {$count}");
                    
                    if ($count > 0) {
                        $this->info('📝 Sample customer:');
                        $sample = $data['customers'][0];
                        $this->info('  - ID: ' . ($sample['id'] ?? 'N/A'));
                        $this->info('  - Name: ' . ($sample['nama'] ?? $sample['name'] ?? 'N/A'));
                        $this->info('  - Email: ' . ($sample['email'] ?? 'N/A'));
                        $this->info('  - Phone: ' . ($sample['telepon'] ?? $sample['phone'] ?? 'N/A'));
                    }
                } else {
                    $this->warning('⚠️  No customers array in response');
                    $this->info('📄 Response structure: ' . json_encode(array_keys($data), JSON_PRETTY_PRINT));
                }
                
            } else {
                $this->error('❌ API Response: FAILED');
                $this->error('📊 Status Code: ' . $response->status());
                $this->error('📄 Response: ' . $response->body());
            }

        } catch (\Exception $e) {
            $this->error('❌ Exception occurred: ' . $e->getMessage());
            $this->error('🔍 Exception type: ' . get_class($e));
        }

        return 0;
    }
}
