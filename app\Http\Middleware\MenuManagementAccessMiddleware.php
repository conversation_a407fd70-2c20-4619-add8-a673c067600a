<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class MenuManagementAccessMiddleware
{
    /**
     * Handle an incoming request.
     * Check if user has proper role and j<PERSON><PERSON> to access Menu Management features.
     * <PERSON><PERSON><PERSON> role is excluded from menu management.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Check if user can access menu management features
        if (!$user->canAccessMenuManagement()) {
            // Show Indonesian error message for menu management access
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Anda tidak memiliki akses ke manajemen menu'], 403);
            }
            
            // For web requests: logout user and redirect to login with popup message
            auth()->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();
            
            return redirect()->route('login')->with('error', 'Anda tidak memiliki akses ke manajemen menu');
        }

        return $next($request);
    }
}
