<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Create Product') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-8 text-gray-900">
                    <form action="{{ route('menu-items.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf

                        <!-- Product Information Section -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 mb-6">Product Information</h3>

                            <!-- Product Name -->
                            <div class="mb-6">
                                <x-input-label for="name" :value="__('Product Name')" class="text-sm font-medium text-gray-700" />
                                <x-text-input id="name"
                                    class="block mt-2 w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                    type="text"
                                    name="name"
                                    :value="old('name')"
                                    required
                                    autofocus
                                    placeholder="Enter product name" />
                                <x-input-error :messages="$errors->get('name')" class="mt-2" />
                            </div>

                            <!-- SKU and Barcode Row -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <x-input-label for="sku" :value="__('SKU')" class="text-sm font-medium text-gray-700" />
                                    <x-text-input id="sku"
                                        class="block mt-2 w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                        type="text"
                                        name="sku"
                                        :value="old('sku')"
                                        placeholder="Enter SKU" />
                                    <x-input-error :messages="$errors->get('sku')" class="mt-2" />
                                </div>

                                <div>
                                    <x-input-label for="barcode" :value="__('Barcode')" class="text-sm font-medium text-gray-700" />
                                    <x-text-input id="barcode"
                                        class="block mt-2 w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                        type="text"
                                        name="barcode"
                                        :value="old('barcode')"
                                        placeholder="Enter barcode" />
                                    <x-input-error :messages="$errors->get('barcode')" class="mt-2" />
                                </div>
                            </div>

                            <!-- Category -->
                            <div class="mb-6">
                                <x-input-label for="category_id" :value="__('Category')" class="text-sm font-medium text-gray-700" />
                                <select id="category_id"
                                    name="category_id"
                                    class="block mt-2 w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                    required>
                                    <option value="">Pilih produk atau layanan</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <x-input-error :messages="$errors->get('category_id')" class="mt-2" />
                            </div>

                            <!-- Description -->
                            <div class="mb-6">
                                <x-input-label for="description" :value="__('Description')" class="text-sm font-medium text-gray-700" />
                                <textarea id="description"
                                    name="description"
                                    rows="4"
                                    class="block mt-2 w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm resize-none"
                                    placeholder="Enter product description">{{ old('description') }}</textarea>
                                <x-input-error :messages="$errors->get('description')" class="mt-2" />
                            </div>
                        </div>

                        <!-- Pricing & Inventory Section -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 mb-6">Pricing & Inventory</h3>

                            <!-- Selling Price and Cost Price Row -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <x-input-label for="price" :value="__('Selling Price')" class="text-sm font-medium text-gray-700" />
                                    <div class="relative mt-2">
                                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">Rp</span>
                                        <x-text-input id="price"
                                            class="block w-full pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                            type="number"
                                            step="0.01"
                                            min="0"
                                            name="price"
                                            :value="old('price')"
                                            required
                                            placeholder="0" />
                                    </div>
                                    <x-input-error :messages="$errors->get('price')" class="mt-2" />
                                </div>

                                <div>
                                    <x-input-label for="cost_price" :value="__('Cost Price')" class="text-sm font-medium text-gray-700" />
                                    <div class="relative mt-2">
                                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">Rp</span>
                                        <x-text-input id="cost_price"
                                            class="block w-full pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                            type="number"
                                            step="0.01"
                                            min="0"
                                            name="cost_price"
                                            :value="old('cost_price')"
                                            placeholder="0" />
                                    </div>
                                    <x-input-error :messages="$errors->get('cost_price')" class="mt-2" />
                                </div>
                            </div>

                            <!-- Stock Quantity and Active Status Row -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <x-input-label for="stock_quantity" :value="__('Stock Quantity')" class="text-sm font-medium text-gray-700" />
                                    <x-text-input id="stock_quantity"
                                        class="block mt-2 w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                        type="number"
                                        min="0"
                                        name="stock_quantity"
                                        :value="old('stock_quantity', 0)"
                                        placeholder="0" />
                                    <x-input-error :messages="$errors->get('stock_quantity')" class="mt-2" />
                                </div>

                                <div class="flex items-center">
                                    <div class="flex items-center mt-8">
                                        <label class="inline-flex items-center">
                                            <input type="checkbox"
                                                name="is_active"
                                                value="1"
                                                {{ old('is_active', true) ? 'checked' : '' }}
                                                class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                            <span class="ml-3 text-sm font-medium text-gray-700">Active Status</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Food Item Toggle -->
                            <div class="mb-6">
                                <div class="flex items-center">
                                    <input type="checkbox"
                                        id="is_food_item"
                                        name="is_food_item"
                                        value="1"
                                        {{ old('is_food_item', true) ? 'checked' : '' }}
                                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                    <label for="is_food_item" class="ml-3 text-sm font-medium text-gray-700">
                                        Food Item
                                    </label>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">Check if this is a food or beverage item</p>
                            </div>
                        </div>

                        <!-- Network Status Warning -->
                        <div id="network-warning" class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md hidden">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800">
                                        Koneksi Internet Diperlukan
                                    </h3>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p>Produk hanya dapat dibuat saat terhubung ke internet untuk sinkronisasi dengan API. Pastikan koneksi internet Anda stabil.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                            <a href="{{ route('menu-items.index') }}"
                               class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Cancel
                            </a>
                            <button type="submit"
                                    id="create-product-btn"
                                    class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed">
                                <span id="btn-text">Create Product</span>
                                <span id="btn-loading" class="hidden">
                                    <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Creating...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const submitBtn = document.getElementById('create-product-btn');
            const btnText = document.getElementById('btn-text');
            const btnLoading = document.getElementById('btn-loading');
            const networkWarning = document.getElementById('network-warning');

            // Check network status on page load
            checkNetworkStatus();

            // Listen for network status changes
            if (window.networkMonitor) {
                window.networkMonitor.onStatusChange(function(status, details) {
                    updateNetworkStatus(status);
                });
            }

            // Check network status periodically
            setInterval(checkNetworkStatus, 30000); // Check every 30 seconds

            function checkNetworkStatus() {
                // Check if we're in development mode (assume localhost/127.0.0.1 is development)
                const isDevelopment = window.location.hostname === 'localhost' ||
                                    window.location.hostname === '127.0.0.1' ||
                                    window.location.hostname.includes('.test');

                // First check if we're online at all
                if (!navigator.onLine) {
                    updateNetworkStatus('disconnected');
                    return;
                }

                // Then check our local health endpoint
                fetch('/api/health-check', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    },
                    cache: 'no-cache'
                })
                .then(response => {
                    if (response.ok) {
                        // In development, if local server works, assume network is OK
                        if (isDevelopment) {
                            updateNetworkStatus('connected');
                            return;
                        }

                        // In production, also check external connectivity
                        return fetch('https://www.google.com/favicon.ico', {
                            method: 'GET',
                            mode: 'no-cors',
                            cache: 'no-cache'
                        });
                    } else {
                        throw new Error('Local server not responding');
                    }
                })
                .then(() => {
                    updateNetworkStatus('connected');
                })
                .catch(error => {
                    console.log('Network check failed:', error);

                    // In development, be more lenient
                    if (isDevelopment) {
                        console.log('Development mode: allowing product creation despite network check failure');
                        updateNetworkStatus('connected');
                    } else {
                        updateNetworkStatus('disconnected');
                    }
                });
            }

            function updateNetworkStatus(status) {
                if (status === 'connected') {
                    // Enable form submission
                    submitBtn.disabled = false;
                    submitBtn.classList.remove('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                    submitBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');
                    networkWarning.classList.add('hidden');
                } else {
                    // Disable form submission
                    submitBtn.disabled = true;
                    submitBtn.classList.add('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                    submitBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                    networkWarning.classList.remove('hidden');
                }
            }

            // Handle form submission
            form.addEventListener('submit', function(e) {
                if (submitBtn.disabled) {
                    e.preventDefault();
                    alert('Koneksi internet diperlukan untuk membuat produk. Pastikan Anda terhubung ke internet.');
                    return false;
                }

                // Show loading state
                btnText.classList.add('hidden');
                btnLoading.classList.remove('hidden');
                submitBtn.disabled = true;
            });

            // Validate required fields
            const requiredFields = ['name', 'price', 'category_id'];
            requiredFields.forEach(fieldName => {
                const field = document.querySelector(`[name="${fieldName}"]`);
                if (field) {
                    field.addEventListener('input', validateForm);
                    field.addEventListener('change', validateForm);
                }
            });

            function validateForm() {
                let allValid = true;
                requiredFields.forEach(fieldName => {
                    const field = document.querySelector(`[name="${fieldName}"]`);
                    if (field && !field.value.trim()) {
                        allValid = false;
                    }
                });

                // Only enable if network is available AND form is valid
                if (allValid && !submitBtn.classList.contains('disabled:bg-gray-400')) {
                    submitBtn.disabled = false;
                } else {
                    submitBtn.disabled = true;
                }
            }

            // Initial form validation
            validateForm();
        });
    </script>
</x-app-layout>
