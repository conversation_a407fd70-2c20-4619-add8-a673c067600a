<?php

namespace App\Console\Commands;

use App\Services\ProductService;
use App\Services\PosApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;

class TestApiIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:test-api-integration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test API integration with the correct credentials';

    private ProductService $productService;
    private PosApiService $posApiService;

    public function __construct(ProductService $productService, PosApiService $posApiService)
    {
        parent::__construct();
        $this->productService = $productService;
        $this->posApiService = $posApiService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Testing POS API Integration with correct credentials...');
        $this->newLine();

        // Temporarily set the API credentials for testing
        Config::set('pos.api_email', '<EMAIL>');
        Config::set('pos.api_password', 'M4$ukaja');
        Config::set('pos.api_device_name', 'Oppo');
        Config::set('pos.api_location_id', '');

        // Clear any cached data
        $this->productService->clearCache();
        $this->posApiService->clearAuthToken();

        // Test authentication
        $this->info('🔐 Testing Authentication...');
        $authResult = $this->posApiService->authenticate();
        
        if ($authResult['success']) {
            $this->info('✅ Authentication: SUCCESS');
            $this->info("   Token Length: " . strlen($authResult['token']));
        } else {
            $this->error('❌ Authentication: FAILED');
            $this->error("   Error: {$authResult['error']}");
            return Command::FAILURE;
        }
        $this->newLine();

        // Test product fetching
        $this->info('📦 Testing Product Fetching from API...');
        $categories = $this->productService->getCategoriesWithItems();
        
        if ($categories->count() > 0) {
            $this->info('✅ Product Fetching: SUCCESS');
            $this->info("   Categories Found: {$categories->count()}");
            
            $totalItems = $categories->sum(function ($category) {
                return $category->menuItems->count();
            });
            $this->info("   Total Items: {$totalItems}");
            
            // Show data source info
            $sourceInfo = $this->productService->getDataSourceInfo();
            $this->info("   Data Source: {$sourceInfo['source']}");
            
            $this->newLine();
            $this->info('📋 API Products Found:');
            foreach ($categories as $category) {
                $this->line("  📁 {$category->name} ({$category->menuItems->count()} items)");
                foreach ($category->menuItems as $item) {
                    $price = number_format($item->price, 0, ',', '.');
                    $this->line("    - {$item->name} (Rp {$price})");
                    if (!empty($item->sku)) {
                        $this->line("      SKU: {$item->sku}");
                    }
                }
            }
            
        } else {
            $this->error('❌ Product Fetching: FAILED');
            $this->error('   No categories found');
            return Command::FAILURE;
        }
        
        $this->newLine();
        $this->info('🎉 API Integration test completed successfully!');
        $this->newLine();
        
        // Show comparison with local data
        $this->info('📊 Comparison with Local Data:');
        $localCategories = $this->productService->getLocalCategoriesWithItems();
        $localTotalItems = $localCategories->sum(function ($category) {
            return $category->menuItems->count();
        });
        
        $this->line("   Local Categories: {$localCategories->count()}");
        $this->line("   Local Items: {$localTotalItems}");
        $this->line("   API Categories: {$categories->count()}");
        $this->line("   API Items: {$totalItems}");
        
        if ($localTotalItems !== $totalItems) {
            $this->newLine();
            $this->warn('⚠️  The API has different products than your local database.');
            $this->warn('⚠️  You may want to sync your local data to the API or vice versa.');
        }
        
        return Command::SUCCESS;
    }
}
