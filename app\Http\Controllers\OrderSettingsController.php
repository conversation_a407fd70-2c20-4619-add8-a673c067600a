<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\OrderSetting;
use Illuminate\Support\Facades\DB;

class OrderSettingsController extends Controller
{
    /**
     * Display the order settings page
     */
    public function index()
    {
        $categories = OrderSetting::getCategories();
        $settingsByCategory = [];

        foreach ($categories as $category) {
            $settingsByCategory[$category] = OrderSetting::getByCategory($category);
        }

        return view('settings.order-settings', compact('settingsByCategory', 'categories'));
    }

    /**
     * Update order settings
     */
    public function update(Request $request)
    {
        $request->validate([
            'settings' => 'required|array',
            'settings.*' => 'required',
        ]);

        try {
            DB::beginTransaction();

            foreach ($request->settings as $key => $value) {
                $setting = OrderSetting::where('key', $key)->first();

                if ($setting) {
                    // Cast value based on type
                    if ($setting->type === 'boolean') {
                        $value = $request->has("settings.{$key}") ? 'true' : 'false';
                    } elseif ($setting->type === 'number') {
                        $value = is_numeric($value) ? (string) $value : '0';
                    } elseif ($setting->type === 'select') {
                        // For select types, keep the value as is (already validated by form)
                        $value = (string) $value;
                    }

                    $setting->update(['value' => $value]);
                }
            }

            DB::commit();

            return redirect()->route('settings.order-settings')
                           ->with('success', 'Order settings updated successfully!');

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->route('settings.order-settings')
                           ->with('error', 'Error updating settings: ' . $e->getMessage());
        }
    }

    /**
     * Reset settings to default values
     */
    public function reset()
    {
        try {
            DB::beginTransaction();

            // Reset to default values
            $defaults = [
                'tax_rate' => '0.10',
                'service_charge_rate' => '0.00',
                'auto_apply_tax' => 'true',
                'auto_apply_service_charge' => 'false',
                'tax_charging_method' => 'customer_pays',
                'max_discount_percentage' => '50',
                'currency_symbol' => '$',
                'receipt_footer_text' => 'Thank you for dining with us!',
                'order_timeout_minutes' => '30',
            ];

            foreach ($defaults as $key => $value) {
                OrderSetting::where('key', $key)->update(['value' => $value]);
            }

            DB::commit();

            return redirect()->route('settings.order-settings')
                           ->with('success', 'Settings reset to default values successfully!');

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->route('settings.order-settings')
                           ->with('error', 'Error resetting settings: ' . $e->getMessage());
        }
    }

    /**
     * Get setting value for API
     */
    public function getSetting($key)
    {
        $value = OrderSetting::get($key);

        return response()->json([
            'key' => $key,
            'value' => $value
        ]);
    }
}
