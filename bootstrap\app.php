<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        then: function () {
            Route::middleware('web')
                ->group(base_path('routes/pos-api.php'));
        },
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->alias([
            'role' => \App\Http\Middleware\RoleMiddleware::class,
            'pos.access' => \App\Http\Middleware\PosAccessMiddleware::class,
            'menu.access' => \App\Http\Middleware\MenuManagementAccessMiddleware::class,
        ]);
    })
    ->withSchedule(function (Schedule $schedule): void {
        // Sync unsynced transactions every 15 minutes
        $schedule->command('pos:sync-transactions')
                 ->everyFifteenMinutes()
                 ->withoutOverlapping()
                 ->runInBackground()
                 ->appendOutputTo(storage_path('logs/transaction-sync.log'));
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
