<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS API Test</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-6xl">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-6">🚀 POS API Test Interface</h1>
            
            <div x-data="posApiTest()" class="space-y-6">
                <!-- Session Information -->
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h2 class="text-xl font-semibold text-purple-800 mb-3">🏪 Session Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <strong>API Token:</strong>
                            <span class="text-gray-600">{{ session('api_token') ? 'Available (' . strlen(session('api_token')) . ' chars)' : 'Not available' }}</span>
                        </div>
                        <div>
                            <strong>Outlet ID:</strong>
                            <span class="text-gray-600">{{ session('api_outlet_id') ?? 'Not available' }}</span>
                        </div>
                        <div>
                            <strong>User:</strong>
                            <span class="text-gray-600">{{ session('api_user.name') ?? 'Not logged in' }}</span>
                        </div>
                        <div>
                            <strong>Authenticated:</strong>
                            <span class="text-gray-600">{{ session('api_authenticated') ? 'Yes' : 'No' }}</span>
                        </div>
                    </div>
                </div>

                <!-- API Status -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h2 class="text-xl font-semibold text-blue-800 mb-3">📡 API Status</h2>
                    <button 
                        @click="testConnection()" 
                        :disabled="loading"
                        class="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                        <span x-show="!loading">Test Connection</span>
                        <span x-show="loading">Testing...</span>
                    </button>
                    
                    <div x-show="connectionResult" class="mt-3">
                        <div x-show="connectionResult?.success" class="text-green-600">
                            ✅ Connection successful! 
                            <span x-text="connectionResult?.response_time ? `(${Math.round(connectionResult.response_time * 1000)}ms)` : ''"></span>
                        </div>
                        <div x-show="connectionResult && !connectionResult?.success" class="text-red-600">
                            ❌ Connection failed: <span x-text="connectionResult?.error"></span>
                        </div>
                    </div>
                </div>

                <!-- Fetch Products -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h2 class="text-xl font-semibold text-green-800 mb-3">📦 Fetch Products</h2>
                    <div class="flex gap-3 mb-3">
                        <button 
                            @click="fetchProducts()" 
                            :disabled="loading"
                            class="bg-green-600 hover:bg-green-700 disabled:bg-green-300 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                            <span x-show="!loading">Fetch Products</span>
                            <span x-show="loading">Fetching...</span>
                        </button>
                        
                        <button 
                            @click="fetchProductsCached()" 
                            :disabled="loading"
                            class="bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                            <span x-show="!loading">Fetch Cached</span>
                            <span x-show="loading">Fetching...</span>
                        </button>
                        
                        <button 
                            @click="fetchFormattedProducts()" 
                            :disabled="loading"
                            class="bg-green-400 hover:bg-green-500 disabled:bg-green-300 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                            <span x-show="!loading">Fetch Formatted</span>
                            <span x-show="loading">Fetching...</span>
                        </button>
                    </div>
                    
                    <div x-show="productsResult" class="mt-3">
                        <div x-show="productsResult?.success" class="text-green-600 mb-2">
                            ✅ <span x-text="productsResult?.message"></span>
                            <span x-show="productsResult?.count">(Count: <span x-text="productsResult.count"></span>)</span>
                        </div>
                        <div x-show="productsResult && !productsResult?.success" class="text-red-600 mb-2">
                            ❌ <span x-text="productsResult?.message"></span>: <span x-text="productsResult?.error"></span>
                        </div>
                    </div>
                </div>

                <!-- Products Display -->
                <div x-show="products.length > 0" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h2 class="text-xl font-semibold text-gray-800 mb-3">📋 Products List</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-300 rounded-lg">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">ID</th>
                                    <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Name</th>
                                    <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Price</th>
                                    <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Category</th>
                                    <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template x-for="product in products.slice(0, 10)" :key="product.id">
                                    <tr class="border-t border-gray-200">
                                        <td class="px-4 py-2 text-sm text-gray-600" x-text="product.id"></td>
                                        <td class="px-4 py-2 text-sm text-gray-800 font-medium" x-text="product.name"></td>
                                        <td class="px-4 py-2 text-sm text-gray-600" x-text="'$' + parseFloat(product.price || 0).toFixed(2)"></td>
                                        <td class="px-4 py-2 text-sm text-gray-600" x-text="product.category?.name || product.category || 'N/A'"></td>
                                        <td class="px-4 py-2 text-sm">
                                            <span x-show="product.is_active" class="text-green-600">✅ Active</span>
                                            <span x-show="!product.is_active" class="text-red-600">❌ Inactive</span>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        <div x-show="products.length > 10" class="mt-2 text-sm text-gray-600 text-center">
                            ... and <span x-text="products.length - 10"></span> more products
                        </div>
                    </div>
                </div>

                <!-- Raw Response -->
                <div x-show="rawResponse" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h2 class="text-xl font-semibold text-gray-800 mb-3">🔍 Raw Response</h2>
                    <button 
                        @click="showRawResponse = !showRawResponse"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm mb-3"
                    >
                        <span x-show="!showRawResponse">Show Raw Response</span>
                        <span x-show="showRawResponse">Hide Raw Response</span>
                    </button>
                    <pre x-show="showRawResponse" class="bg-gray-800 text-green-400 p-4 rounded-lg overflow-x-auto text-sm" x-text="JSON.stringify(rawResponse, null, 2)"></pre>
                </div>

                <!-- Loading Indicator -->
                <div x-show="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                        <span class="text-gray-700">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function posApiTest() {
            return {
                loading: false,
                connectionResult: null,
                productsResult: null,
                products: [],
                rawResponse: null,
                showRawResponse: false,

                async testConnection() {
                    this.loading = true;
                    try {
                        const response = await fetch('/pos-api/test-connection');
                        this.connectionResult = await response.json();
                    } catch (error) {
                        this.connectionResult = { success: false, error: error.message };
                    }
                    this.loading = false;
                },

                async fetchProducts() {
                    this.loading = true;
                    try {
                        const response = await fetch('/pos-api/products');
                        const result = await response.json();
                        this.productsResult = result;
                        this.rawResponse = result;
                        
                        if (result.success && result.data?.data) {
                            this.products = result.data.data;
                        }
                    } catch (error) {
                        this.productsResult = { success: false, error: error.message };
                    }
                    this.loading = false;
                },

                async fetchProductsCached() {
                    this.loading = true;
                    try {
                        const response = await fetch('/pos-api/products/cached');
                        const result = await response.json();
                        this.productsResult = result;
                        this.rawResponse = result;
                        
                        if (result.success && result.data?.data) {
                            this.products = result.data.data;
                        }
                    } catch (error) {
                        this.productsResult = { success: false, error: error.message };
                    }
                    this.loading = false;
                },

                async fetchFormattedProducts() {
                    this.loading = true;
                    try {
                        const response = await fetch('/pos-api/products/formatted');
                        const result = await response.json();
                        this.productsResult = result;
                        this.rawResponse = result;
                        
                        if (result.success && result.products) {
                            this.products = result.products;
                        }
                    } catch (error) {
                        this.productsResult = { success: false, error: error.message };
                    }
                    this.loading = false;
                }
            }
        }
    </script>
</body>
</html>
