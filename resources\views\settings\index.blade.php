<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Settings & Management') }}
            </h2>
            <a href="{{ route('pos.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Back to POS
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- System Statistics -->
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4">System Overview</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Customers Stats -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"/>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">Total Customers</p>
                                    <p class="text-2xl font-semibold text-gray-900">{{ $stats['customers'] }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Menu Items Stats -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 8a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 12a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 16a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">Menu Items</p>
                                    <p class="text-2xl font-semibold text-gray-900">{{ $stats['menu_items'] }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Orders Stats -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">Total Orders</p>
                                    <p class="text-2xl font-semibold text-gray-900">{{ $stats['total_orders'] }}</p>
                                    <p class="text-sm text-gray-500">{{ $stats['pending_orders'] }} pending</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Management Sections -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                
                <!-- Customer Management -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="h-8 w-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"/>
                            </svg>
                            <h3 class="ml-3 text-lg font-medium text-gray-900">Customer Management</h3>
                        </div>
                        <p class="text-gray-600 mb-4">View and edit customer information, search customers, and manage customer data.</p>
                        <div class="space-y-2">
                            <a href="{{ route('settings.customers.index') }}" 
                               class="block w-full bg-blue-500 hover:bg-blue-600 text-white text-center py-2 px-4 rounded transition-colors duration-200">
                                Manage Customers
                            </a>
                            <p class="text-sm text-gray-500 text-center">{{ $stats['customers'] }} customers in database</p>
                        </div>
                    </div>
                </div>

                <!-- Menu Management -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="h-8 w-8 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 8a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 12a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 16a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
                            </svg>
                            <h3 class="ml-3 text-lg font-medium text-gray-900">Menu Management</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Manage menu categories and items, update prices, and organize your restaurant menu.</p>
                        <div class="space-y-2">
                            <a href="{{ route('menu-categories.index') }}" 
                               class="block w-full bg-green-500 hover:bg-green-600 text-white text-center py-2 px-4 rounded transition-colors duration-200">
                                Manage Menu
                            </a>
                            <p class="text-sm text-gray-500 text-center">{{ $stats['menu_categories'] }} categories, {{ $stats['menu_items'] }} items</p>
                        </div>
                    </div>
                </div>

                <!-- Table Management -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="h-8 w-8 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 3h4v4H3V3zm6 0h4v4H9V3zm6 0h2v4h-2V3zM3 9h4v4H3V9zm6 0h4v4H9V9zm6 0h2v4h-2V9zM3 15h4v2H3v-2zm6 0h4v2H9v-2zm6 0h2v2h-2v-2z"/>
                            </svg>
                            <h3 class="ml-3 text-lg font-medium text-gray-900">Table Management</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Configure restaurant tables, seating arrangements, and table layouts for your POS system.</p>
                        <div class="space-y-2">
                            <a href="{{ route('tables.index') }}" 
                               class="block w-full bg-purple-500 hover:bg-purple-600 text-white text-center py-2 px-4 rounded transition-colors duration-200">
                                Manage Tables
                            </a>
                            <p class="text-sm text-gray-500 text-center">{{ $stats['tables'] }} tables configured</p>
                        </div>
                    </div>
                </div>

                <!-- Order Settings -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="h-8 w-8 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                            </svg>
                            <h3 class="ml-3 text-lg font-medium text-gray-900">Order Settings</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Configure tax rates, service charges, currency settings, and other order-related preferences.</p>
                        <div class="space-y-2">
                            <a href="{{ route('settings.order-settings') }}"
                               class="block w-full bg-purple-500 hover:bg-purple-600 text-white text-center py-2 px-4 rounded transition-colors duration-200">
                                Configure Order Settings
                            </a>
                            <p class="text-sm text-gray-500 text-center">Tax rates, service charges, and more</p>
                        </div>
                    </div>
                </div>

                <!-- Order Types Management -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="h-8 w-8 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <h3 class="ml-3 text-lg font-medium text-gray-900">Order Types</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Manage order types like Dine In, Take Away, and Delivery. Configure requirements and settings for each type.</p>
                        <div class="space-y-2">
                            <a href="{{ route('settings.order-types.index') }}"
                               class="block w-full bg-indigo-500 hover:bg-indigo-600 text-white text-center py-2 px-4 rounded transition-colors duration-200">
                                Manage Order Types
                            </a>
                            <p class="text-sm text-gray-500 text-center">Dine In, Take Away, Delivery, and more</p>
                        </div>
                    </div>
                </div>

                <!-- Payment Methods Management -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="h-8 w-8 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                            </svg>
                            <h3 class="ml-3 text-lg font-medium text-gray-900">Payment Methods</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Manage payment methods like Cash, Credit Card, and Digital Wallets. Configure icons, colors, and reference requirements.</p>
                        <div class="space-y-2">
                            <a href="{{ route('settings.payment-methods.index') }}"
                               class="block w-full bg-green-500 hover:bg-green-600 text-white text-center py-2 px-4 rounded transition-colors duration-200">
                                Manage Payment Methods
                            </a>
                            <p class="text-sm text-gray-500 text-center">Cash, Cards, Digital Payments, and more</p>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>
</x-app-layout>
