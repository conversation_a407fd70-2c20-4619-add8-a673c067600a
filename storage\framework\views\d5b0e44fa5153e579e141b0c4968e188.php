<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Products')); ?>

            </h2>
            <div class="flex space-x-2">
                <?php if($syncStatus['sync_available']): ?>
                    <form action="<?php echo e(route('menu-items.sync')); ?>" method="POST" class="inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                            Sync from API
                        </button>
                    </form>
                <?php endif; ?>

                <!-- Manual Bidirectional Sync Button -->
                <form method="POST" action="<?php echo e(route('menu-items.manual-sync')); ?>" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <button type="submit"
                            id="manual-sync-btn"
                            class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded flex items-center space-x-2"
                            onclick="return confirmManualSync()">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span id="sync-btn-text">Manual Sync</span>
                        <span id="sync-btn-loading" class="hidden">
                            <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </span>
                    </button>
                </form>

                <a href="<?php echo e(route('menu-items.create')); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Add New Product
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Sync Status Notification -->
            <?php if(session('sync_status')): ?>
                <div class="mb-6">
                    <?php if(session('sync_status') === 'success'): ?>
                        <div class="bg-green-50 border border-green-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-green-800">
                                        Produk Berhasil Disinkronkan!
                                    </h3>
                                    <div class="mt-2 text-sm text-green-700">
                                        <p><strong><?php echo e(session('sync_details.product_name')); ?></strong> telah berhasil dibuat dan disinkronkan ke API.</p>
                                        <?php if(session('sync_details.api_id')): ?>
                                            <p class="mt-1">API ID: <code class="bg-green-100 px-1 rounded"><?php echo e(session('sync_details.api_id')); ?></code></p>
                                        <?php endif; ?>
                                        <p class="mt-1 text-xs">Waktu sync: <?php echo e(session('sync_details.sync_time')); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php elseif(session('sync_status') === 'failed'): ?>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800">
                                        Produk Dibuat Lokal, Sync API Gagal
                                    </h3>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p><strong><?php echo e(session('sync_details.product_name')); ?></strong> berhasil dibuat secara lokal tetapi gagal disinkronkan ke API.</p>
                                        <p class="mt-1">Error: <?php echo e(session('sync_details.error')); ?></p>
                                        <p class="mt-1 text-xs">Waktu: <?php echo e(session('sync_details.sync_time')); ?></p>
                                        <p class="mt-2 text-xs font-medium">💡 Produk akan disinkronkan otomatis saat koneksi API pulih.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- Manual Sync Results -->
            <?php if(session('sync_details') && !session('sync_status')): ?>
                <div class="mb-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="ml-3 w-full">
                                <h3 class="text-sm font-medium text-blue-800">
                                    Manual Sync Completed
                                </h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <?php $details = session('sync_details'); ?>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                                        <!-- Local to API Stats -->
                                        <div class="bg-blue-100 p-3 rounded">
                                            <h4 class="font-medium text-blue-900">Local → API</h4>
                                            <div class="mt-1 text-xs">
                                                <p>✅ Sent: <?php echo e($details['local_to_api']['products_sent'] ?? 0); ?></p>
                                                <p>⏭️ Skipped: <?php echo e($details['local_to_api']['products_skipped'] ?? 0); ?></p>
                                                <p>❌ Failed: <?php echo e($details['local_to_api']['products_failed'] ?? 0); ?></p>
                                            </div>
                                        </div>

                                        <!-- API to Local Stats -->
                                        <div class="bg-green-100 p-3 rounded">
                                            <h4 class="font-medium text-green-900">API → Local</h4>
                                            <div class="mt-1 text-xs">
                                                <p>✅ Created: <?php echo e($details['api_to_local']['products_created'] ?? 0); ?></p>
                                                <p>⏭️ Skipped: <?php echo e($details['api_to_local']['products_skipped'] ?? 0); ?></p>
                                                <p>❌ Failed: <?php echo e($details['api_to_local']['products_failed'] ?? 0); ?></p>
                                            </div>
                                        </div>
                                    </div>

                                    <p class="mt-3 text-xs">
                                        Total: <?php echo e($details['total_local_products'] ?? 0); ?> local products,
                                        <?php echo e($details['total_api_products'] ?? 0); ?> API products
                                    </p>
                                    <p class="text-xs">Sync time: <?php echo e($details['sync_time'] ?? 'Unknown'); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Toast Notification -->
            <?php if(session('sync_status')): ?>
                <div id="toast-notification" class="fixed top-4 right-4 z-50 max-w-sm">
                    <?php if(session('sync_status') === 'success'): ?>
                        <div class="bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                <div>
                                    <p class="font-semibold">Sync Berhasil!</p>
                                    <p class="text-sm"><?php echo e(session('sync_details.product_name')); ?> disinkronkan ke API</p>
                                </div>
                            </div>
                        </div>
                    <?php elseif(session('sync_status') === 'failed'): ?>
                        <div class="bg-yellow-500 text-white px-6 py-4 rounded-lg shadow-lg">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                </svg>
                                <div>
                                    <p class="font-semibold">Sync Gagal</p>
                                    <p class="text-sm"><?php echo e(session('sync_details.product_name')); ?> dibuat lokal saja</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <script>
                    // Auto-hide toast after 5 seconds
                    setTimeout(function() {
                        const toast = document.getElementById('toast-notification');
                        if (toast) {
                            toast.style.transition = 'opacity 0.5s ease-out';
                            toast.style.opacity = '0';
                            setTimeout(() => toast.remove(), 500);
                        }
                    }, 5000);
                </script>
            <?php endif; ?>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <?php if(session('success')): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            <?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>

                    <!-- API Status Information -->
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                        <h3 class="text-sm font-medium text-gray-900 mb-2">Data Source Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="font-medium">API Status:</span>
                                <span class="ml-1 px-2 py-1 rounded text-xs <?php echo e($syncStatus['api_connected'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                    <?php echo e($syncStatus['api_connected'] ? 'Connected' : 'Disconnected'); ?>

                                </span>
                            </div>
                            <div>
                                <span class="font-medium">Last Sync:</span>
                                <span class="ml-1 text-gray-600"><?php echo e($syncStatus['last_sync']); ?></span>
                            </div>
                            <div>
                                <span class="font-medium">Local Items:</span>
                                <span class="ml-1 text-gray-600"><?php echo e($syncStatus['local_stats']['total_items']); ?></span>
                            </div>
                            <div>
                                <span class="font-medium">API Synced:</span>
                                <span class="ml-1 text-gray-600"><?php echo e($syncStatus['local_stats']['items_with_api_id']); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Search Section -->
                    <div class="mb-6">
                        <div class="flex items-center space-x-4">
                            <div class="flex-1 relative">
                                <input type="text"
                                       id="productSearchInput"
                                       placeholder="Search products by name, SKU, barcode, or category..."
                                       class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                            </div>

                            <!-- Category Filter -->
                            <select id="categoryFilter" class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">All Categories</option>
                                <?php $__currentLoopData = $menuItems->pluck('category')->unique('id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category->id); ?>"><?php echo e($category->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>

                            <!-- Clear Search -->
                            <button id="clearProductSearchBtn"
                                    onclick="clearProductSearch()"
                                    class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200 hidden">
                                Clear
                            </button>
                        </div>

                        <!-- Search Results Info -->
                        <div id="productSearchResultsInfo" class="hidden mt-2 text-sm text-gray-600">
                            <span id="productSearchResultsText"></span>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full table-auto" id="productsTable">
                            <thead>
                                <tr class="bg-gray-50">
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="productsTableBody">
                                <?php $__empty_1 = true; $__currentLoopData = $menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr class="product-row"
                                        data-name="<?php echo e(strtolower($item->name)); ?>"
                                        data-sku="<?php echo e(strtolower($item->sku ?? '')); ?>"
                                        data-barcode="<?php echo e(strtolower($item->barcode ?? '')); ?>"
                                        data-category="<?php echo e(strtolower($item->category->name ?? '')); ?>"
                                        data-category-id="<?php echo e($item->category->id ?? ''); ?>">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if($item->image_path): ?>
                                                <img src="<?php echo e(asset('storage/' . $item->image_path)); ?>" alt="<?php echo e($item->name); ?>" class="h-12 w-12 object-cover rounded">
                                            <?php else: ?>
                                                <div class="h-12 w-12 bg-gray-200 rounded flex items-center justify-center">
                                                    <span class="text-gray-400 text-xs">No Image</span>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <?php echo e($item->name); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo e($item->category->name); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            $<?php echo e(number_format($item->price, 2)); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <?php if($item->isSyncedToApi()): ?>
                                                <div class="flex items-center">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        Synced
                                                    </span>
                                                    <?php if($item->last_api_sync_at): ?>
                                                        <span class="ml-2 text-xs text-gray-400">
                                                            <?php echo e($item->last_api_sync_at->format('M j, H:i')); ?>

                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            <?php else: ?>
                                                <div class="flex items-center space-x-2">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        Not Synced
                                                    </span>
                                                    <button
                                                        onclick="uploadToApi(<?php echo e($item->id); ?>, '<?php echo e($item->name); ?>')"
                                                        class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                                        id="upload-btn-<?php echo e($item->id); ?>"
                                                    >
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                                        </svg>
                                                        Upload
                                                    </button>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="<?php echo e(route('menu-items.show', $item)); ?>" class="text-indigo-600 hover:text-indigo-900 mr-3">View</a>
                                            <a href="<?php echo e(route('menu-items.edit', $item)); ?>" class="text-yellow-600 hover:text-yellow-900 mr-3">Edit</a>
                                            <form action="<?php echo e(route('menu-items.destroy', $item)); ?>" method="POST" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Are you sure you want to delete this item?')">Delete</button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            No menu items found. <a href="<?php echo e(route('menu-items.create')); ?>" class="text-blue-600 hover:text-blue-900">Create one now</a>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function confirmManualSync() {
            const confirmed = confirm(
                'Manual Sync akan melakukan sinkronisasi dua arah:\n\n' +
                '• Produk lokal dengan SKU baru akan dikirim ke API\n' +
                '• Produk API dengan SKU baru akan dibuat secara lokal\n' +
                '• Produk yang sudah ada akan dilewati\n\n' +
                'Proses ini mungkin memakan waktu beberapa menit. Lanjutkan?'
            );

            if (confirmed) {
                // Show loading state
                const btn = document.getElementById('manual-sync-btn');
                const btnText = document.getElementById('sync-btn-text');
                const btnLoading = document.getElementById('sync-btn-loading');

                if (btn && btnText && btnLoading) {
                    btn.disabled = true;
                    btn.classList.add('opacity-75', 'cursor-not-allowed');
                    btnText.classList.add('hidden');
                    btnLoading.classList.remove('hidden');
                }
            }

            return confirmed;
        }

        // Auto-hide detailed sync notifications after 10 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const syncDetails = <?php echo json_encode(session('sync_details', null), 512) ?>;

            if (syncDetails) {
                console.log('Manual Sync Results:', syncDetails);

                // Show detailed results in console for debugging
                if (syncDetails.local_to_api) {
                    console.log('Local → API:', syncDetails.local_to_api);
                }
                if (syncDetails.api_to_local) {
                    console.log('API → Local:', syncDetails.api_to_local);
                }
            }
        });

        // Upload product to API function
        async function uploadToApi(itemId, itemName) {
            const btn = document.getElementById(`upload-btn-${itemId}`);
            const originalContent = btn.innerHTML;

            // Show loading state
            btn.disabled = true;
            btn.innerHTML = `
                <svg class="animate-spin w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Uploading...
            `;

            try {
                const response = await fetch(`/menu-items/${itemId}/upload-to-api`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const result = await response.json();

                if (result.success) {
                    // Show success message
                    alert(`✅ Product "${itemName}" uploaded to API successfully!`);

                    // Reload the page to show updated status
                    window.location.reload();
                } else {
                    // Show error message
                    alert(`❌ Failed to upload "${itemName}" to API: ${result.message}`);

                    // Restore button
                    btn.disabled = false;
                    btn.innerHTML = originalContent;
                }
            } catch (error) {
                console.error('Upload error:', error);
                alert(`❌ An error occurred while uploading "${itemName}" to API. Please try again.`);

                // Restore button
                btn.disabled = false;
                btn.innerHTML = originalContent;
            }
        }

        // Product Search Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('productSearchInput');
            const categoryFilter = document.getElementById('categoryFilter');
            const clearBtn = document.getElementById('clearProductSearchBtn');
            const searchResultsInfo = document.getElementById('productSearchResultsInfo');
            const searchResultsText = document.getElementById('productSearchResultsText');

            // Setup search input listener
            if (searchInput) {
                searchInput.addEventListener('input', handleProductSearch);
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        handleProductSearch();
                    }
                });
            }

            // Setup category filter listener
            if (categoryFilter) {
                categoryFilter.addEventListener('change', handleProductSearch);
            }
        });

        // Handle product search
        function handleProductSearch() {
            const searchInput = document.getElementById('productSearchInput');
            const categoryFilter = document.getElementById('categoryFilter');
            const clearBtn = document.getElementById('clearProductSearchBtn');
            const searchResultsInfo = document.getElementById('productSearchResultsInfo');
            const searchResultsText = document.getElementById('productSearchResultsText');

            const searchTerm = searchInput.value.trim().toLowerCase();
            const selectedCategory = categoryFilter.value;
            const productRows = document.querySelectorAll('.product-row');

            let visibleCount = 0;
            let totalCount = productRows.length;

            // Show/hide clear button
            if (searchTerm !== '' || selectedCategory !== '') {
                clearBtn.classList.remove('hidden');
            } else {
                clearBtn.classList.add('hidden');
            }

            // Filter products
            productRows.forEach(row => {
                const name = row.dataset.name || '';
                const sku = row.dataset.sku || '';
                const barcode = row.dataset.barcode || '';
                const category = row.dataset.category || '';
                const categoryId = row.dataset.categoryId || '';

                let matchesSearch = true;
                let matchesCategory = true;

                // Check search term match
                if (searchTerm !== '') {
                    matchesSearch = name.includes(searchTerm) ||
                                   sku.includes(searchTerm) ||
                                   barcode.includes(searchTerm) ||
                                   category.includes(searchTerm);
                }

                // Check category filter match
                if (selectedCategory !== '') {
                    matchesCategory = categoryId === selectedCategory;
                }

                // Show/hide row based on matches
                if (matchesSearch && matchesCategory) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            // Update search results info
            if (searchTerm !== '' || selectedCategory !== '') {
                let filterText = '';
                if (searchTerm !== '' && selectedCategory !== '') {
                    const categoryName = categoryFilter.options[categoryFilter.selectedIndex].text;
                    filterText = `"${searchInput.value}" in category "${categoryName}"`;
                } else if (searchTerm !== '') {
                    filterText = `"${searchInput.value}"`;
                } else if (selectedCategory !== '') {
                    const categoryName = categoryFilter.options[categoryFilter.selectedIndex].text;
                    filterText = `category "${categoryName}"`;
                }

                searchResultsText.textContent = `Showing ${visibleCount} of ${totalCount} products matching ${filterText}`;
                searchResultsInfo.classList.remove('hidden');
            } else {
                searchResultsInfo.classList.add('hidden');
            }

            // Show "no results" message if needed
            showNoResultsMessage(visibleCount);
        }

        // Show no results message
        function showNoResultsMessage(visibleCount) {
            const tableBody = document.getElementById('productsTableBody');
            let noResultsRow = document.getElementById('noResultsRow');

            if (visibleCount === 0) {
                if (!noResultsRow) {
                    noResultsRow = document.createElement('tr');
                    noResultsRow.id = 'noResultsRow';
                    noResultsRow.innerHTML = `
                        <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                            <div class="flex flex-col items-center">
                                <svg class="w-12 h-12 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <p class="text-lg font-medium">No products found</p>
                                <p class="text-sm">Try adjusting your search criteria</p>
                            </div>
                        </td>
                    `;
                    tableBody.appendChild(noResultsRow);
                }
                noResultsRow.style.display = '';
            } else {
                if (noResultsRow) {
                    noResultsRow.style.display = 'none';
                }
            }
        }

        // Clear product search
        function clearProductSearch() {
            const searchInput = document.getElementById('productSearchInput');
            const categoryFilter = document.getElementById('categoryFilter');

            searchInput.value = '';
            categoryFilter.value = '';
            handleProductSearch();
            searchInput.focus();
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\POS\resources\views/menu-items/index.blade.php ENDPATH**/ ?>