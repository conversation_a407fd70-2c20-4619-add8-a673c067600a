<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Table Layout')); ?>

            </h2>
            <?php if(session('api_authenticated')): ?>
                <a href="<?php echo e(route('tables.create')); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Add New Table
                </a>
            <?php endif; ?>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <?php if(session('success')): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            <?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>

                    <!-- Table Grid Layout -->
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mb-8">
                        <?php $__empty_1 = true; $__currentLoopData = $tables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="relative">
                                <div class="border-2 rounded-lg p-4 text-center cursor-pointer transition-all duration-200 hover:shadow-lg
                                    <?php echo e($table->isAvailable() ? 'border-green-500 bg-green-50 hover:bg-green-100' : 'border-red-500 bg-red-50 hover:bg-red-100'); ?>"
                                    onclick="window.location.href='<?php echo e(route('tables.show', $table)); ?>'">
                                    
                                    <!-- Table Icon -->
                                    <div class="mb-2">
                                        <svg class="w-8 h-8 mx-auto <?php echo e($table->isAvailable() ? 'text-green-600' : 'text-red-600'); ?>" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                        </svg>
                                    </div>
                                    
                                    <!-- Table Name -->
                                    <h3 class="font-semibold text-lg <?php echo e($table->isAvailable() ? 'text-green-800' : 'text-red-800'); ?>">
                                        <?php echo e($table->name); ?>

                                    </h3>
                                    
                                    <!-- Status -->
                                    <p class="text-sm <?php echo e($table->isAvailable() ? 'text-green-600' : 'text-red-600'); ?>">
                                        <?php echo e($table->status); ?>

                                    </p>
                                    
                                    <!-- Current Order Info -->
                                    <?php if($table->currentOrder): ?>
                                        <p class="text-xs text-gray-600 mt-1">
                                            Order #<?php echo e($table->currentOrder->id); ?>

                                        </p>
                                        <p class="text-xs text-gray-600">
                                            $<?php echo e(number_format($table->currentOrder->total_amount, 2)); ?>

                                        </p>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Admin Actions -->
                                <?php if(session('api_authenticated')): ?>
                                    <div class="absolute top-2 right-2">
                                        <div class="flex space-x-1">
                                            <a href="<?php echo e(route('tables.edit', $table)); ?>" class="bg-yellow-500 hover:bg-yellow-600 text-white text-xs px-2 py-1 rounded">
                                                Edit
                                            </a>
                                            <form action="<?php echo e(route('tables.destroy', $table)); ?>" method="POST" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="bg-red-500 hover:bg-red-600 text-white text-xs px-2 py-1 rounded" onclick="return confirm('Are you sure?')">
                                                    Del
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="col-span-full text-center py-8">
                                <p class="text-gray-500 mb-4">No tables found.</p>
                                <?php if(session('api_authenticated')): ?>
                                    <a href="<?php echo e(route('tables.create')); ?>" class="text-blue-600 hover:text-blue-900">Create your first table</a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Legend -->
                    <div class="flex justify-center space-x-6 text-sm">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-500 rounded mr-2"></div>
                            <span>Available</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-red-500 rounded mr-2"></div>
                            <span>Occupied</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\POS\resources\views/tables/index.blade.php ENDPATH**/ ?>