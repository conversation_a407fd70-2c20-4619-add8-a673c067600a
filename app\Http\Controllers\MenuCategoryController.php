<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\MenuCategory;
use App\Services\ProductService;

class MenuCategoryController extends Controller
{
    private ProductService $productService;

    public function __construct(ProductService $productService)
    {
        $this->middleware(['auth', 'menu.access']);
        $this->productService = $productService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $categories = MenuCategory::withCount('menuItems')->get();
        $syncStatus = $this->productService->getSyncStatus();

        return view('menu-categories.index', compact('categories', 'syncStatus'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('menu-categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:menu_categories',
        ]);

        MenuCategory::create($request->only('name'));

        return redirect()->route('menu-categories.index')
                        ->with('success', 'Menu category created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(MenuCategory $menuCategory)
    {
        $menuCategory->load('menuItems');
        return view('menu-categories.show', compact('menuCategory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MenuCategory $menuCategory)
    {
        return view('menu-categories.edit', compact('menuCategory'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MenuCategory $menuCategory)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:menu_categories,name,' . $menuCategory->id,
        ]);

        $menuCategory->update($request->only('name'));

        return redirect()->route('menu-categories.index')
                        ->with('success', 'Menu category updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MenuCategory $menuCategory)
    {
        if ($menuCategory->menuItems()->count() > 0) {
            return redirect()->route('menu-categories.index')
                            ->with('error', 'Cannot delete category with existing menu items.');
        }

        $menuCategory->delete();

        return redirect()->route('menu-categories.index')
                        ->with('success', 'Menu category deleted successfully.');
    }

    /**
     * Sync categories and products from API
     */
    public function sync()
    {
        $result = $this->productService->forceSyncFromApi();

        if ($result['success']) {
            $message = 'Products synchronized successfully! ';
            $message .= "Created {$result['stats']['categories_created']} categories, ";
            $message .= "updated {$result['stats']['categories_updated']} categories, ";
            $message .= "created {$result['stats']['items_created']} items, ";
            $message .= "updated {$result['stats']['items_updated']} items.";

            return redirect()->route('menu-categories.index')
                            ->with('success', $message);
        } else {
            return redirect()->route('menu-categories.index')
                            ->with('error', 'Sync failed: ' . $result['error']);
        }
    }

    /**
     * Get sync status as JSON
     */
    public function syncStatus()
    {
        return response()->json($this->productService->getSyncStatus());
    }
}
