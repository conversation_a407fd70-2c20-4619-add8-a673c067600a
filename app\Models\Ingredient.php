<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Ingredient extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'unit',
        'stock_level',
    ];

    protected function casts(): array
    {
        return [
            'stock_level' => 'decimal:2',
        ];
    }

    /**
     * Get the menu items that use this ingredient (recipes).
     */
    public function menuItems()
    {
        return $this->belongsToMany(MenuItem::class, 'recipes')
                    ->withPivot('quantity_needed')
                    ->withTimestamps();
    }

    /**
     * Get the purchase items for this ingredient.
     */
    public function purchaseItems()
    {
        return $this->hasMany(PurchaseItem::class);
    }

    /**
     * Check if ingredient is low in stock.
     */
    public function isLowStock($threshold = 10): bool
    {
        return $this->stock_level <= $threshold;
    }
}
