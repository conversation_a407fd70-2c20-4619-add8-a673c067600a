@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Payment Method Details: {{ $paymentMethod->name }}</h3>
                    <div class="btn-group">
                        <a href="{{ route('settings.payment-methods.edit', $paymentMethod) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('settings.payment-methods.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Payment Methods
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="200">Name:</th>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($paymentMethod->icon)
                                                <i class="{{ $paymentMethod->icon }} me-2" 
                                                   @if($paymentMethod->color) style="color: {{ $paymentMethod->color }}" @endif></i>
                                            @endif
                                            <strong>{{ $paymentMethod->name }}</strong>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Slug:</th>
                                    <td><code>{{ $paymentMethod->slug }}</code></td>
                                </tr>
                                <tr>
                                    <th>Description:</th>
                                    <td>{{ $paymentMethod->description ?: 'No description provided' }}</td>
                                </tr>
                                <tr>
                                    <th>Sort Order:</th>
                                    <td>{{ $paymentMethod->sort_order }}</td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        @if($paymentMethod->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="200">Requires Reference:</th>
                                    <td>
                                        @if($paymentMethod->requires_reference)
                                            <span class="badge bg-success"><i class="fas fa-check"></i> Yes</span>
                                        @else
                                            <span class="badge bg-secondary"><i class="fas fa-times"></i> No</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Icon:</th>
                                    <td>
                                        @if($paymentMethod->icon)
                                            <i class="{{ $paymentMethod->icon }}" 
                                               @if($paymentMethod->color) style="color: {{ $paymentMethod->color }}" @endif></i>
                                            <code class="ms-2">{{ $paymentMethod->icon }}</code>
                                        @else
                                            <span class="text-muted">No icon set</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Color:</th>
                                    <td>
                                        @if($paymentMethod->color)
                                            <span class="d-inline-block rounded" 
                                                  style="width: 20px; height: 20px; background-color: {{ $paymentMethod->color }}; border: 1px solid #ddd;"></span>
                                            <code class="ms-2">{{ $paymentMethod->color }}</code>
                                        @else
                                            <span class="text-muted">No color set</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Created:</th>
                                    <td>{{ $paymentMethod->created_at->format('M d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <th>Last Updated:</th>
                                    <td>{{ $paymentMethod->updated_at->format('M d, Y H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($paymentMethod->payments()->exists())
                        <div class="mt-4">
                            <h5>Usage Statistics</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ $paymentMethod->payments()->count() }}</h4>
                                            <p class="mb-0">Total Payments</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ $paymentMethod->payments()->where('status', 'completed')->count() }}</h4>
                                            <p class="mb-0">Completed Payments</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ $paymentMethod->payments()->where('status', 'pending')->count() }}</h4>
                                            <p class="mb-0">Pending Payments</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h4>${{ number_format($paymentMethod->payments()->where('status', 'completed')->sum('amount'), 2) }}</h4>
                                            <p class="mb-0">Total Amount</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <h6>Recent Payments</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Order</th>
                                                <th>Amount</th>
                                                <th>Reference</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($paymentMethod->payments()->with('order')->latest()->limit(10)->get() as $payment)
                                                <tr>
                                                    <td>{{ $payment->created_at->setTimezone('Asia/Jakarta')->format('M d, Y H:i') }}</td>
                                                    <td>
                                                        @if($payment->order)
                                                            <a href="{{ route('pos.order-details', $payment->order) }}">
                                                                Order #{{ $payment->order->id }}
                                                            </a>
                                                        @else
                                                            -
                                                        @endif
                                                    </td>
                                                    <td>${{ number_format($payment->amount, 2) }}</td>
                                                    <td>{{ $payment->reference_number ?: '-' }}</td>
                                                    <td>
                                                        <span class="badge bg-{{ $payment->status === 'completed' ? 'success' : ($payment->status === 'pending' ? 'warning' : 'danger') }}">
                                                            {{ ucfirst($payment->status) }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                This payment method has not been used in any payments yet.
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection