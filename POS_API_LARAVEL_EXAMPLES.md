# Laravel POS API Integration Examples

This document provides comprehensive Laravel examples for making HTTP GET requests to the POS sync products API endpoint.

## 📋 Overview

**API Endpoint:** `https://viera-filament.test/api/pos/sync/products`  
**Authentication:** Bearer Token `5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35`  
**Method:** GET  
**Response Format:** JSON  

## 🚀 Quick Start

### 1. Register Routes

Add the routes to your `routes/web.php` or create a separate route file:

```php
// Include the POS API routes
require_once __DIR__ . '/pos-api.php';
```

### 2. Register the Service

Add to your `AppServiceProvider` or create a dedicated service provider:

```php
// In AppServiceProvider.php
public function register()
{
    $this->app->singleton(PosApiService::class);
}
```

### 3. Register the Artisan Command

Add to your `app/Console/Kernel.php`:

```php
protected $commands = [
    Commands\TestPosApiCommand::class,
];
```

## 📁 Files Created

### Core Files:
- `app/Services/PosApiService.php` - Main service for API communication
- `app/Http/Controllers/PosApiController.php` - Controller with endpoints
- `app/Console/Commands/TestPosApiCommand.php` - Artisan command for testing
- `routes/pos-api.php` - API routes
- `resources/views/pos-api-test.blade.php` - Web interface for testing

## 🔧 Usage Examples

### 1. Using the Service Directly

```php
use App\Services\PosApiService;

// In a controller or anywhere in your app
$posApiService = app(PosApiService::class);

// Basic fetch
$result = $posApiService->fetchProducts();
if ($result['success']) {
    $products = $result['data']['data'];
    // Process products...
}

// Formatted fetch
$result = $posApiService->getFormattedProducts();
if ($result['success']) {
    foreach ($result['products'] as $product) {
        echo "Product: {$product['name']} - Price: \${$product['price']}\n";
    }
}

// Test connection
$connectionTest = $posApiService->testConnection();
if ($connectionTest['success']) {
    echo "API is reachable!";
}
```

### 2. Using HTTP Endpoints

```bash
# Test connection
curl http://your-app.test/pos-api/test-connection

# Fetch products
curl http://your-app.test/pos-api/products

# Fetch cached products
curl http://your-app.test/pos-api/products/cached

# Get formatted products
curl http://your-app.test/pos-api/products/formatted

# Get API status
curl http://your-app.test/pos-api/status
```

### 3. Using Artisan Command

```bash
# Basic test
php artisan pos:test-api

# Test connection only
php artisan pos:test-api --connection-only

# Different output formats
php artisan pos:test-api --format=table
php artisan pos:test-api --format=json
php artisan pos:test-api --format=summary

# Use cached results
php artisan pos:test-api --cache
```

### 4. Using Web Interface

Visit: `http://your-app.test/pos-api-test`

The web interface provides:
- ✅ Connection testing
- 📦 Product fetching (normal, cached, formatted)
- 📋 Products display in table format
- 🔍 Raw response viewer

## 🛠️ Advanced Usage

### Custom Configuration

Create a config file `config/pos-api.php`:

```php
<?php

return [
    'base_url' => env('POS_API_BASE_URL', 'https://viera-filament.test/api/pos'),
    'token' => env('POS_API_TOKEN', '5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35'),
    'timeout' => env('POS_API_TIMEOUT', 30),
    'retry_attempts' => env('POS_API_RETRY_ATTEMPTS', 3),
    'retry_delay' => env('POS_API_RETRY_DELAY', 1000),
    'cache_duration' => env('POS_API_CACHE_DURATION', 300), // 5 minutes
];
```

Then update your `.env`:

```env
POS_API_BASE_URL=https://viera-filament.test/api/pos
POS_API_TOKEN=5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35
POS_API_TIMEOUT=30
POS_API_CACHE_DURATION=300
```

### Scheduled Sync

Add to your `app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule)
{
    // Sync products every hour
    $schedule->command('pos:test-api --format=summary')
             ->hourly()
             ->appendOutputTo(storage_path('logs/pos-sync.log'));
}
```

### Event-Driven Sync

Create an event and listener:

```php
// Event
class ProductsSyncRequested
{
    // Event properties...
}

// Listener
class SyncProductsFromPosApi
{
    public function handle(ProductsSyncRequested $event)
    {
        $posApiService = app(PosApiService::class);
        $result = $posApiService->fetchProducts();
        
        if ($result['success']) {
            // Process and save products...
        }
    }
}
```

## 🔍 Error Handling

The service includes comprehensive error handling:

- **Connection Errors**: Network issues, timeouts
- **HTTP Errors**: 4xx, 5xx status codes
- **JSON Parsing Errors**: Invalid response format
- **Authentication Errors**: Invalid or expired tokens

Example error response:
```json
{
    "success": false,
    "error": "HTTP Error: 401",
    "status_code": 401,
    "response_body": "{\"message\":\"Unauthenticated.\"}"
}
```

## 📊 Response Format

Successful response structure:
```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 1,
                "name": "Product Name",
                "price": 25.99,
                "category": {
                    "id": 1,
                    "name": "Category Name"
                },
                "is_active": true,
                "description": "Product description",
                "created_at": "2025-07-25T10:00:00.000000Z",
                "updated_at": "2025-07-25T10:00:00.000000Z"
            }
        ]
    },
    "status_code": 200,
    "message": "Products fetched successfully"
}
```

## 🧪 Testing

### Unit Tests

Create tests in `tests/Feature/PosApiTest.php`:

```php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\PosApiService;
use Illuminate\Support\Facades\Http;

class PosApiTest extends TestCase
{
    public function test_can_fetch_products()
    {
        Http::fake([
            'viera-filament.test/api/pos/sync/products' => Http::response([
                'data' => [
                    ['id' => 1, 'name' => 'Test Product', 'price' => 10.99]
                ]
            ], 200)
        ]);

        $service = new PosApiService();
        $result = $service->fetchProducts();

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('data', $result);
    }
}
```

## 🚨 Security Notes

- Store API tokens in environment variables, never in code
- Use HTTPS for all API communications
- Implement rate limiting for API endpoints
- Log API requests for monitoring and debugging
- Validate and sanitize all API responses before processing

## 📝 Logging

The service automatically logs:
- Successful API calls with response counts
- HTTP errors with status codes and response bodies
- Network exceptions with error messages
- General exceptions with stack traces

Check logs in `storage/logs/laravel.log` or create dedicated log channels.

## 🔄 Next Steps

1. **Database Integration**: Modify the sync methods to save products to your database
2. **Queue Processing**: Use Laravel queues for large product syncs
3. **Webhook Support**: Add webhook endpoints for real-time updates
4. **API Versioning**: Handle different API versions
5. **Monitoring**: Add health checks and monitoring dashboards
