<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('menu_items', function (Blueprint $table) {
            $table->boolean('is_synced_to_api')->default(false)->after('api_id')->comment('Whether product is synced to external API');
            $table->timestamp('last_api_sync_at')->nullable()->after('is_synced_to_api')->comment('Last time product was synced to API');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('menu_items', function (Blueprint $table) {
            $table->dropColumn(['is_synced_to_api', 'last_api_sync_at']);
        });
    }
};
