<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Product') }}
            </h2>
            <a href="{{ route('menu-items.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Products
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-8 text-gray-900">

                    <!-- Product Info Header -->
                    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Editing Product: {{ $menuItem->name }}</h3>
                        <p class="text-sm text-gray-600">Update the product details below. All fields marked with * are required.</p>
                    </div>

                    <!-- Edit Form -->
                    <form method="POST" action="{{ route('menu-items.update', $menuItem) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <!-- Product Information Section -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 mb-6">Product Information</h3>

                            <!-- Product Name -->
                            <div class="mb-6">
                                <x-input-label for="name" :value="__('Product Name')" class="text-sm font-medium text-gray-700" />
                                <x-text-input id="name"
                                    class="block mt-2 w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                    type="text"
                                    name="name"
                                    :value="old('name', $menuItem->name)"
                                    required
                                    autofocus
                                    placeholder="Enter product name" />
                                <x-input-error :messages="$errors->get('name')" class="mt-2" />
                            </div>

                            <!-- SKU and Barcode Row -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <x-input-label for="sku" :value="__('SKU')" class="text-sm font-medium text-gray-700" />
                                    <x-text-input id="sku"
                                        class="block mt-2 w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                        type="text"
                                        name="sku"
                                        :value="old('sku', $menuItem->sku)"
                                        placeholder="Enter SKU" />
                                    <x-input-error :messages="$errors->get('sku')" class="mt-2" />
                                </div>

                                <div>
                                    <x-input-label for="barcode" :value="__('Barcode')" class="text-sm font-medium text-gray-700" />
                                    <x-text-input id="barcode"
                                        class="block mt-2 w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                        type="text"
                                        name="barcode"
                                        :value="old('barcode', $menuItem->barcode)"
                                        placeholder="Enter barcode" />
                                    <x-input-error :messages="$errors->get('barcode')" class="mt-2" />
                                </div>
                            </div>

                            <!-- Category -->
                            <div class="mb-6">
                                <x-input-label for="category_id" :value="__('Category')" class="text-sm font-medium text-gray-700" />
                                <select id="category_id"
                                    name="category_id"
                                    class="block mt-2 w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                    required>
                                    <option value="">Pilih produk atau layanan</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('category_id', $menuItem->category_id) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <x-input-error :messages="$errors->get('category_id')" class="mt-2" />
                            </div>

                            <!-- Description -->
                            <div class="mb-6">
                                <x-input-label for="description" :value="__('Description')" class="text-sm font-medium text-gray-700" />
                                <textarea id="description"
                                    name="description"
                                    rows="4"
                                    class="block mt-2 w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm resize-none"
                                    placeholder="Enter product description">{{ old('description', $menuItem->description) }}</textarea>
                                <x-input-error :messages="$errors->get('description')" class="mt-2" />
                            </div>
                        </div>

                        <!-- Pricing & Inventory Section -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 mb-6">Pricing & Inventory</h3>

                            <!-- Selling Price and Cost Price Row -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <x-input-label for="price" :value="__('Selling Price')" class="text-sm font-medium text-gray-700" />
                                    <div class="relative mt-2">
                                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">Rp</span>
                                        <x-text-input id="price"
                                            class="block w-full pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                            type="number"
                                            step="0.01"
                                            min="0"
                                            name="price"
                                            :value="old('price', $menuItem->price)"
                                            required
                                            placeholder="0" />
                                    </div>
                                    <x-input-error :messages="$errors->get('price')" class="mt-2" />
                                </div>

                                <div>
                                    <x-input-label for="cost_price" :value="__('Cost Price')" class="text-sm font-medium text-gray-700" />
                                    <div class="relative mt-2">
                                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">Rp</span>
                                        <x-text-input id="cost_price"
                                            class="block w-full pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                            type="number"
                                            step="0.01"
                                            min="0"
                                            name="cost_price"
                                            :value="old('cost_price', $menuItem->cost_price)"
                                            placeholder="0" />
                                    </div>
                                    <x-input-error :messages="$errors->get('cost_price')" class="mt-2" />
                                </div>
                            </div>

                            <!-- Stock Quantity and Active Status Row -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <x-input-label for="stock_quantity" :value="__('Stock Quantity')" class="text-sm font-medium text-gray-700" />
                                    <x-text-input id="stock_quantity"
                                        class="block mt-2 w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                        type="number"
                                        min="0"
                                        name="stock_quantity"
                                        :value="old('stock_quantity', $menuItem->stock_quantity)"
                                        placeholder="0" />
                                    <x-input-error :messages="$errors->get('stock_quantity')" class="mt-2" />
                                </div>

                                <div class="flex items-center">
                                    <div class="flex items-center mt-8">
                                        <label class="inline-flex items-center">
                                            <input type="checkbox"
                                                name="is_active"
                                                value="1"
                                                {{ old('is_active', $menuItem->is_active) ? 'checked' : '' }}
                                                class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                            <span class="ml-3 text-sm font-medium text-gray-700">Active Status</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Food Item Toggle -->
                            <div class="mb-6">
                                <div class="flex items-center">
                                    <input type="checkbox"
                                        id="is_food_item"
                                        name="is_food_item"
                                        value="1"
                                        {{ old('is_food_item', $menuItem->is_food_item) ? 'checked' : '' }}
                                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                    <label for="is_food_item" class="ml-3 text-sm font-medium text-gray-700">
                                        Food Item
                                    </label>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">Check if this is a food or beverage item</p>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                            <a href="{{ route('menu-items.index') }}"
                               class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Cancel
                            </a>
                            <button type="submit"
                                    id="update-product-btn"
                                    class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span id="btn-text">Update Product</span>
                                <span id="btn-loading" class="hidden">
                                    <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Updating...
                                </span>
                            </button>
                        </div>

                    </form>

                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const submitBtn = document.getElementById('update-product-btn');
            const btnText = document.getElementById('btn-text');
            const btnLoading = document.getElementById('btn-loading');

            // Handle form submission
            form.addEventListener('submit', function(e) {
                // Show loading state
                btnText.classList.add('hidden');
                btnLoading.classList.remove('hidden');
                submitBtn.disabled = true;
            });
        });
    </script>
</x-app-layout>
