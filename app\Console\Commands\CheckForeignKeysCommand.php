<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Customer;
use App\Models\TableLayout;

class CheckForeignKeysCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:check-foreign-keys';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check foreign key constraints for order creation';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Checking Foreign Key Constraints');
        $this->newLine();

        // Check Users
        $userCount = User::count();
        $user79 = User::find(79);
        $this->info("👥 Users:");
        $this->info("  Total users: {$userCount}");
        $this->info("  User 79 exists: " . ($user79 ? 'YES' : 'NO'));
        if ($user79) {
            $this->info("  User 79: {$user79->name} ({$user79->email})");
        }
        $this->newLine();

        // Check Customers
        $customerCount = Customer::count();
        $customer1 = Customer::find(1);
        $this->info("🛒 Customers:");
        $this->info("  Total customers: {$customerCount}");
        $this->info("  Customer 1 exists: " . ($customer1 ? 'YES' : 'NO'));
        if ($customer1) {
            $this->info("  Customer 1: {$customer1->name} ({$customer1->phone})");
        }
        $this->newLine();

        // Check Tables
        $tableCount = TableLayout::count();
        $table3 = TableLayout::find(3);
        $this->info("🪑 Tables:");
        $this->info("  Total tables: {$tableCount}");
        $this->info("  Table 3 exists: " . ($table3 ? 'YES' : 'NO'));
        if ($table3) {
            $this->info("  Table 3: {$table3->table_number} (Seats: {$table3->seats})");
        }
        $this->newLine();

        // Show some sample data
        $this->info("📋 Sample Data:");
        $this->info("Recent Users:");
        $recentUsers = User::orderBy('id', 'desc')->limit(3)->get();
        foreach ($recentUsers as $user) {
            $this->info("  - ID {$user->id}: {$user->name}");
        }

        $this->info("Recent Customers:");
        $recentCustomers = Customer::orderBy('id', 'desc')->limit(3)->get();
        foreach ($recentCustomers as $customer) {
            $this->info("  - ID {$customer->id}: {$customer->name}");
        }

        $this->info("Recent Tables:");
        $recentTables = TableLayout::orderBy('id', 'desc')->limit(3)->get();
        foreach ($recentTables as $table) {
            $this->info("  - ID {$table->id}: Table {$table->table_number}");
        }

        return 0;
    }
}
