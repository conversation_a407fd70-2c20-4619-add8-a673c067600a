<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Table') }}
            </h2>
            <a href="{{ route('tables.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Tables
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @if($errors->any())
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            <ul class="list-disc list-inside">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('tables.update', $table) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-6">
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Table Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name', $table->name) }}"
                                   class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('name') border-red-500 @enderror"
                                   placeholder="Enter table name (e.g., Table 1, VIP Table A)"
                                   required>
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                Status <span class="text-red-500">*</span>
                            </label>
                            <select id="status" 
                                    name="status" 
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('status') border-red-500 @enderror"
                                    required>
                                <option value="Available" {{ old('status', $table->status) === 'Available' ? 'selected' : '' }}>
                                    Available
                                </option>
                                <option value="Occupied" {{ old('status', $table->status) === 'Occupied' ? 'selected' : '' }}>
                                    Occupied
                                </option>
                            </select>
                            @error('status')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500">
                                Note: Setting status to "Occupied" manually should only be done if there's an active order for this table.
                            </p>
                        </div>

                        <!-- Current Order Information (if exists) -->
                        @if($table->currentOrder)
                            <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                                <h3 class="text-sm font-medium text-yellow-800 mb-2">Current Order Information</h3>
                                <div class="text-sm text-yellow-700">
                                    <p><strong>Order ID:</strong> #{{ $table->currentOrder->id }}</p>
                                    <p><strong>Customer:</strong> {{ $table->currentOrder->customer->name ?? 'N/A' }}</p>
                                    <p><strong>Total Amount:</strong> ${{ number_format($table->currentOrder->total_amount, 2) }}</p>
                                    <p><strong>Status:</strong> {{ $table->currentOrder->status }}</p>
                                    <p><strong>Created:</strong> {{ $table->currentOrder->created_at->format('M d, Y H:i') }}</p>
                                </div>
                                <p class="mt-2 text-xs text-yellow-600">
                                    This table has an active order. Consider the order status when changing the table status.
                                </p>
                            </div>
                        @endif

                        <div class="flex items-center justify-between">
                            <div class="flex space-x-3">
                                <button type="submit" 
                                        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md transition-colors duration-200">
                                    Update Table
                                </button>
                                <a href="{{ route('tables.index') }}" 
                                   class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded-md transition-colors duration-200">
                                    Cancel
                                </a>
                            </div>
                            
                            @if(!$table->currentOrder)
                                <form action="{{ route('tables.destroy', $table) }}" method="POST" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" 
                                            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md transition-colors duration-200"
                                            onclick="return confirm('Are you sure you want to delete this table? This action cannot be undone.')">
                                        Delete Table
                                    </button>
                                </form>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
