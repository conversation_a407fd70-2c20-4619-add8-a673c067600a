# Product Creation & API Sync Documentation

## Overview

The POS system now requires **online connectivity** for product creation and automatically syncs all new products to the external API at `viera-filament.test/api/pos/sync/products`.

## Key Features

### 🌐 Network Connectivity Requirement
- **Products can only be created when online**
- Real-time network status monitoring
- User-friendly error messages in Indonesian
- Form validation prevents submission when offline

### 🔄 Automatic API Synchronization
- **Every new product is automatically synced to API**
- Immediate sync after local product creation
- Graceful error handling with user feedback
- API ID tracking for synchronized products

### 📊 Enhanced Product Structure
- Full support for all new product fields (SKU, barcode, cost price, stock quantity, etc.)
- Backward compatibility with legacy API responses
- Professional form design matching modern standards

## Technical Implementation

### Network Validation Flow

```php
// 1. Check network connectivity before allowing product creation
if (!$this->productSyncService->isNetworkAvailable()) {
    return redirect()->back()
        ->with('error', 'Anda tidak memiliki koneksi internet. Produk hanya dapat dibuat saat online untuk sinkronisasi dengan API.')
        ->withInput();
}

// 2. Create product locally
$menuItem = MenuItem::create($data);

// 3. Automatically sync to API
$syncResult = $this->productSyncService->syncProductToApi($menuItem);
```

### API Sync Process

1. **Network Check**: Validates connectivity to API endpoint
2. **Authentication**: Obtains Bearer token for API calls
3. **Data Preparation**: Formats product data for API submission
4. **API Call**: POST to `viera-filament.test/api/pos/sync/products`
5. **Response Handling**: Updates local product with API ID if successful

### Data Structure Sent to API

```json
{
  "name": "Product Name",
  "sku": "PROD-001",
  "barcode": "1234567890",
  "description": "Product description",
  "price": 25000,
  "cost_price": 15000,
  "stock_quantity": 100,
  "is_active": true,
  "is_food_item": true,
  "image": "product.jpg",
  "category": {
    "id": 1,
    "name": "Category Name"
  },
  "local_id": 123,
  "created_at": "2025-01-15T10:30:00.000Z",
  "updated_at": "2025-01-15T10:30:00.000Z"
}
```

## User Experience

### ✅ Online Mode (Normal Operation)
1. User fills out product form
2. Network indicator shows connected status
3. Form submission is enabled
4. Product is created and synced automatically
5. Success message: "Produk berhasil dibuat dan disinkronkan ke API"

### ❌ Offline Mode (Blocked Operation)
1. User fills out product form
2. Network indicator shows disconnected status
3. Form submission is disabled
4. Warning message displayed: "Koneksi Internet Diperlukan"
5. Error on attempt: "Anda tidak memiliki koneksi internet. Produk hanya dapat dibuat saat online untuk sinkronisasi dengan API."

### ⚠️ Partial Failure (Local Success, API Failure)
1. Product is created locally
2. API sync fails (network issues, API down, etc.)
3. Warning message: "Produk berhasil dibuat secara lokal, tetapi gagal disinkronkan ke API: [error details]"
4. Product remains in local database for later sync

## Configuration

### Environment Variables

Add to your `.env` file:

```env
# API Configuration
POS_API_BASE_URL=http://viera-filament.test/api/pos
POS_API_USERNAME=your_api_username
POS_API_PASSWORD=your_api_password
POS_API_TIMEOUT=30
```

### Config File (`config/pos.php`)

```php
'api_base_url' => env('POS_API_BASE_URL', 'http://viera-filament.test/api/pos'),
'api_username' => env('POS_API_USERNAME'),
'api_password' => env('POS_API_PASSWORD'),
'api_timeout' => env('POS_API_TIMEOUT', 30),
```

## API Endpoints

### Product Sync Endpoint
- **URL**: `POST /api/pos/sync/products`
- **Authentication**: Bearer Token
- **Content-Type**: `application/json`

### Health Check Endpoint
- **URL**: `GET /api/pos/health-check`
- **Purpose**: Network connectivity validation
- **Response**: JSON status information

## Error Handling

### Network Errors
- Connection timeout: 5 seconds for health checks
- Fallback connectivity test to Google favicon
- User-friendly Indonesian error messages

### API Errors
- Authentication failures: Token refresh attempts
- Sync failures: Graceful degradation with local storage
- Detailed logging for debugging

### User Feedback
- **Success**: Green success messages
- **Warning**: Yellow warning messages for partial failures
- **Error**: Red error messages for complete failures

## Monitoring & Logging

### Network Status
- Real-time network monitoring with visual indicators
- Periodic connectivity checks (every 30 seconds)
- Manual refresh capability

### API Sync Logging
- Successful syncs logged with product details
- Failed syncs logged with error details
- Performance metrics (response times)

## Backward Compatibility

### Legacy API Support
- Supports both new and old field names
- Graceful fallbacks for missing fields
- Maintains existing sync functionality

### Field Mapping
- `is_available` → `is_active`
- `image_path` → `image`
- Default values for new fields

## Testing

The system includes comprehensive testing for:
- Network connectivity validation
- API sync functionality
- Error handling scenarios
- Data structure validation
- User interface behavior

## Security

### Authentication
- Bearer token authentication
- Token caching with expiration
- Automatic re-authentication on token expiry

### Data Validation
- Server-side validation for all fields
- Client-side validation for user experience
- SQL injection protection
- XSS protection

## Performance

### Optimization
- Connection pooling for API calls
- Response caching where appropriate
- Timeout optimization (5s for health checks, 30s for sync)
- Minimal network overhead

### Monitoring
- Response time tracking
- Success/failure rate monitoring
- Network latency measurement
