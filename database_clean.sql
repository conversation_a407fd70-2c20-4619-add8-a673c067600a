-- MySQL Database Schema for POS System
-- Converted from SQLite to MySQL format

SET FOREIGN_KEY_CHECKS = 0;
START TRANSACTION;

-- Create migrations table
CREATE TABLE IF NOT EXISTS `migrations` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `migration` VARCHAR(255) NOT NULL,
    `batch` INT NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert migration records
INSERT INTO migrations VALUES(1,'0001_01_01_000000_create_users_table',1);
INSERT INTO migrations VALUES(2,'0001_01_01_000001_create_cache_table',1);
INSERT INTO migrations VALUES(3,'0001_01_01_000002_create_jobs_table',1);
INSERT INTO migrations VALUES(4,'2025_07_18_043034_modify_users_table_for_pos',1);
INSERT INTO migrations VALUES(5,'2025_07_18_043046_create_customers_table',1);
INSERT INTO migrations VALUES(6,'2025_07_18_043055_create_menu_categories_table',1);
INSERT INTO migrations VALUES(7,'2025_07_18_043124_create_menu_items_table',1);
INSERT INTO migrations VALUES(8,'2025_07_18_043134_create_table_layouts_table',1);
INSERT INTO migrations VALUES(9,'2025_07_18_043140_create_orders_table',1);
INSERT INTO migrations VALUES(10,'2025_07_18_043146_create_order_items_table',1);
INSERT INTO migrations VALUES(11,'2025_07_18_043159_create_payments_table',1);
INSERT INTO migrations VALUES(12,'2025_07_18_043207_create_ingredients_table',1);
INSERT INTO migrations VALUES(13,'2025_07_18_043214_create_suppliers_table',1);
INSERT INTO migrations VALUES(14,'2025_07_18_043220_create_purchases_table',1);
INSERT INTO migrations VALUES(15,'2025_07_18_043227_create_purchase_items_table',1);
INSERT INTO migrations VALUES(16,'2025_07_18_043236_create_recipes_table',1);
INSERT INTO migrations VALUES(17,'2025_07_18_064337_update_orders_table_add_paid_status',1);
INSERT INTO migrations VALUES(18,'2025_07_21_022947_add_email_and_address_to_customers_table',2);
INSERT INTO migrations VALUES(19,'2025_07_21_030114_add_order_type_to_orders_table',3);
INSERT INTO migrations VALUES(23,'2025_07_21_065152_add_discount_fields_to_orders_table',4);
INSERT INTO migrations VALUES(24,'2025_07_21_065435_create_coupons_table',4);
INSERT INTO migrations VALUES(25,'2025_07_21_065534_add_points_to_customers_table',4);
INSERT INTO migrations VALUES(26,'2025_07_22_025234_add_party_size_to_orders_table',5);
INSERT INTO migrations VALUES(27,'2025_07_22_041036_create_order_settings_table',6);
INSERT INTO migrations VALUES(28,'2025_07_22_041549_add_service_charge_to_orders_table',7);
INSERT INTO migrations VALUES(29,'2025_07_22_041614_add_service_charge_to_orders_table',7);
INSERT INTO migrations VALUES(30,'2025_07_22_044436_create_order_discounts_table',8);

-- Create users table
CREATE TABLE IF NOT EXISTS `users` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `email` VARCHAR(255) NOT NULL,
    `password` VARCHAR(255) NOT NULL,
    `remember_token` VARCHAR(100),
    `created_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP NULL DEFAULT NULL,
    `username` VARCHAR(255) NOT NULL,
    `role` ENUM('Admin', 'Cashier') NOT NULL DEFAULT 'Cashier',
    UNIQUE KEY `users_email_unique` (`email`),
    UNIQUE KEY `users_username_unique` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
INSERT INTO users VALUES(1,'Administrator','<EMAIL>','$2y$12$tqo7dMrWwpIVsEvLB6.Yoexvikyt0wLOEB7nfZSZsLeIEvE.fxKQK',NULL,'2025-07-18 08:57:06','2025-07-18 08:57:06','admin','Admin');
INSERT INTO users VALUES(2,'Cashier','<EMAIL>','$2y$12$y7bTKLhrYOMFC/8AcnBeJeBYyXcUjpLGNCxD.4vEPeHG/0RStiTU2',NULL,'2025-07-18 08:57:07','2025-07-18 08:57:07','cashier','Cashier');
CREATE TABLE IF NOT EXISTS "password_reset_tokens" ("email" varchar not null, "token" varchar not null, "created_at" datetime, primary key ("email"));
CREATE TABLE IF NOT EXISTS "sessions" ("id" varchar not null, "user_id" integer, "ip_address" varchar, "user_agent" text, "payload" text not null, "last_activity" integer not null, primary key ("id"));
INSERT INTO sessions VALUES('1VCtZqqDFisc4qAO4mnG2j7Esxj5oysX8hGpi1V0',1,'127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','YTo0OntzOjY6Il90b2tlbiI7czo0MDoiemFmWW9MRDJSNDh0OWFacW9hZWk1ODhIeTNLaHhCTDBwU2NTM2hYOSI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MTk6Imh0dHA6Ly9wb3MudGVzdC9wb3MiO31zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aToxO30=',1753179799);
INSERT INTO sessions VALUES('q2JqBRSlb8ROND7c35EM2IcbKxr2hhcHqR2T2qsZ',NULL,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','YTo0OntzOjY6Il90b2tlbiI7czo0MDoiUDJvRExCVmoxdDRkT2xxQVBtdElqQUo4U1hlYWN5ME5pZ3pJbjJ0UiI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czozMToiaHR0cDovL2xvY2FsaG9zdC9QT1MvcHVibGljL3BvcyI7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjMzOiJodHRwOi8vbG9jYWxob3N0L1BPUy9wdWJsaWMvbG9naW4iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19',1753178270);
CREATE TABLE IF NOT EXISTS "cache" ("key" varchar not null, "value" text not null, "expiration" integer not null, primary key ("key"));
CREATE TABLE IF NOT EXISTS "cache_locks" ("key" varchar not null, "owner" varchar not null, "expiration" integer not null, primary key ("key"));
CREATE TABLE IF NOT EXISTS "jobs" ("id" integer primary key  not null, "queue" varchar not null, "payload" text not null, "attempts" integer not null, "reserved_at" integer, "available_at" integer not null, "created_at" integer not null);
CREATE TABLE IF NOT EXISTS "job_batches" ("id" varchar not null, "name" varchar not null, "total_jobs" integer not null, "pending_jobs" integer not null, "failed_jobs" integer not null, "failed_job_ids" text not null, "options" text, "cancelled_at" integer, "created_at" integer not null, "finished_at" integer, primary key ("id"));
CREATE TABLE IF NOT EXISTS "failed_jobs" ("id" integer primary key  not null, "uuid" varchar not null, "connection" text not null, "queue" text not null, "payload" text not null, "exception" text not null, "failed_at" datetime not null default CURRENT_TIMESTAMP);
CREATE TABLE IF NOT EXISTS "customers" ("id" integer primary key  not null, "name" varchar not null default 'Walk-in', "phone" varchar, "created_at" datetime, "updated_at" datetime, "email" varchar, "address" text, "points" integer not null default '0');
INSERT INTO customers VALUES(1,'Budi Santoso','081234567890','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Sudirman No. 123, Jakarta Pusat',0);
INSERT INTO customers VALUES(2,'Siti Nurhaliza','081234567891','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Thamrin No. 45, Jakarta Pusat',0);
INSERT INTO customers VALUES(3,'Ahmad Wijaya','081234567892','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Gatot Subroto No. 67, Jakarta Selatan',0);
INSERT INTO customers VALUES(4,'Dewi Lestari','081234567893','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Kemang Raya No. 89, Jakarta Selatan',0);
INSERT INTO customers VALUES(5,'Rudi Hartono','081234567894','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Senopati No. 12, Jakarta Selatan',0);
INSERT INTO customers VALUES(6,'Maya Sari','081234567895','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Menteng Raya No. 34, Jakarta Pusat',0);
INSERT INTO customers VALUES(7,'Andi Prasetyo','081234567896','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Kuningan No. 56, Jakarta Selatan',0);
INSERT INTO customers VALUES(8,'Rina Wulandari','081234567897','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Pondok Indah No. 78, Jakarta Selatan',0);
INSERT INTO customers VALUES(9,'Hendra Gunawan','081234567898','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Kelapa Gading No. 90, Jakarta Utara',0);
INSERT INTO customers VALUES(10,'Lina Marlina','081234567899','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Pluit Raya No. 11, Jakarta Utara',0);
INSERT INTO customers VALUES(11,'Bambang Sutrisno','081234567800','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Cempaka Putih No. 22, Jakarta Pusat',0);
INSERT INTO customers VALUES(12,'Indira Sari','081234567801','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Tebet Raya No. 33, Jakarta Selatan',0);
INSERT INTO customers VALUES(13,'Joko Widodo','081234567802','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Pancoran No. 44, Jakarta Selatan',0);
INSERT INTO customers VALUES(14,'Sri Mulyani','081234567803','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Pasar Minggu No. 55, Jakarta Selatan',0);
INSERT INTO customers VALUES(15,'Agus Salim','081234567804','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Cikini Raya No. 66, Jakarta Pusat',0);
INSERT INTO customers VALUES(16,'Ratna Dewi','081234567805','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Salemba No. 77, Jakarta Pusat',0);
INSERT INTO customers VALUES(17,'Doni Setiawan','081234567806','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Mangga Besar No. 88, Jakarta Barat',0);
INSERT INTO customers VALUES(18,'Fitri Handayani','081234567807','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Grogol No. 99, Jakarta Barat',0);
INSERT INTO customers VALUES(19,'Wahyu Nugroho','081234567808','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Jelambar No. 101, Jakarta Barat',0);
INSERT INTO customers VALUES(20,'Sari Indah','081234567809','2025-07-21 03:21:56','2025-07-21 07:18:59','<EMAIL>','Jl. Puri Indah No. 102, Jakarta Barat',0);
INSERT INTO customers VALUES(21,'Tinto Pratama','0895634811819','2025-07-21 03:25:00','2025-07-21 07:18:59','<EMAIL>','Jalan Gajah Mada',0);
INSERT INTO customers VALUES(22,'Guest Customer','000-000-0000','2025-07-21 04:48:27','2025-07-21 09:20:36',NULL,NULL,0);
CREATE TABLE IF NOT EXISTS "menu_categories" ("id" integer primary key  not null, "name" varchar not null, "created_at" datetime, "updated_at" datetime);
INSERT INTO menu_categories VALUES(1,'Makanan Utama','2025-07-18 08:57:07','2025-07-18 08:57:07');
INSERT INTO menu_categories VALUES(2,'Minuman','2025-07-18 08:57:07','2025-07-18 08:57:07');
INSERT INTO menu_categories VALUES(3,'Appetizer','2025-07-18 08:57:07','2025-07-18 08:57:07');
INSERT INTO menu_categories VALUES(4,'Dessert','2025-07-18 08:57:07','2025-07-18 08:57:07');
INSERT INTO menu_categories VALUES(5,'Snack','2025-07-18 08:57:07','2025-07-18 08:57:07');
CREATE TABLE IF NOT EXISTS "menu_items" ("id" integer primary key  not null, "category_id" integer not null, "name" varchar not null, "description" text, "price" numeric not null, "image_path" varchar, "created_at" datetime, "updated_at" datetime, foreign key("category_id") references "menu_categories"("id") on delete cascade);
INSERT INTO menu_items VALUES(1,1,'Nasi Goreng Spesial','Nasi goreng dengan telur, ayam, dan sayuran',25000,'menu-items/nasi-goreng-spesial.jpg','2025-07-18 08:57:07','2025-07-21 04:02:27');
INSERT INTO menu_items VALUES(2,1,'Mie Ayam Bakso','Mie ayam dengan bakso dan pangsit',20000,'menu-items/mie-ayam-bakso.jpg','2025-07-18 08:57:07','2025-07-21 03:17:44');
INSERT INTO menu_items VALUES(3,1,'Ayam Bakar','Ayam bakar dengan sambal dan lalapan',30000,'menu-items/ayam-bakar.jpg','2025-07-18 08:57:07','2025-07-21 03:17:45');
INSERT INTO menu_items VALUES(4,1,'Gado-Gado','Sayuran dengan bumbu kacang',18000,'menu-items/gado-gado.jpg','2025-07-18 08:57:07','2025-07-21 03:17:45');
INSERT INTO menu_items VALUES(5,2,'Es Teh Manis','Teh manis dingin',5000,'menu-items/es-teh-manis.jpg','2025-07-18 08:57:07','2025-07-21 03:17:46');
INSERT INTO menu_items VALUES(6,2,'Es Jeruk','Jus jeruk segar',8000,'menu-items/es-jeruk.jpg','2025-07-18 08:57:07','2025-07-21 03:17:46');
INSERT INTO menu_items VALUES(7,2,'Kopi Hitam','Kopi hitam panas',6000,'menu-items/kopi-hitam.jpg','2025-07-18 08:57:07','2025-07-21 03:17:48');
INSERT INTO menu_items VALUES(8,2,'Jus Alpukat','Jus alpukat segar',12000,'menu-items/jus-alpukat.jpg','2025-07-18 08:57:07','2025-07-21 03:17:48');
INSERT INTO menu_items VALUES(9,3,'Kerupuk','Kerupuk udang crispy',3000,'menu-items/kerupuk.jpg','2025-07-18 08:57:08','2025-07-21 03:17:49');
INSERT INTO menu_items VALUES(10,3,'Tahu Goreng','Tahu goreng dengan sambal kecap',8000,'menu-items/tahu-goreng.jpg','2025-07-18 08:57:08','2025-07-21 03:17:49');
INSERT INTO menu_items VALUES(11,3,'Tempe Mendoan','Tempe goreng tepung',7000,'menu-items/tempe-mendoan.jpg','2025-07-18 08:57:08','2025-07-21 03:17:50');
INSERT INTO menu_items VALUES(12,4,'Es Krim Vanilla','Es krim vanilla dengan topping',10000,'menu-items/es-krim-vanilla.jpg','2025-07-18 08:57:08','2025-07-21 03:17:50');
INSERT INTO menu_items VALUES(13,4,'Pisang Goreng','Pisang goreng dengan madu',8000,'menu-items/pisang-goreng.jpg','2025-07-18 08:57:08','2025-07-21 03:17:51');
INSERT INTO menu_items VALUES(14,4,'Puding Coklat','Puding coklat lembut',7000,'menu-items/puding-coklat.jpg','2025-07-18 08:57:08','2025-07-21 03:17:51');
INSERT INTO menu_items VALUES(15,5,'Kentang Goreng','Kentang goreng crispy',12000,'menu-items/kentang-goreng.jpg','2025-07-18 08:57:08','2025-07-21 03:17:52');
INSERT INTO menu_items VALUES(16,5,'Onion Rings','Bawang bombay goreng tepung',15000,'menu-items/onion-rings.jpg','2025-07-18 08:57:08','2025-07-21 03:17:52');
INSERT INTO menu_items VALUES(17,5,'Chicken Wings','Sayap ayam goreng pedas',18000,'menu-items/chicken-wings.jpg','2025-07-18 08:57:08','2025-07-21 03:17:52');
CREATE TABLE IF NOT EXISTS "table_layouts" ("id" integer primary key  not null, "name" varchar not null, "status" varchar check ("status" in ('Available', 'Occupied')) not null default 'Available', "created_at" datetime, "updated_at" datetime);
INSERT INTO table_layouts VALUES(1,'Table 1','Available','2025-07-18 08:57:08','2025-07-22 04:21:58');
INSERT INTO table_layouts VALUES(2,'Table 2','Available','2025-07-18 08:57:08','2025-07-22 09:16:53');
INSERT INTO table_layouts VALUES(3,'Table 3','Available','2025-07-18 08:57:08','2025-07-22 10:01:05');
INSERT INTO table_layouts VALUES(4,'Table 4','Available','2025-07-18 08:57:08','2025-07-22 09:57:17');
INSERT INTO table_layouts VALUES(5,'Table 5','Available','2025-07-18 08:57:08','2025-07-22 09:06:52');
INSERT INTO table_layouts VALUES(6,'Table 6','Available','2025-07-18 08:57:08','2025-07-22 10:07:18');
INSERT INTO table_layouts VALUES(7,'Table 7','Available','2025-07-18 08:57:08','2025-07-21 10:21:23');
INSERT INTO table_layouts VALUES(8,'Table 8','Available','2025-07-18 08:57:08','2025-07-21 10:21:09');
INSERT INTO table_layouts VALUES(9,'Table 9','Available','2025-07-18 08:57:08','2025-07-21 10:20:54');
INSERT INTO table_layouts VALUES(10,'Table 10','Available','2025-07-18 08:57:09','2025-07-18 08:57:09');
INSERT INTO table_layouts VALUES(11,'Table 11','Available','2025-07-22 02:45:20','2025-07-22 10:09:12');
CREATE TABLE IF NOT EXISTS "order_items" ("id" integer primary key  not null, "order_id" integer not null, "menu_item_id" integer not null, "quantity" integer not null, "price_at_time" numeric not null, "notes" text, "created_at" datetime, "updated_at" datetime, foreign key("order_id") references "orders"("id") on delete cascade, foreign key("menu_item_id") references "menu_items"("id") on delete cascade);
INSERT INTO order_items VALUES(10,4,2,1,20000,NULL,'2025-07-21 04:30:22','2025-07-21 04:30:22');
INSERT INTO order_items VALUES(11,4,3,1,30000,NULL,'2025-07-21 04:30:22','2025-07-21 04:30:22');
INSERT INTO order_items VALUES(12,5,2,1,20000,NULL,'2025-07-21 04:47:21','2025-07-21 04:47:21');
INSERT INTO order_items VALUES(13,5,2,1,20000,NULL,'2025-07-21 04:47:23','2025-07-21 04:47:23');
INSERT INTO order_items VALUES(14,6,2,1,20000,NULL,'2025-07-21 04:48:27','2025-07-21 04:48:27');
INSERT INTO order_items VALUES(15,6,3,1,30000,NULL,'2025-07-21 04:48:27','2025-07-21 04:48:27');
INSERT INTO order_items VALUES(16,7,2,1,20000,NULL,'2025-07-21 04:54:28','2025-07-21 04:54:28');
INSERT INTO order_items VALUES(17,7,3,1,30000,NULL,'2025-07-21 04:54:32','2025-07-21 04:54:32');
INSERT INTO order_items VALUES(18,8,3,3,30000,NULL,'2025-07-21 05:10:38','2025-07-21 05:13:51');
INSERT INTO order_items VALUES(19,8,2,1,20000,NULL,'2025-07-21 05:10:38','2025-07-21 05:10:38');
INSERT INTO order_items VALUES(20,8,1,1,25000,NULL,'2025-07-21 05:10:38','2025-07-21 05:10:38');
INSERT INTO order_items VALUES(21,8,3,1,30000,NULL,'2025-07-21 05:10:54','2025-07-21 05:10:54');
INSERT INTO order_items VALUES(22,9,3,6,30000,NULL,'2025-07-21 05:20:08','2025-07-21 05:22:03');
INSERT INTO order_items VALUES(23,9,2,1,20000,NULL,'2025-07-21 05:20:08','2025-07-21 05:20:08');
INSERT INTO order_items VALUES(24,9,3,4,30000,NULL,'2025-07-21 05:20:26','2025-07-21 05:20:52');
INSERT INTO order_items VALUES(25,11,3,3,30000,NULL,'2025-07-21 07:48:22','2025-07-21 07:48:27');
INSERT INTO order_items VALUES(26,12,3,3,30000,NULL,'2025-07-21 09:20:06','2025-07-21 09:20:06');
INSERT INTO order_items VALUES(27,12,1,3,25000,NULL,'2025-07-21 09:20:12','2025-07-21 09:20:12');
INSERT INTO order_items VALUES(28,15,3,2,30000,NULL,'2025-07-21 09:59:35','2025-07-21 09:59:35');
INSERT INTO order_items VALUES(29,15,2,1,20000,NULL,'2025-07-21 09:59:35','2025-07-21 09:59:35');
INSERT INTO order_items VALUES(30,23,2,1,20000,NULL,'2025-07-22 02:13:36','2025-07-22 02:13:36');
INSERT INTO order_items VALUES(31,23,3,1,30000,NULL,'2025-07-22 02:13:36','2025-07-22 02:13:36');
INSERT INTO order_items VALUES(32,23,4,1,18000,NULL,'2025-07-22 02:13:36','2025-07-22 02:13:36');
INSERT INTO order_items VALUES(33,24,3,1,30000,NULL,'2025-07-22 02:22:28','2025-07-22 02:22:28');
INSERT INTO order_items VALUES(34,24,2,1,20000,NULL,'2025-07-22 02:22:28','2025-07-22 02:22:28');
INSERT INTO order_items VALUES(35,24,1,1,25000,NULL,'2025-07-22 02:22:28','2025-07-22 02:22:28');
INSERT INTO order_items VALUES(36,25,2,3,20000,NULL,'2025-07-22 02:33:27','2025-07-22 02:33:27');
INSERT INTO order_items VALUES(37,25,1,1,25000,NULL,'2025-07-22 02:33:27','2025-07-22 02:33:27');
INSERT INTO order_items VALUES(38,26,6,2,8000,NULL,'2025-07-22 02:42:47','2025-07-22 02:42:47');
INSERT INTO order_items VALUES(39,26,5,2,5000,NULL,'2025-07-22 02:42:47','2025-07-22 02:42:47');
INSERT INTO order_items VALUES(40,26,11,2,7000,NULL,'2025-07-22 02:42:47','2025-07-22 02:42:47');
INSERT INTO order_items VALUES(41,27,3,4,30000,NULL,'2025-07-22 02:44:00','2025-07-22 02:44:00');
INSERT INTO order_items VALUES(42,27,2,2,20000,NULL,'2025-07-22 02:44:00','2025-07-22 02:44:00');
INSERT INTO order_items VALUES(43,28,3,1,30000,NULL,'2025-07-22 03:20:54','2025-07-22 03:20:54');
INSERT INTO order_items VALUES(44,28,2,1,20000,NULL,'2025-07-22 03:20:54','2025-07-22 03:20:54');
INSERT INTO order_items VALUES(45,29,3,1,30000,NULL,'2025-07-22 04:21:49','2025-07-22 04:21:49');
INSERT INTO order_items VALUES(46,29,2,1,20000,NULL,'2025-07-22 04:21:49','2025-07-22 04:21:49');
INSERT INTO order_items VALUES(47,30,3,2,30000,NULL,'2025-07-22 04:41:35','2025-07-22 04:41:35');
INSERT INTO order_items VALUES(48,31,3,4,30000,NULL,'2025-07-22 08:36:14','2025-07-22 08:36:14');
INSERT INTO order_items VALUES(49,32,3,2,30000,NULL,'2025-07-22 09:16:41','2025-07-22 09:16:41');
INSERT INTO order_items VALUES(50,32,2,1,20000,NULL,'2025-07-22 09:16:41','2025-07-22 09:16:41');
INSERT INTO order_items VALUES(51,33,3,4,30000,NULL,'2025-07-22 09:56:29','2025-07-22 09:56:29');
INSERT INTO order_items VALUES(52,34,3,5,30000,NULL,'2025-07-22 10:00:55','2025-07-22 10:00:55');
INSERT INTO order_items VALUES(53,35,3,10,30000,NULL,'2025-07-22 10:06:13','2025-07-22 10:06:13');
INSERT INTO order_items VALUES(54,36,2,7,20000,NULL,'2025-07-22 10:09:02','2025-07-22 10:09:02');
INSERT INTO order_items VALUES(55,36,3,2,30000,NULL,'2025-07-22 10:09:02','2025-07-22 10:09:02');
INSERT INTO order_items VALUES(56,36,1,2,25000,NULL,'2025-07-22 10:09:02','2025-07-22 10:09:02');
CREATE TABLE IF NOT EXISTS "payments" ("id" integer primary key  not null, "order_id" integer not null, "method" varchar check ("method" in ('Cash', 'Card', 'QRIS')) not null, "amount_paid" numeric not null, "created_at" datetime, "updated_at" datetime, foreign key("order_id") references "orders"("id") on delete cascade);
INSERT INTO payments VALUES(4,11,'Cash',99000,'2025-07-21 07:48:54','2025-07-21 07:48:54');
INSERT INTO payments VALUES(5,8,'Cash',181500,'2025-07-21 08:01:53','2025-07-21 08:01:53');
INSERT INTO payments VALUES(6,12,'Cash',181497.799999999988,'2025-07-21 09:21:09','2025-07-21 09:21:09');
INSERT INTO payments VALUES(7,9,'Cash',352000,'2025-07-21 09:21:29','2025-07-21 09:21:29');
INSERT INTO payments VALUES(8,5,'Cash',44000,'2025-07-21 09:22:28','2025-07-21 09:22:28');
INSERT INTO payments VALUES(9,23,'Cash',74800,'2025-07-22 02:31:40','2025-07-22 02:31:40');
INSERT INTO payments VALUES(10,25,'Cash',93500,'2025-07-22 02:33:52','2025-07-22 02:33:52');
INSERT INTO payments VALUES(11,26,'Cash',44000,'2025-07-22 02:43:09','2025-07-22 02:43:09');
INSERT INTO payments VALUES(12,27,'Cash',176000,'2025-07-22 02:44:24','2025-07-22 02:44:24');
INSERT INTO payments VALUES(13,28,'Cash',55000,'2025-07-22 03:59:28','2025-07-22 03:59:28');
INSERT INTO payments VALUES(14,29,'Cash',55000,'2025-07-22 04:22:01','2025-07-22 04:22:01');
INSERT INTO payments VALUES(15,31,'Cash',107800,'2025-07-22 09:06:58','2025-07-22 09:06:58');
INSERT INTO payments VALUES(16,32,'Cash',0.0100000000000000002,'2025-07-22 09:36:52','2025-07-22 09:36:52');
INSERT INTO payments VALUES(17,33,'Card',121000,'2025-07-22 09:57:36','2025-07-22 09:57:36');
INSERT INTO payments VALUES(18,34,'Cash',110000,'2025-07-22 10:01:53','2025-07-22 10:01:53');
INSERT INTO payments VALUES(19,35,'Card',330000,'2025-07-22 10:07:27','2025-07-22 10:07:27');
INSERT INTO payments VALUES(20,36,'Cash',200000,'2025-07-22 10:11:53','2025-07-22 10:11:53');
CREATE TABLE IF NOT EXISTS "ingredients" ("id" integer primary key  not null, "name" varchar not null, "unit" varchar not null, "stock_level" numeric not null default '0', "created_at" datetime, "updated_at" datetime);
CREATE TABLE IF NOT EXISTS "suppliers" ("id" integer primary key  not null, "name" varchar not null, "contact_info" text, "created_at" datetime, "updated_at" datetime);
CREATE TABLE IF NOT EXISTS "purchases" ("id" integer primary key  not null, "supplier_id" integer not null, "purchase_date" datetime not null, "created_at" datetime, "updated_at" datetime, foreign key("supplier_id") references "suppliers"("id") on delete cascade);
CREATE TABLE IF NOT EXISTS "purchase_items" ("purchase_id" integer not null, "ingredient_id" integer not null, "quantity" numeric not null, "unit_price" numeric not null, "created_at" datetime, "updated_at" datetime, foreign key("purchase_id") references "purchases"("id") on delete cascade, foreign key("ingredient_id") references "ingredients"("id") on delete cascade, primary key ("purchase_id", "ingredient_id"));
CREATE TABLE IF NOT EXISTS "recipes" ("menu_item_id" integer not null, "ingredient_id" integer not null, "quantity_needed" numeric not null, "created_at" datetime, "updated_at" datetime, foreign key("menu_item_id") references "menu_items"("id") on delete cascade, foreign key("ingredient_id") references "ingredients"("id") on delete cascade, primary key ("menu_item_id", "ingredient_id"));
CREATE TABLE IF NOT EXISTS "orders" ("id" integer primary key  not null, "user_id" integer not null, "customer_id" integer not null, "table_id" integer not null, "status" varchar check ("status" in ('Pending', 'Completed', 'Cancelled', 'Paid')) not null default 'Pending', "total_amount" numeric not null default ('0'), "tax_amount" numeric not null default ('0'), "created_at" datetime, "updated_at" datetime, "order_type" varchar check ("order_type" in ('dine_in', 'take_away')) not null default 'dine_in', "discount_type" varchar check ("discount_type" in ('none', 'percentage', 'fixed', 'coupon', 'points')) not null default 'none', "discount_value" numeric not null default '0', "discount_amount" numeric not null default '0', "coupon_code" varchar, "points_used" integer not null default '0', "subtotal_amount" numeric not null default '0', "party_size" integer not null default '2', "service_charge_amount" numeric not null default '0', foreign key("table_id") references table_layouts("id") on delete cascade on update no action, foreign key("customer_id") references customers("id") on delete cascade on update no action, foreign key("user_id") references users("id") on delete cascade on update no action);
INSERT INTO orders VALUES(4,1,1,4,'Completed',55000,5000,'2025-07-21 04:30:22','2025-07-21 04:33:32','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(5,1,3,1,'Completed',44000,4000,'2025-07-21 04:47:05','2025-07-21 09:22:54','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(6,1,22,7,'Cancelled',55000,5000,'2025-07-21 04:48:27','2025-07-21 09:51:26','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(7,1,22,5,'Completed',0,0,'2025-07-21 04:54:15','2025-07-21 09:40:02','dine_in','fixed',100000,50000,NULL,0,50000,2,0);
INSERT INTO orders VALUES(8,1,22,3,'Completed',181500,16500,'2025-07-21 05:10:38','2025-07-21 08:02:46','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(9,1,22,4,'Completed',352000,32000,'2025-07-21 05:20:08','2025-07-21 09:21:45','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(10,1,22,6,'Cancelled',0,0,'2025-07-21 07:08:40','2025-07-21 09:51:12','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(11,1,22,8,'Cancelled',99000,9000,'2025-07-21 07:48:06','2025-07-21 09:51:41','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(12,1,22,2,'Completed',181497.799999999988,16499.7999999999992,'2025-07-21 09:19:24','2025-07-21 09:23:40','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(13,1,22,2,'Completed',0,0,'2025-07-21 09:54:49','2025-07-22 02:32:34','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(14,1,22,6,'Completed',0,0,'2025-07-21 09:57:55','2025-07-22 02:32:32','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(15,1,17,3,'Cancelled',66000,6000,'2025-07-21 09:59:35','2025-07-21 10:22:36','dine_in','fixed',20000,20000,NULL,0,80000,2,0);
INSERT INTO orders VALUES(16,1,22,4,'Cancelled',0,0,'2025-07-21 10:07:33','2025-07-21 10:22:28','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(17,1,1,5,'Cancelled',0,0,'2025-07-21 10:07:50','2025-07-21 10:22:00','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(18,1,22,7,'Cancelled',0,0,'2025-07-21 10:08:37','2025-07-21 10:21:23','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(19,1,22,8,'Cancelled',0,0,'2025-07-21 10:10:55','2025-07-21 10:21:09','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(20,1,22,9,'Cancelled',0,0,'2025-07-21 10:17:37','2025-07-21 10:20:54','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(21,1,22,1,'Cancelled',0,0,'2025-07-21 10:20:20','2025-07-21 10:20:44','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(22,1,22,1,'Completed',0,0,'2025-07-22 02:04:55','2025-07-22 02:32:25','dine_in','none',0,0,NULL,0,0,2,0);
INSERT INTO orders VALUES(23,1,11,3,'Completed',74800,6800,'2025-07-22 02:13:36','2025-07-22 02:33:00','dine_in','none',0,0,NULL,0,68000,2,0);
INSERT INTO orders VALUES(24,1,22,4,'Completed',82500,7500,'2025-07-22 02:22:28','2025-07-22 02:24:05','dine_in','none',0,0,NULL,0,75000,2,0);
INSERT INTO orders VALUES(25,1,4,1,'Paid',93500,8500,'2025-07-22 02:33:27','2025-07-22 02:33:52','dine_in','none',0,0,NULL,0,85000,2,0);
INSERT INTO orders VALUES(26,1,22,1,'Paid',44000,4000,'2025-07-22 02:42:47','2025-07-22 02:43:09','dine_in','none',0,0,NULL,0,40000,2,0);
INSERT INTO orders VALUES(27,1,3,1,'Paid',176000,16000,'2025-07-22 02:44:00','2025-07-22 02:44:24','dine_in','none',0,0,NULL,0,160000,2,0);
INSERT INTO orders VALUES(28,1,7,2,'Paid',55000,5000,'2025-07-22 03:20:54','2025-07-22 03:59:28','dine_in','none',0,0,NULL,0,50000,3,0);
INSERT INTO orders VALUES(29,1,11,1,'Paid',55000,5000,'2025-07-22 04:21:49','2025-07-22 04:22:01','take_away','none',0,0,NULL,0,50000,2,0);
INSERT INTO orders VALUES(30,1,11,4,'Completed',59400,5400,'2025-07-22 04:41:35','2025-07-22 04:41:56','take_away','percentage',10,6000,NULL,0,60000,4,0);
INSERT INTO orders VALUES(31,1,22,5,'Paid',107800,9800,'2025-07-22 08:36:14','2025-07-22 09:06:58','dine_in','none',0,22000,NULL,0,120000,2,0);
INSERT INTO orders VALUES(32,1,22,2,'Paid',0,0,'2025-07-22 09:16:41','2025-07-22 09:36:52','dine_in','none',0,90000,NULL,0,80000,2,0);
INSERT INTO orders VALUES(33,1,11,4,'Paid',121000,11000,'2025-07-22 09:56:29','2025-07-22 09:57:36','dine_in','none',0,10000,NULL,0,120000,2,0);
INSERT INTO orders VALUES(34,1,21,3,'Paid',110000,10000,'2025-07-22 10:00:55','2025-07-22 10:01:53','dine_in','none',0,50000,NULL,0,150000,2,0);
INSERT INTO orders VALUES(35,1,4,6,'Paid',330000,30000,'2025-07-22 10:06:13','2025-07-22 10:07:27','dine_in','none',0,0,NULL,0,300000,1,0);
INSERT INTO orders VALUES(36,1,22,11,'Paid',143000,13000,'2025-07-22 10:09:02','2025-07-22 10:11:53','dine_in','none',0,120000,NULL,0,250000,2,0);
CREATE TABLE IF NOT EXISTS "orders_temp" ("id" integer primary key  not null, "user_id" integer not null, "customer_id" integer not null, "table_id" integer not null, "status" varchar check ("status" in ('Pending', 'Completed', 'Cancelled', 'Paid')) not null default 'Pending', "order_type" varchar check ("order_type" in ('dine_in', 'take_away')) not null default 'dine_in', "total_amount" numeric not null default '0', "tax_amount" numeric not null default '0', "discount_type" varchar check ("discount_type" in ('none', 'percentage', 'fixed', 'coupon', 'points')) not null default 'none', "discount_value" numeric not null default '0', "discount_amount" numeric not null default '0', "coupon_code" varchar, "points_used" integer not null default '0', "subtotal_amount" numeric not null default '0', "created_at" datetime, "updated_at" datetime, foreign key("user_id") references "users"("id") on delete cascade, foreign key("customer_id") references "customers"("id") on delete cascade, foreign key("table_id") references "table_layouts"("id") on delete cascade);
CREATE TABLE IF NOT EXISTS "coupons" ("id" integer primary key  not null, "code" varchar not null, "name" varchar not null, "description" text, "type" varchar check ("type" in ('percentage', 'fixed_amount')) not null, "value" numeric not null, "minimum_amount" numeric not null default '0', "usage_limit" integer, "used_count" integer not null default '0', "is_active" tinyint(1) not null default '1', "valid_from" datetime not null, "valid_until" datetime not null, "created_at" datetime, "updated_at" datetime);
CREATE TABLE IF NOT EXISTS "order_settings" ("id" integer primary key  not null, "key" varchar not null, "value" text not null, "type" varchar not null default 'string', "category" varchar not null default 'general', "label" varchar not null, "description" text, "is_active" tinyint(1) not null default '1', "created_at" datetime, "updated_at" datetime);
INSERT INTO order_settings VALUES(1,'tax_rate','0.10','number','tax','Tax Rate (%)','Default tax rate applied to orders (as decimal, e.g., 0.10 for 10%)',1,'2025-07-22 04:11:20','2025-07-22 04:11:20');
INSERT INTO order_settings VALUES(2,'service_charge_rate','0.00','number','service','Service Charge Rate (%)','Service charge rate applied to orders (as decimal, e.g., 0.05 for 5%)',1,'2025-07-22 04:11:20','2025-07-22 04:11:20');
INSERT INTO order_settings VALUES(3,'auto_apply_tax','true','boolean','tax','Auto Apply Tax','Automatically apply tax to all orders',1,'2025-07-22 04:11:20','2025-07-22 04:11:20');
INSERT INTO order_settings VALUES(4,'auto_apply_service_charge','false','boolean','service','Auto Apply Service Charge','Automatically apply service charge to all orders',1,'2025-07-22 04:11:20','2025-07-22 04:11:20');
INSERT INTO order_settings VALUES(5,'max_discount_percentage','50','number','discount','Maximum Discount Percentage','Maximum percentage discount allowed on orders',1,'2025-07-22 04:11:20','2025-07-22 04:11:20');
INSERT INTO order_settings VALUES(6,'currency_symbol','$','string','general','Currency Symbol','Currency symbol displayed in the system',1,'2025-07-22 04:11:20','2025-07-22 04:11:20');
INSERT INTO order_settings VALUES(7,'receipt_footer_text','Thank you for dining with us!','string','general','Receipt Footer Text','Text displayed at the bottom of receipts',1,'2025-07-22 04:11:20','2025-07-22 04:11:20');
INSERT INTO order_settings VALUES(8,'order_timeout_minutes','30','number','general','Order Timeout (Minutes)','Minutes after which orders are considered overdue',1,'2025-07-22 04:11:20','2025-07-22 04:11:20');
CREATE TABLE IF NOT EXISTS "order_discounts" ("id" integer primary key  not null, "order_id" integer not null, "type" varchar not null, "value" numeric not null, "amount" numeric not null, "coupon_code" varchar, "points_used" integer, "description" varchar, "is_active" tinyint(1) not null default '1', "created_at" datetime, "updated_at" datetime, foreign key("order_id") references "orders"("id") on delete cascade);
INSERT INTO order_discounts VALUES(1,31,'fixed',10000,10000,NULL,NULL,'$10000.00 Off',1,'2025-07-22 08:53:11','2025-07-22 08:53:11');
INSERT INTO order_discounts VALUES(3,31,'percentage',10,12000,NULL,NULL,'10% Discount',1,'2025-07-22 08:53:45','2025-07-22 08:53:45');
INSERT INTO order_discounts VALUES(4,32,'fixed',10000,10000,NULL,NULL,'$10000.00 Off',1,'2025-07-22 09:31:01','2025-07-22 09:31:01');
INSERT INTO order_discounts VALUES(6,32,'percentage',10,8000,NULL,NULL,'10% Discount',1,'2025-07-22 09:31:54','2025-07-22 09:31:54');
INSERT INTO order_discounts VALUES(7,32,'percentage',90,72000,NULL,NULL,'90% Discount',1,'2025-07-22 09:36:29','2025-07-22 09:36:29');
INSERT INTO order_discounts VALUES(8,33,'fixed',10000,10000,NULL,NULL,'$10000.00 Off',1,'2025-07-22 09:56:30','2025-07-22 09:56:30');
INSERT INTO order_discounts VALUES(9,34,'fixed',50000,50000,NULL,NULL,'$50000.00 Off',1,'2025-07-22 10:01:17','2025-07-22 10:01:17');
INSERT INTO order_discounts VALUES(10,36,'fixed',120000,120000,NULL,NULL,'$120000.00 Off',1,'2025-07-22 10:09:23','2025-07-22 10:09:23');
DELETE FROM sqlite_sequence;
INSERT INTO sqlite_sequence VALUES('migrations',30);
INSERT INTO sqlite_sequence VALUES('orders',36);
INSERT INTO sqlite_sequence VALUES('users',2);
INSERT INTO sqlite_sequence VALUES('menu_categories',5);
INSERT INTO sqlite_sequence VALUES('menu_items',17);
INSERT INTO sqlite_sequence VALUES('table_layouts',11);
INSERT INTO sqlite_sequence VALUES('order_items',56);
INSERT INTO sqlite_sequence VALUES('payments',20);
INSERT INTO sqlite_sequence VALUES('customers',22);
INSERT INTO sqlite_sequence VALUES('order_settings',8);
INSERT INTO sqlite_sequence VALUES('order_discounts',10);
CREATE UNIQUE INDEX "users_email_unique" on "users" ("email");
CREATE INDEX "sessions_user_id_index" on "sessions" ("user_id");
CREATE INDEX "sessions_last_activity_index" on "sessions" ("last_activity");
CREATE INDEX "jobs_queue_index" on "jobs" ("queue");
CREATE UNIQUE INDEX "failed_jobs_uuid_unique" on "failed_jobs" ("uuid");
CREATE UNIQUE INDEX "users_username_unique" on "users" ("username");
CREATE UNIQUE INDEX "coupons_code_unique" on "coupons" ("code");
CREATE UNIQUE INDEX "order_settings_key_unique" on "order_settings" ("key");
CREATE INDEX "order_discounts_order_id_is_active_index" on "order_discounts" ("order_id", "is_active");
COMMIT;
