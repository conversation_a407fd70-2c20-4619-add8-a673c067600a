CREATE TABLE IF NOT EXISTS "migrations"(
  "id" integer primary key autoincrement not null,
  "migration" varchar not null,
  "batch" integer not null
);
CREATE TABLE IF NOT EXISTS "cache"(
  "key" varchar not null,
  "value" text not null,
  "expiration" integer not null,
  primary key("key")
);
CREATE TABLE IF NOT EXISTS "cache_locks"(
  "key" varchar not null,
  "owner" varchar not null,
  "expiration" integer not null,
  primary key("key")
);
CREATE TABLE IF NOT EXISTS "jobs"(
  "id" integer primary key autoincrement not null,
  "queue" varchar not null,
  "payload" text not null,
  "attempts" integer not null,
  "reserved_at" integer,
  "available_at" integer not null,
  "created_at" integer not null
);
CREATE INDEX "jobs_queue_index" on "jobs"("queue");
CREATE TABLE IF NOT EXISTS "job_batches"(
  "id" varchar not null,
  "name" varchar not null,
  "total_jobs" integer not null,
  "pending_jobs" integer not null,
  "failed_jobs" integer not null,
  "failed_job_ids" text not null,
  "options" text,
  "cancelled_at" integer,
  "created_at" integer not null,
  "finished_at" integer,
  primary key("id")
);
CREATE TABLE IF NOT EXISTS "failed_jobs"(
  "id" integer primary key autoincrement not null,
  "uuid" varchar not null,
  "connection" text not null,
  "queue" text not null,
  "payload" text not null,
  "exception" text not null,
  "failed_at" datetime not null default CURRENT_TIMESTAMP
);
CREATE UNIQUE INDEX "failed_jobs_uuid_unique" on "failed_jobs"("uuid");
CREATE TABLE IF NOT EXISTS "customers"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null default 'Walk-in',
  "phone" varchar,
  "created_at" datetime,
  "updated_at" datetime,
  "email" varchar,
  "address" text,
  "points" integer not null default '0',
  "api_id" varchar
);
CREATE TABLE IF NOT EXISTS "menu_categories"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE TABLE IF NOT EXISTS "menu_items"(
  "id" integer primary key autoincrement not null,
  "category_id" integer not null,
  "name" varchar not null,
  "description" text,
  "price" numeric not null,
  "image_path" varchar,
  "created_at" datetime,
  "updated_at" datetime,
  "api_id" varchar,
  "is_available" tinyint(1) not null default '1',
  foreign key("category_id") references "menu_categories"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "table_layouts"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "status" varchar check("status" in('Available', 'Occupied')) not null default 'Available',
  "created_at" datetime,
  "updated_at" datetime
);
CREATE TABLE IF NOT EXISTS "order_items"(
  "id" integer primary key autoincrement not null,
  "order_id" integer not null,
  "menu_item_id" integer not null,
  "quantity" integer not null,
  "price_at_time" numeric not null,
  "notes" text,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("order_id") references "orders"("id") on delete cascade,
  foreign key("menu_item_id") references "menu_items"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "payments"(
  "id" integer primary key autoincrement not null,
  "order_id" integer not null,
  "method" varchar check("method" in('Cash', 'Card', 'QRIS')) not null,
  "amount_paid" numeric not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("order_id") references "orders"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "ingredients"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "unit" varchar not null,
  "stock_level" numeric not null default '0',
  "created_at" datetime,
  "updated_at" datetime
);
CREATE TABLE IF NOT EXISTS "suppliers"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "contact_info" text,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE TABLE IF NOT EXISTS "purchases"(
  "id" integer primary key autoincrement not null,
  "supplier_id" integer not null,
  "purchase_date" datetime not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("supplier_id") references "suppliers"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "purchase_items"(
  "purchase_id" integer not null,
  "ingredient_id" integer not null,
  "quantity" numeric not null,
  "unit_price" numeric not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("purchase_id") references "purchases"("id") on delete cascade,
  foreign key("ingredient_id") references "ingredients"("id") on delete cascade,
  primary key("purchase_id", "ingredient_id")
);
CREATE TABLE IF NOT EXISTS "recipes"(
  "menu_item_id" integer not null,
  "ingredient_id" integer not null,
  "quantity_needed" numeric not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("menu_item_id") references "menu_items"("id") on delete cascade,
  foreign key("ingredient_id") references "ingredients"("id") on delete cascade,
  primary key("menu_item_id", "ingredient_id")
);
CREATE TABLE IF NOT EXISTS "orders_temp"(
  "id" integer primary key autoincrement not null,
  "user_id" integer not null,
  "customer_id" integer not null,
  "table_id" integer,
  "status" varchar check("status" in('Pending', 'Completed', 'Cancelled', 'Paid')) not null default 'Pending',
  "order_type" varchar check("order_type" in('dine_in', 'take_away')) not null default 'dine_in',
  "total_amount" numeric not null default '0',
  "tax_amount" numeric not null default '0',
  "discount_type" varchar check("discount_type" in('none', 'percentage', 'fixed', 'coupon', 'points')) not null default 'none',
  "discount_value" numeric not null default '0',
  "discount_amount" numeric not null default '0',
  "coupon_code" varchar,
  "points_used" integer not null default '0',
  "subtotal_amount" numeric not null default '0',
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("user_id") references "users"("id") on delete cascade,
  foreign key("customer_id") references "customers"("id") on delete cascade,
  foreign key("table_id") references "table_layouts"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "coupons"(
  "id" integer primary key autoincrement not null,
  "code" varchar not null,
  "name" varchar not null,
  "description" text,
  "type" varchar check("type" in('percentage', 'fixed_amount')) not null,
  "value" numeric not null,
  "minimum_amount" numeric not null default '0',
  "usage_limit" integer,
  "used_count" integer not null default '0',
  "is_active" tinyint(1) not null default '1',
  "valid_from" datetime not null,
  "valid_until" datetime not null,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE UNIQUE INDEX "coupons_code_unique" on "coupons"("code");
CREATE TABLE IF NOT EXISTS "order_settings"(
  "id" integer primary key autoincrement not null,
  "key" varchar not null,
  "value" text not null,
  "type" varchar not null default 'string',
  "category" varchar not null default 'general',
  "label" varchar not null,
  "description" text,
  "is_active" tinyint(1) not null default '1',
  "created_at" datetime,
  "updated_at" datetime
);
CREATE UNIQUE INDEX "order_settings_key_unique" on "order_settings"("key");
CREATE TABLE IF NOT EXISTS "order_discounts"(
  "id" integer primary key autoincrement not null,
  "order_id" integer not null,
  "type" varchar not null,
  "value" numeric not null,
  "amount" numeric not null,
  "coupon_code" varchar,
  "points_used" integer,
  "description" varchar,
  "is_active" tinyint(1) not null default '1',
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("order_id") references "orders"("id") on delete cascade
);
CREATE INDEX "order_discounts_order_id_is_active_index" on "order_discounts"(
  "order_id",
  "is_active"
);
CREATE INDEX "menu_items_api_id_index" on "menu_items"("api_id");
CREATE INDEX "customers_api_id_index" on "customers"("api_id");
CREATE TABLE IF NOT EXISTS "orders"(
  "id" integer primary key autoincrement not null,
  "user_id" integer,
  "customer_id" integer not null,
  "table_id" integer,
  "status" varchar not null default('Pending'),
  "total_amount" numeric not null default('0'),
  "tax_amount" numeric not null default('0'),
  "created_at" datetime,
  "updated_at" datetime,
  "order_type" varchar not null default('dine_in'),
  "discount_type" varchar not null default('none'),
  "discount_value" numeric not null default('0'),
  "discount_amount" numeric not null default('0'),
  "coupon_code" varchar,
  "points_used" integer not null default('0'),
  "subtotal_amount" numeric not null default('0'),
  "party_size" integer not null default('2'),
  "service_charge_amount" numeric not null default('0'),
  "api_user_id" varchar,
  "cashier_name" varchar,
  "cashier_email" varchar,
  "isSync" tinyint(1) not null default '0',
  "synced_at" datetime,
  "sync_error" text,
  foreign key("customer_id") references customers("id") on delete cascade on update no action,
  foreign key("table_id") references table_layouts("id") on delete cascade on update no action
);
CREATE TABLE orders_new(
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NULL,
  api_user_id VARCHAR(255) NULL,
  cashier_name VARCHAR(255) NULL,
  cashier_email VARCHAR(255) NULL,
  customer_id INTEGER NOT NULL,
  table_id INTEGER NULL,
  status VARCHAR(255) NOT NULL DEFAULT "Pending",
  order_type VARCHAR(255) NOT NULL DEFAULT "dine_in",
  party_size INTEGER NOT NULL DEFAULT 2,
  subtotal DECIMAL(10,2) DEFAULT 0,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  service_charge_amount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) DEFAULT 0,
  subtotal_amount DECIMAL(10,2) DEFAULT 0,
  paid_status VARCHAR(255) NOT NULL DEFAULT "unpaid",
  created_at TIMESTAMP NULL,
  updated_at TIMESTAMP NULL,
  FOREIGN KEY(customer_id) REFERENCES customers(id),
  FOREIGN KEY(table_id) REFERENCES table_layouts(id)
);
CREATE TABLE IF NOT EXISTS "sessions"(
  "id" varchar not null,
  "user_id" integer,
  "ip_address" varchar,
  "user_agent" text,
  "payload" text not null,
  "last_activity" integer not null,
  primary key("id")
);
CREATE INDEX "sessions_user_id_index" on "sessions"("user_id");
CREATE INDEX "sessions_last_activity_index" on "sessions"("last_activity");

INSERT INTO migrations VALUES(1,'0001_01_01_000000_create_users_table',1);
INSERT INTO migrations VALUES(2,'0001_01_01_000001_create_cache_table',1);
INSERT INTO migrations VALUES(3,'0001_01_01_000002_create_jobs_table',1);
INSERT INTO migrations VALUES(4,'2025_07_18_043034_modify_users_table_for_pos',1);
INSERT INTO migrations VALUES(5,'2025_07_18_043046_create_customers_table',1);
INSERT INTO migrations VALUES(6,'2025_07_18_043055_create_menu_categories_table',1);
INSERT INTO migrations VALUES(7,'2025_07_18_043124_create_menu_items_table',1);
INSERT INTO migrations VALUES(8,'2025_07_18_043134_create_table_layouts_table',1);
INSERT INTO migrations VALUES(9,'2025_07_18_043140_create_orders_table',1);
INSERT INTO migrations VALUES(10,'2025_07_18_043146_create_order_items_table',1);
INSERT INTO migrations VALUES(11,'2025_07_18_043159_create_payments_table',1);
INSERT INTO migrations VALUES(12,'2025_07_18_043207_create_ingredients_table',1);
INSERT INTO migrations VALUES(13,'2025_07_18_043214_create_suppliers_table',1);
INSERT INTO migrations VALUES(14,'2025_07_18_043220_create_purchases_table',1);
INSERT INTO migrations VALUES(15,'2025_07_18_043227_create_purchase_items_table',1);
INSERT INTO migrations VALUES(16,'2025_07_18_043236_create_recipes_table',1);
INSERT INTO migrations VALUES(17,'2025_07_18_064337_update_orders_table_add_paid_status',1);
INSERT INTO migrations VALUES(18,'2025_07_21_022947_add_email_and_address_to_customers_table',2);
INSERT INTO migrations VALUES(19,'2025_07_21_030114_add_order_type_to_orders_table',3);
INSERT INTO migrations VALUES(23,'2025_07_21_065152_add_discount_fields_to_orders_table',4);
INSERT INTO migrations VALUES(24,'2025_07_21_065435_create_coupons_table',4);
INSERT INTO migrations VALUES(25,'2025_07_21_065534_add_points_to_customers_table',4);
INSERT INTO migrations VALUES(26,'2025_07_22_025234_add_party_size_to_orders_table',5);
INSERT INTO migrations VALUES(27,'2025_07_22_041036_create_order_settings_table',6);
INSERT INTO migrations VALUES(28,'2025_07_22_041549_add_service_charge_to_orders_table',7);
INSERT INTO migrations VALUES(29,'2025_07_22_041614_add_service_charge_to_orders_table',7);
INSERT INTO migrations VALUES(30,'2025_07_22_044436_create_order_discounts_table',8);
INSERT INTO migrations VALUES(31,'2025_07_31_050000_add_api_id_to_menu_items_table',9);
INSERT INTO migrations VALUES(32,'2025_07_31_093748_add_api_id_to_menu_items_table',9);
INSERT INTO migrations VALUES(33,'2025_08_01_041431_add_api_id_to_customers_table',10);
INSERT INTO migrations VALUES(34,'2025_08_06_083349_remove_user_foreign_key_from_orders_table',11);
INSERT INTO migrations VALUES(35,'2025_08_06_083419_add_api_user_id_to_orders_table',11);
INSERT INTO migrations VALUES(36,'2025_08_06_084500_add_cashier_info_to_orders_table',12);
INSERT INTO migrations VALUES(37,'2025_08_06_084843_add_cashier_info_to_orders_table',12);
INSERT INTO migrations VALUES(38,'2025_08_06_134141_remove_user_id_foreign_key_from_orders',13);
INSERT INTO migrations VALUES(39,'2025_08_06_135600_add_is_available_to_menu_items_table',14);
INSERT INTO migrations VALUES(40,'2025_08_06_135727_add_is_available_to_menu_items_table',14);
INSERT INTO migrations VALUES(41,'2025_08_07_000000_drop_user_related_tables',15);
INSERT INTO migrations VALUES(42,'2025_08_07_000001_recreate_sessions_table',16);
INSERT INTO migrations VALUES(43,'2025_08_08_081520_add_is_sync_to_orders_table',17);
