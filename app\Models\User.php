<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'username',
        'email',
        'password',
        'role',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'password' => 'hashed',
        ];
    }

    /**
     * Get the orders created by this user.
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is cashier.
     */
    public function isCashier(): bool
    {
        return $this->role === 'Cashier';
    }

    /**
     * Check if user can access admin features (admin, keptok, karyawan roles with proper jabatan).
     * For database users, only admin role is allowed since they don't have jabatan field.
     */
    public function canAccessAdminFeatures(): bool
    {
        $role = strtolower($this->role ?? '');

        // For database users, only admin role is allowed
        // (database users don't have jabatan field from API)
        return $role === 'admin';
    }

    /**
     * Check if user can access menu management features (Categories and Menu Items).
     * For database users, only admin role can access menu management.
     */
    public function canAccessMenuManagement(): bool
    {
        $role = strtolower($this->role ?? '');

        // For database users, only admin role can access menu management
        return $role === 'admin';
    }
}
