<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class MenuItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'api_id',
        'name',
        'sku',
        'barcode',
        'description',
        'price',
        'cost_price',
        'category_id',
        'stock_quantity',
        'is_active',
        'is_food_item',
        'image',
        'is_synced_to_api',
        'last_api_sync_at',
    ];

    protected function casts(): array
    {
        return [
            'price' => 'decimal:2',
            'cost_price' => 'decimal:2',
            'is_active' => 'boolean',
            'is_food_item' => 'boolean',
            'is_synced_to_api' => 'boolean',
            'stock_quantity' => 'integer',
            'last_api_sync_at' => 'datetime',
        ];
    }

    /**
     * Get the category that owns the menu item.
     */
    public function category()
    {
        return $this->belongsTo(MenuCategory::class, 'category_id');
    }

    /**
     * Get the order items for this menu item.
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Check if the menu item is synced to the API
     */
    public function isSyncedToApi(): bool
    {
        return !empty($this->api_id) && $this->is_synced_to_api;
    }

    /**
     * Mark the menu item as synced to API
     */
    public function markAsSyncedToApi(string $apiId = null): void
    {
        $this->update([
            'api_id' => $apiId ?? $this->api_id,
            'is_synced_to_api' => true,
            'last_api_sync_at' => now(),
        ]);
    }

    /**
     * Mark the menu item as not synced to API
     */
    public function markAsNotSyncedToApi(): void
    {
        $this->update([
            'is_synced_to_api' => false,
            'last_api_sync_at' => null,
        ]);
    }

    /**
     * Get the ingredients for this menu item (recipes).
     */
    public function ingredients()
    {
        return $this->belongsToMany(Ingredient::class, 'recipes')
                    ->withPivot('quantity_needed')
                    ->withTimestamps();
    }

    /**
     * Backward compatibility: Get image_path attribute
     */
    public function getImagePathAttribute()
    {
        return $this->image;
    }

    /**
     * Backward compatibility: Set image_path attribute
     */
    public function setImagePathAttribute($value)
    {
        $this->attributes['image'] = $value;
    }

    /**
     * Backward compatibility: Get is_available attribute
     */
    public function getIsAvailableAttribute()
    {
        return $this->is_active;
    }

    /**
     * Backward compatibility: Set is_available attribute
     */
    public function setIsAvailableAttribute($value)
    {
        $this->attributes['is_active'] = $value;
    }

    /**
     * Check if item is low in stock
     */
    public function isLowStock($threshold = 10): bool
    {
        return $this->stock_quantity <= $threshold;
    }

    /**
     * Check if item is out of stock
     */
    public function isOutOfStock(): bool
    {
        return $this->stock_quantity <= 0;
    }

    /**
     * Scope to get only active items
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only food items
     */
    public function scopeFoodItems($query)
    {
        return $query->where('is_food_item', true);
    }

    /**
     * Scope to get items in stock
     */
    public function scopeInStock($query)
    {
        return $query->where('stock_quantity', '>', 0);
    }
}
