/**
 * JavaScript Examples for POS Sync Products API
 * Endpoint: https://viera-filament.test/api/pos/sync/products
 * Token: 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35
 */

// =============================================================================
// 1. Using Fetch API (Modern JavaScript)
// =============================================================================

async function fetchProductsWithFetch() {
    const url = 'https://viera-filament.test/api/pos/sync/products';
    const token = '5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35';
    
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            // Note: In browser, you might need to handle CORS
        });
        
        // Check if response is ok
        if (!response.ok) {
            throw new Error(`HTTP Error: ${response.status} ${response.statusText}`);
        }
        
        // Parse JSON
        const data = await response.json();
        
        return {
            success: true,
            data: data,
            status: response.status,
            statusText: response.statusText
        };
        
    } catch (error) {
        return {
            success: false,
            error: error.message,
            type: error.name
        };
    }
}

// =============================================================================
// 2. Using XMLHttpRequest (Compatible with older browsers)
// =============================================================================

function fetchProductsWithXHR() {
    return new Promise((resolve, reject) => {
        const url = 'https://viera-filament.test/api/pos/sync/products';
        const token = '5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35';
        
        const xhr = new XMLHttpRequest();
        
        xhr.open('GET', url, true);
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
        xhr.setRequestHeader('Accept', 'application/json');
        xhr.setRequestHeader('Content-Type', 'application/json');
        
        xhr.timeout = 30000; // 30 seconds timeout
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === XMLHttpRequest.DONE) {
                if (xhr.status === 200) {
                    try {
                        const data = JSON.parse(xhr.responseText);
                        resolve({
                            success: true,
                            data: data,
                            status: xhr.status,
                            statusText: xhr.statusText
                        });
                    } catch (parseError) {
                        resolve({
                            success: false,
                            error: `JSON Parse Error: ${parseError.message}`,
                            rawResponse: xhr.responseText
                        });
                    }
                } else {
                    resolve({
                        success: false,
                        error: `HTTP Error: ${xhr.status} ${xhr.statusText}`,
                        status: xhr.status,
                        response: xhr.responseText
                    });
                }
            }
        };
        
        xhr.onerror = function() {
            resolve({
                success: false,
                error: 'Network Error: Request failed'
            });
        };
        
        xhr.ontimeout = function() {
            resolve({
                success: false,
                error: 'Timeout Error: Request timed out'
            });
        };
        
        xhr.send();
    });
}

// =============================================================================
// 3. Using Axios (if available)
// =============================================================================

async function fetchProductsWithAxios() {
    // Requires: npm install axios or include axios via CDN
    
    const url = 'https://viera-filament.test/api/pos/sync/products';
    const token = '5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35';
    
    try {
        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            timeout: 30000, // 30 seconds
        });
        
        return {
            success: true,
            data: response.data,
            status: response.status,
            statusText: response.statusText
        };
        
    } catch (error) {
        if (error.response) {
            // Server responded with error status
            return {
                success: false,
                error: `HTTP Error: ${error.response.status} ${error.response.statusText}`,
                status: error.response.status,
                data: error.response.data
            };
        } else if (error.request) {
            // Request was made but no response received
            return {
                success: false,
                error: 'Network Error: No response received',
                request: error.request
            };
        } else {
            // Something else happened
            return {
                success: false,
                error: `Request Error: ${error.message}`
            };
        }
    }
}

// =============================================================================
// Usage Examples
// =============================================================================

// Example 1: Using Fetch API
console.log('=== Testing with Fetch API ===');
fetchProductsWithFetch()
    .then(result => {
        if (result.success) {
            console.log('✅ Success!', {
                status: result.status,
                productsCount: result.data.data?.length || 0
            });
            console.log('📄 Response preview:', JSON.stringify(result.data, null, 2));
        } else {
            console.error('❌ Error:', result.error);
        }
    })
    .catch(error => {
        console.error('❌ Unexpected error:', error);
    });

// Example 2: Using XMLHttpRequest
console.log('\n=== Testing with XMLHttpRequest ===');
fetchProductsWithXHR()
    .then(result => {
        if (result.success) {
            console.log('✅ Success!', {
                status: result.status,
                productsCount: result.data.data?.length || 0
            });
        } else {
            console.error('❌ Error:', result.error);
        }
    });

// Example 3: Using Axios (uncomment if axios is available)
/*
console.log('\n=== Testing with Axios ===');
fetchProductsWithAxios()
    .then(result => {
        if (result.success) {
            console.log('✅ Success!', {
                status: result.status,
                productsCount: result.data.data?.length || 0
            });
        } else {
            console.error('❌ Error:', result.error);
        }
    });
*/

// =============================================================================
// Helper function to display products nicely
// =============================================================================

function displayProducts(products) {
    if (!products || !Array.isArray(products)) {
        console.log('No products data available');
        return;
    }
    
    console.log(`\n📦 Found ${products.length} products:`);
    products.forEach((product, index) => {
        console.log(`${index + 1}. ${product.name || 'Unnamed Product'}`);
        console.log(`   Price: $${product.price || 'N/A'}`);
        console.log(`   Category: ${product.category?.name || 'N/A'}`);
        console.log(`   Status: ${product.is_active ? 'Active' : 'Inactive'}`);
        console.log('   ---');
    });
}

// Example usage with display function
fetchProductsWithFetch()
    .then(result => {
        if (result.success && result.data.data) {
            displayProducts(result.data.data);
        }
    });
