<?php

namespace App\Services;

use App\Models\MenuItem;
use App\Models\MenuCategory;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class ProductSyncService
{
    private string $apiBaseUrl;
    private int $timeout;

    public function __construct()
    {
        $this->apiBaseUrl = config('pos.api_base_url', 'http://viera-filament.test/api/pos');
        $this->timeout = config('pos.api_timeout', 30);
    }

    /**
     * Sync a newly created product to the API
     *
     * @param MenuItem $menuItem
     * @return array
     */
    public function syncProductToApi(MenuItem $menuItem): array
    {
        try {
            // First check if we have network connectivity
            if (!$this->isNetworkAvailable()) {
                return [
                    'success' => false,
                    'error' => 'No network connection available. Product created locally but not synced to API.',
                    'local_only' => true
                ];
            }

            // Get authentication token
            $token = $this->getAuthToken();
            if (!$token) {
                return [
                    'success' => false,
                    'error' => 'Failed to authenticate with API. Product created locally but not synced.',
                    'local_only' => true
                ];
            }

            // Prepare product data for API
            $productData = $this->prepareProductData($menuItem);

            // Send to API
            $response = Http::timeout($this->timeout)
                ->withToken($token)
                ->post("{$this->apiBaseUrl}/sync/products", $productData);

            if ($response->successful()) {
                $responseData = $response->json();

                // Update local product with API ID and mark as synced
                $apiId = $responseData['data']['id'] ?? null;
                if ($apiId) {
                    // Only update api_id and sync status, don't change category_id
                    $menuItem->update([
                        'api_id' => $apiId,
                        'is_synced_to_api' => true,
                        'last_api_sync_at' => now(),
                    ]);
                } else {
                    // Even if no API ID is returned, mark as synced if the request was successful
                    $menuItem->update([
                        'is_synced_to_api' => true,
                        'last_api_sync_at' => now(),
                    ]);
                }

                Log::info('ProductSyncService: Product synced to API successfully', [
                    'local_id' => $menuItem->id,
                    'api_id' => $apiId,
                    'product_name' => $menuItem->name,
                    'local_category' => $menuItem->category->name ?? 'Unknown'
                ]);

                return [
                    'success' => true,
                    'message' => 'Product created and synced to API successfully',
                    'api_id' => $apiId
                ];
            } else {
                Log::error('ProductSyncService: API sync failed', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'product_id' => $menuItem->id
                ]);

                return [
                    'success' => false,
                    'error' => 'Failed to sync product to API: ' . $response->body(),
                    'local_only' => true
                ];
            }

        } catch (\Exception $e) {
            Log::error('ProductSyncService: Exception during API sync', [
                'error' => $e->getMessage(),
                'product_id' => $menuItem->id
            ]);

            return [
                'success' => false,
                'error' => 'Error syncing product to API: ' . $e->getMessage(),
                'local_only' => true
            ];
        }
    }

    /**
     * Check if network is available for API calls
     *
     * @return bool
     */
    public function isNetworkAvailable(): bool
    {
        // Check if network validation is disabled in config
        if (config('pos.disable_network_validation', false)) {
            Log::info('ProductSyncService: Network validation disabled by config');
            return true;
        }

        try {
            // For development, be more lenient with network checks
            if (app()->environment('local')) {
                return $this->isNetworkAvailableForDevelopment();
            }

            // Production network check
            return $this->isNetworkAvailableForProduction();
        } catch (\Exception $e) {
            Log::warning('ProductSyncService: Network check failed', ['error' => $e->getMessage()]);

            // In development, allow creation even if network check fails
            if (app()->environment('local')) {
                return true;
            }

            return false;
        }
    }

    /**
     * Network check for development environment
     *
     * @return bool
     */
    private function isNetworkAvailableForDevelopment(): bool
    {
        // In development, just check if we can reach any external service
        $testUrls = [
            'http://httpbin.org/status/200',
            'http://www.google.com',
            'https://jsonplaceholder.typicode.com/posts/1'
        ];

        foreach ($testUrls as $url) {
            try {
                $response = Http::timeout(2)->get($url);
                if ($response->successful()) {
                    Log::info('ProductSyncService: Development network check passed', ['url' => $url]);
                    return true;
                }
            } catch (\Exception $e) {
                continue;
            }
        }

        // If all external checks fail, still allow in development
        Log::warning('ProductSyncService: All network checks failed in development, but allowing creation');
        return true;
    }

    /**
     * Network check for production environment
     *
     * @return bool
     */
    private function isNetworkAvailableForProduction(): bool
    {
        try {
            // First try a simple connectivity test to a reliable endpoint
            $response = Http::timeout(3)
                ->get('https://www.google.com/favicon.ico');

            if ($response->successful()) {
                // If basic internet works, test API connectivity
                return $this->testApiConnectivity();
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Test API connectivity specifically
     *
     * @return bool
     */
    private function testApiConnectivity(): bool
    {
        try {
            // Try to access the API base URL or a known endpoint
            $testEndpoints = [
                $this->apiBaseUrl . '/status',
                $this->apiBaseUrl . '/health',
                $this->apiBaseUrl,
                'http://viera-filament.test/api/pos'
            ];

            foreach ($testEndpoints as $endpoint) {
                try {
                    $response = Http::timeout(3)->get($endpoint);
                    // Accept any response (even 404) as long as we can reach the server
                    if ($response->status() < 500) {
                        return true;
                    }
                } catch (\Exception $e) {
                    continue;
                }
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get authentication token for API calls
     *
     * @return string|null
     */
    private function getAuthToken(): ?string
    {
        // Check if we have a cached token
        $cachedToken = Cache::get('pos_api_token');
        if ($cachedToken) {
            return $cachedToken;
        }

        try {
            // Get credentials from config (use email, not username)
            $email = config('pos.api_email');
            $password = config('pos.api_password');
            $deviceName = config('pos.api_device_name', 'POS System');

            if (!$email || !$password) {
                Log::error('ProductSyncService: API credentials not configured');
                return null;
            }

            // Authenticate using the same method as PosApiService
            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])
            ->timeout($this->timeout)
            ->post("{$this->apiBaseUrl}/login", [
                'email' => $email,
                'password' => $password,
                'device_name' => $deviceName
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $token = $data['token'] ?? $data['access_token'] ?? null;

                if ($token) {
                    // Cache token for 1 hour
                    Cache::put('pos_api_token', $token, 3600);

                    Log::info('ProductSyncService: Authentication successful', [
                        'email' => $email,
                        'token_length' => strlen($token)
                    ]);

                    return $token;
                }
            }

            Log::error('ProductSyncService: Authentication failed', [
                'status' => $response->status(),
                'response' => $response->body(),
                'email' => $email
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('ProductSyncService: Authentication error', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Prepare product data for API submission
     *
     * @param MenuItem $menuItem
     * @return array
     */
    private function prepareProductData(MenuItem $menuItem): array
    {
        // Map local category to API category
        $apiCategoryId = $this->mapLocalCategoryToApi($menuItem->category);

        return [
            'name' => $menuItem->name,
            'sku' => $menuItem->sku,
            'barcode' => $menuItem->barcode,
            'description' => $menuItem->description,
            'price' => $menuItem->price,
            'cost_price' => $menuItem->cost_price,
            'category_id' => $apiCategoryId,
            'stock_quantity' => $menuItem->stock_quantity,
            'is_active' => $menuItem->is_active,
            'is_food_item' => $menuItem->is_food_item,
            'image' => $menuItem->image,
        ];
    }

    /**
     * Map local category to API category ID
     */
    private function mapLocalCategoryToApi($localCategory): ?int
    {
        if (!$localCategory) {
            return null;
        }

        // Try to get API categories from PosApiService
        try {
            $posApiService = app(\App\Services\PosApiService::class);
            $productsResult = $posApiService->fetchProducts();

            if ($productsResult['success'] && !empty($productsResult['data']['products'])) {
                // Look for matching category name
                foreach ($productsResult['data']['products'] as $product) {
                    if (isset($product['category']['name']) &&
                        strtolower($product['category']['name']) === strtolower($localCategory->name)) {
                        return $product['category']['id'];
                    }
                }

                // If no exact match, try to find a reasonable default
                $categoryName = strtolower($localCategory->name);
                foreach ($productsResult['data']['products'] as $product) {
                    $apiCategoryName = strtolower($product['category']['name']);

                    // Check for partial matches
                    if (str_contains($categoryName, 'makan') && str_contains($apiCategoryName, 'makan')) {
                        return $product['category']['id'];
                    }
                    if (str_contains($categoryName, 'minum') && str_contains($apiCategoryName, 'minum')) {
                        return $product['category']['id'];
                    }
                }

                // Last resort: use the first available category
                return $productsResult['data']['products'][0]['category']['id'];
            }
        } catch (\Exception $e) {
            Log::warning('ProductSyncService: Failed to map category', [
                'local_category' => $localCategory->name,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Test API connectivity
     *
     * @return array
     */
    public function testApiConnection(): array
    {
        try {
            $startTime = microtime(true);
            
            $response = Http::timeout(10)
                ->get("{$this->apiBaseUrl}/health-check");
            
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'message' => 'API connection successful',
                    'response_time' => $responseTime . 'ms',
                    'status_code' => $response->status()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'API connection failed',
                    'response_time' => $responseTime . 'ms',
                    'status_code' => $response->status(),
                    'error' => $response->body()
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'API connection error: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ];
        }
    }
}
