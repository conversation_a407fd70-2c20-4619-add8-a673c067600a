<?php

namespace App\Console\Commands;

use App\Services\ProductService;
use App\Services\PosApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;

class ConfigureApiMode extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:configure-api-mode {--test : Test the configuration without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Configure the POS system to use API data as primary source';

    private ProductService $productService;
    private PosApiService $posApiService;

    public function __construct(ProductService $productService, PosApiService $posApiService)
    {
        parent::__construct();
        $this->productService = $productService;
        $this->posApiService = $posApiService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $testMode = $this->option('test');
        
        if ($testMode) {
            $this->info('🧪 TEST MODE - No changes will be made');
        } else {
            $this->info('🔧 Configuring POS system to use API data...');
        }
        $this->newLine();

        // Check if .env file exists
        $envPath = base_path('.env');
        if (!File::exists($envPath)) {
            $this->error('❌ .env file not found. Please create one first.');
            return Command::FAILURE;
        }

        // Read current .env content
        $envContent = File::get($envPath);
        $newEnvContent = $envContent;

        // API Configuration to add/update
        $apiConfig = [
            'POS_API_BASE_URL' => 'http://viera-filament.test/api/pos',
            'POS_API_EMAIL' => '<EMAIL>',
            'POS_API_PASSWORD' => 'M4$ukaja',
            'POS_API_DEVICE_NAME' => 'Oppo',
            'POS_API_LOCATION_ID' => '',
            'POS_USE_LOCAL_FALLBACK' => 'false',
            'POS_TOKEN_CACHE_DURATION' => '3600',
            'POS_PRODUCT_CACHE_DURATION' => '300',
        ];

        $this->info('📝 Configuration to be applied:');
        foreach ($apiConfig as $key => $value) {
            $displayValue = $key === 'POS_API_PASSWORD' ? '***' : $value;
            $this->line("   {$key}={$displayValue}");
        }
        $this->newLine();

        if (!$testMode) {
            // Update or add each configuration
            foreach ($apiConfig as $key => $value) {
                if (strpos($newEnvContent, $key . '=') !== false) {
                    // Update existing
                    $newEnvContent = preg_replace(
                        '/^' . preg_quote($key, '/') . '=.*$/m',
                        $key . '=' . $value,
                        $newEnvContent
                    );
                } else {
                    // Add new
                    $newEnvContent .= "\n{$key}={$value}";
                }
            }

            // Write back to .env file
            File::put($envPath, $newEnvContent);
            $this->info('✅ .env file updated successfully');
        }

        // Test the configuration
        $this->newLine();
        $this->info('🧪 Testing API configuration...');

        // Temporarily set config for testing
        foreach ($apiConfig as $key => $value) {
            $configKey = 'pos.' . strtolower(str_replace('POS_', '', $key));
            $configKey = str_replace('_', '_', $configKey);
            
            if ($key === 'POS_API_BASE_URL') Config::set('pos.api_base_url', $value);
            elseif ($key === 'POS_API_EMAIL') Config::set('pos.api_email', $value);
            elseif ($key === 'POS_API_PASSWORD') Config::set('pos.api_password', $value);
            elseif ($key === 'POS_API_DEVICE_NAME') Config::set('pos.api_device_name', $value);
            elseif ($key === 'POS_API_LOCATION_ID') Config::set('pos.api_location_id', $value);
            elseif ($key === 'POS_USE_LOCAL_FALLBACK') Config::set('pos.use_local_fallback', $value === 'true');
        }

        // Clear caches
        $this->productService->clearCache();
        $this->posApiService->clearAuthToken();

        // Test authentication
        $authResult = $this->posApiService->authenticate();
        if ($authResult['success']) {
            $this->info('✅ API Authentication: SUCCESS');
        } else {
            $this->error('❌ API Authentication: FAILED');
            $this->error("   Error: {$authResult['error']}");
            return Command::FAILURE;
        }

        // Test product fetching
        $categories = $this->productService->getCategoriesWithItems();
        if ($categories->count() > 0) {
            $this->info('✅ Product Fetching: SUCCESS');
            $totalItems = $categories->sum(fn($cat) => $cat->menuItems->count());
            $this->info("   Found {$categories->count()} categories with {$totalItems} items");
            
            $this->newLine();
            $this->info('📋 Available Products:');
            foreach ($categories as $category) {
                $this->line("  📁 {$category->name} ({$category->menuItems->count()} items)");
                foreach ($category->menuItems as $item) {
                    $price = number_format($item->price, 0, ',', '.');
                    $this->line("    - {$item->name} (Rp {$price})");
                }
            }
        } else {
            $this->error('❌ Product Fetching: FAILED');
            return Command::FAILURE;
        }

        $this->newLine();
        if ($testMode) {
            $this->info('🎉 Test completed successfully! Configuration is valid.');
            $this->info('💡 Run without --test flag to apply the configuration.');
        } else {
            $this->info('🎉 POS system successfully configured to use API data!');
            $this->info('💡 Your POS system will now use products from the API.');
            $this->info('💡 You can access the POS interface and it will show the API products.');
        }

        return Command::SUCCESS;
    }
}
