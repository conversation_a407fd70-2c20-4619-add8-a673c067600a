<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Table Layout') }}
            </h2>
            @if(session('api_authenticated'))
                <a href="{{ route('tables.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Add New Table
                </a>
            @endif
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {{ session('error') }}
                        </div>
                    @endif

                    <!-- Table Grid Layout -->
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mb-8">
                        @forelse($tables as $table)
                            <div class="relative">
                                <div class="border-2 rounded-lg p-4 text-center cursor-pointer transition-all duration-200 hover:shadow-lg
                                    {{ $table->isAvailable() ? 'border-green-500 bg-green-50 hover:bg-green-100' : 'border-red-500 bg-red-50 hover:bg-red-100' }}"
                                    onclick="window.location.href='{{ route('tables.show', $table) }}'">
                                    
                                    <!-- Table Icon -->
                                    <div class="mb-2">
                                        <svg class="w-8 h-8 mx-auto {{ $table->isAvailable() ? 'text-green-600' : 'text-red-600' }}" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                        </svg>
                                    </div>
                                    
                                    <!-- Table Name -->
                                    <h3 class="font-semibold text-lg {{ $table->isAvailable() ? 'text-green-800' : 'text-red-800' }}">
                                        {{ $table->name }}
                                    </h3>
                                    
                                    <!-- Status -->
                                    <p class="text-sm {{ $table->isAvailable() ? 'text-green-600' : 'text-red-600' }}">
                                        {{ $table->status }}
                                    </p>
                                    
                                    <!-- Current Order Info -->
                                    @if($table->currentOrder)
                                        <p class="text-xs text-gray-600 mt-1">
                                            Order #{{ $table->currentOrder->id }}
                                        </p>
                                        <p class="text-xs text-gray-600">
                                            ${{ number_format($table->currentOrder->total_amount, 2) }}
                                        </p>
                                    @endif
                                </div>
                                
                                <!-- Admin Actions -->
                                @if(session('api_authenticated'))
                                    <div class="absolute top-2 right-2">
                                        <div class="flex space-x-1">
                                            <a href="{{ route('tables.edit', $table) }}" class="bg-yellow-500 hover:bg-yellow-600 text-white text-xs px-2 py-1 rounded">
                                                Edit
                                            </a>
                                            <form action="{{ route('tables.destroy', $table) }}" method="POST" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="bg-red-500 hover:bg-red-600 text-white text-xs px-2 py-1 rounded" onclick="return confirm('Are you sure?')">
                                                    Del
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @empty
                            <div class="col-span-full text-center py-8">
                                <p class="text-gray-500 mb-4">No tables found.</p>
                                @if(session('api_authenticated'))
                                    <a href="{{ route('tables.create') }}" class="text-blue-600 hover:text-blue-900">Create your first table</a>
                                @endif
                            </div>
                        @endforelse
                    </div>

                    <!-- Legend -->
                    <div class="flex justify-center space-x-6 text-sm">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-500 rounded mr-2"></div>
                            <span>Available</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-red-500 rounded mr-2"></div>
                            <span>Occupied</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
