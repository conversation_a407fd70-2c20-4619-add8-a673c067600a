<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Customer Management') }}
            </h2>
            <a href="{{ route('settings.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Settings
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    
                    <!-- Success/Error Messages -->
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {{ session('error') }}
                        </div>
                    @endif

                    <!-- API Status Information -->
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                        <h3 class="text-sm font-medium text-gray-900 mb-2">Data Source Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="font-medium">API Status:</span>
                                <span class="ml-1 px-2 py-1 rounded text-xs {{ $syncStatus['api_connected'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $syncStatus['api_connected'] ? 'Connected' : 'Disconnected' }}
                                </span>
                            </div>
                            <div>
                                <span class="font-medium">Last Sync:</span>
                                <span class="ml-1 text-gray-600">{{ $syncStatus['last_sync'] }}</span>
                            </div>
                            <div>
                                <span class="font-medium">Local Customers:</span>
                                <span class="ml-1 text-gray-600">{{ $syncStatus['local_stats']['total_customers'] }}</span>
                            </div>
                            <div>
                                <span class="font-medium">Data Source:</span>
                                <span class="ml-1 px-2 py-1 rounded text-xs {{ $syncStatus['data_source'] === 'api' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                                    {{ $syncStatus['data_source'] === 'api' ? '🌐 API' : '💾 Local' }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Search Form and Actions -->
                    <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                        <form method="GET" action="{{ route('settings.customers.index') }}" class="flex items-center space-x-4 flex-1">
                            <div class="flex-1">
                                <input type="text"
                                       name="search"
                                       value="{{ request('search') }}"
                                       placeholder="Search customers by name, phone, or email..."
                                       class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            </div>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Search
                            </button>
                            @if(request('search'))
                                <a href="{{ route('settings.customers.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                    Clear
                                </a>
                            @endif
                        </form>

                        <!-- Action Buttons -->
                        <div class="flex space-x-2">
                            <a href="{{ route('settings.customers.create') }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                Add Customer
                            </a>
                            @if($syncStatus['sync_available'])
                                <form method="POST" action="{{ route('settings.customers.sync') }}" class="inline">
                                    @csrf
                                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded flex items-center space-x-2">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                        </svg>
                                        <span>Sync from API</span>
                                    </button>
                                </form>
                                <form method="POST" action="{{ route('settings.customers.sync-to-api') }}" class="inline">
                                    @csrf
                                    <button type="submit" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded flex items-center space-x-2">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"/>
                                        </svg>
                                        <span>Sync to API</span>
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>

                    <!-- Customer Count -->
                    <div class="mb-4">
                        <p class="text-gray-600">
                            @if(request('search'))
                                Showing {{ count($customers) }} customer(s) matching "{{ request('search') }}"
                            @else
                                Total: {{ count($customers) }} customers
                            @endif
                        </p>
                    </div>

                    <!-- Customers Table -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full table-auto">
                            <thead>
                                <tr class="bg-gray-50">
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Segment</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Points</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($customers as $customer)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ $customer->name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $customer->phone }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $customer->email ?? 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            @if(isset($customer->gender) && $customer->gender)
                                                <span class="px-2 py-1 rounded text-xs bg-gray-100 text-gray-800">
                                                    {{ ucfirst($customer->gender) }}
                                                </span>
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            @if(isset($customer->customer_segment) && $customer->customer_segment)
                                                <span class="px-2 py-1 rounded text-xs
                                                    {{ $customer->customer_segment === 'vip' ? 'bg-yellow-100 text-yellow-800' :
                                                       ($customer->customer_segment === 'premium' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800') }}">
                                                    {{ ucfirst($customer->customer_segment) }}
                                                </span>
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $customer->points ?? 0 }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            @if(isset($customer->active_status))
                                                <span class="px-2 py-1 rounded text-xs {{ $customer->active_status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                    {{ $customer->active_status ? 'Active' : 'Inactive' }}
                                                </span>
                                            @else
                                                <span class="px-2 py-1 rounded text-xs bg-green-100 text-green-800">Active</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <span class="px-2 py-1 rounded text-xs {{ ($customer->_source ?? 'local') === 'api' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                                                {{ ($customer->_source ?? 'local') === 'api' ? '🌐 API' : '💾 Local' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            @if(isset($customer->id))
                                                <a href="{{ route('settings.customers.edit', $customer->id) }}"
                                                   class="text-indigo-600 hover:text-indigo-900 bg-indigo-100 hover:bg-indigo-200 px-3 py-1 rounded-md transition-colors duration-200 mr-2">
                                                    Edit
                                                </a>
                                                <form method="POST" action="{{ route('settings.customers.destroy', $customer->id) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this customer?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 px-3 py-1 rounded-md transition-colors duration-200">Delete</button>
                                                </form>
                                            @else
                                                <span class="text-gray-400">API Only</span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                            @if(request('search'))
                                                No customers found matching "{{ request('search') }}"
                                            @else
                                                No customers found
                                                @if(!$syncStatus['api_connected'])
                                                    <br><span class="text-sm text-gray-400">Connect to API to sync customer data.</span>
                                                @endif
                                            @endif
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>
    </div>
</x-app-layout>
