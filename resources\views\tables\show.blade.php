<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Table Details: ') . $table->name }}
            </h2>
            <div class="flex space-x-2">
                @if(session('api_authenticated'))
                    <a href="{{ route('tables.edit', $table) }}" class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded">
                        Edit Table
                    </a>
                @endif
                <a href="{{ route('tables.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Tables
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- Table Information -->
                <div class="lg:col-span-1">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold mb-4">Table Information</h3>
                            
                            <!-- Table Status -->
                            <div class="mb-4">
                                <div class="flex items-center justify-center p-6 rounded-lg {{ $table->isAvailable() ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200' }}">
                                    <div class="text-center">
                                        <svg class="w-12 h-12 mx-auto mb-2 {{ $table->isAvailable() ? 'text-green-600' : 'text-red-600' }}" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                        </svg>
                                        <h4 class="text-xl font-bold {{ $table->isAvailable() ? 'text-green-800' : 'text-red-800' }}">
                                            {{ $table->name }}
                                        </h4>
                                        <p class="text-sm {{ $table->isAvailable() ? 'text-green-600' : 'text-red-600' }}">
                                            {{ $table->status }}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Table Details -->
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Table ID:</span>
                                    <span class="font-medium">#{{ $table->id }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Status:</span>
                                    <span class="font-medium {{ $table->isAvailable() ? 'text-green-600' : 'text-red-600' }}">
                                        {{ $table->status }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Created:</span>
                                    <span class="font-medium">{{ $table->created_at->format('M d, Y') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Last Updated:</span>
                                    <span class="font-medium">{{ $table->updated_at->format('M d, Y H:i') }}</span>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="mt-6 space-y-2">
                                @if($table->isAvailable())
                                    <a href="{{ route('pos.index') }}" class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center font-bold py-2 px-4 rounded">
                                        Create New Order
                                    </a>
                                @else
                                    @if($table->currentOrder)
                                        <a href="{{ route('pos.order', $table->currentOrder) }}" class="block w-full bg-green-600 hover:bg-green-700 text-white text-center font-bold py-2 px-4 rounded">
                                            View Current Order
                                        </a>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Order & Order History -->
                <div class="lg:col-span-2">
                    <!-- Current Order -->
                    @if($table->currentOrder)
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold mb-4">Current Order</h3>
                                <div class="border rounded-lg p-4 bg-yellow-50">
                                    <div class="flex justify-between items-start mb-3">
                                        <div>
                                            <h4 class="font-semibold">Order #{{ $table->currentOrder->id }}</h4>
                                            <p class="text-sm text-gray-600">{{ $table->currentOrder->created_at->format('M d, Y H:i') }}</p>
                                        </div>
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            {{ $table->currentOrder->status }}
                                        </span>
                                    </div>
                                    <div class="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span class="text-gray-600">Customer:</span>
                                            <span class="font-medium">{{ $table->currentOrder->customer->name ?? 'N/A' }}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-600">Total:</span>
                                            <span class="font-medium">${{ number_format($table->currentOrder->total_amount, 2) }}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-600">Items:</span>
                                            <span class="font-medium">{{ $table->currentOrder->orderItems->count() }} items</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-600">Server:</span>
                                            <span class="font-medium">{{ $table->currentOrder->user->name ?? 'N/A' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Order History -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold mb-4">Recent Order History</h3>
                            
                            @if($table->orders->count() > 0)
                                <div class="space-y-3">
                                    @foreach($table->orders as $order)
                                        <div class="border rounded-lg p-4 hover:bg-gray-50">
                                            <div class="flex justify-between items-start">
                                                <div class="flex-1">
                                                    <div class="flex items-center space-x-3 mb-2">
                                                        <h4 class="font-semibold">Order #{{ $order->id }}</h4>
                                                        <span class="px-2 py-1 text-xs font-semibold rounded-full
                                                            @if($order->status === 'Completed') bg-green-100 text-green-800
                                                            @elseif($order->status === 'Cancelled') bg-red-100 text-red-800
                                                            @else bg-yellow-100 text-yellow-800
                                                            @endif">
                                                            {{ $order->status }}
                                                        </span>
                                                    </div>
                                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm text-gray-600">
                                                        <div>{{ $order->created_at->format('M d, Y H:i') }}</div>
                                                        <div>{{ $order->customer->name ?? 'N/A' }}</div>
                                                        <div>${{ number_format($order->total_amount, 2) }}</div>
                                                        <div>{{ $order->orderItems->count() }} items</div>
                                                    </div>
                                                </div>
                                                <a href="{{ route('pos.order-details', $order) }}" class="text-blue-600 hover:text-blue-800 text-sm">
                                                    View Details
                                                </a>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                
                                @if($table->orders->count() >= 10)
                                    <div class="mt-4 text-center">
                                        <a href="{{ route('pos.order-history') }}" class="text-blue-600 hover:text-blue-800 text-sm">
                                            View All Orders →
                                        </a>
                                    </div>
                                @endif
                            @else
                                <div class="text-center py-8">
                                    <svg class="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                    <p class="text-gray-500">No orders found for this table.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
