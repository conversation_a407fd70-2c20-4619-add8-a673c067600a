<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use App\Models\OrderType;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class OrderTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $orderTypes = OrderType::ordered()->get();
        return view('settings.order-types.index', compact('orderTypes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('settings.order-types.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:order_types,name',
            'description' => 'nullable|string|max:500',
            'requires_table' => 'boolean',
            'requires_party_size' => 'boolean',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);
        
        // Set default sort order if not provided
        if (!isset($data['sort_order'])) {
            $data['sort_order'] = OrderType::max('sort_order') + 1;
        }

        // Convert checkboxes to boolean
        $data['requires_table'] = $request->has('requires_table');
        $data['requires_party_size'] = $request->has('requires_party_size');
        $data['is_active'] = $request->has('is_active');

        OrderType::create($data);

        return redirect()->route('settings.order-types.index')
            ->with('success', 'Order type created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(OrderType $orderType)
    {
        return view('settings.order-types.show', compact('orderType'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OrderType $orderType)
    {
        return view('settings.order-types.edit', compact('orderType'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OrderType $orderType)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:order_types,name,' . $orderType->id,
            'description' => 'nullable|string|max:500',
            'requires_table' => 'boolean',
            'requires_party_size' => 'boolean',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);

        // Convert checkboxes to boolean
        $data['requires_table'] = $request->has('requires_table');
        $data['requires_party_size'] = $request->has('requires_party_size');
        $data['is_active'] = $request->has('is_active');

        $orderType->update($data);

        return redirect()->route('settings.order-types.index')
            ->with('success', 'Order type updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OrderType $orderType)
    {
        // Check if order type is being used by any orders
        if ($orderType->orders()->exists()) {
            return redirect()->route('settings.order-types.index')
                ->with('error', 'Cannot delete order type that is being used by existing orders.');
        }

        $orderType->delete();

        return redirect()->route('settings.order-types.index')
            ->with('success', 'Order type deleted successfully.');
    }

    /**
     * Update the sort order of order types
     */
    public function updateOrder(Request $request)
    {
        $request->validate([
            'order_types' => 'required|array',
            'order_types.*' => 'exists:order_types,id'
        ]);

        foreach ($request->order_types as $index => $orderTypeId) {
            OrderType::where('id', $orderTypeId)->update(['sort_order' => $index + 1]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Toggle the active status of an order type
     */
    public function toggleStatus(OrderType $orderType)
    {
        $orderType->update(['is_active' => !$orderType->is_active]);

        return redirect()->route('settings.order-types.index')
            ->with('success', 'Order type status updated successfully.');
    }
}
