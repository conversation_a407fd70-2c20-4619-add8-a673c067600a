<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Data Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .header { color: #333; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1 class="header">Customer Database Test</h1>
    <p>This page would show customer data from the database. In the actual POS system, you can now:</p>
    
    <ul>
        <li><strong>Select from 20 realistic customers</strong> in the dropdown menus</li>
        <li><strong>Add new customers</strong> using the "+" button</li>
        <li><strong>Use "Guest Customer"</strong> for walk-in customers without collecting data</li>
    </ul>

    <h2>Sample Customer Data Structure:</h2>
    <table>
        <thead>
            <tr>
                <th>Name</th>
                <th>Phone</th>
                <th>Email</th>
                <th>Address</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Budi Santoso</td>
                <td>081234567890</td>
                <td><EMAIL></td>
                <td>Jl. Sudirman No. 123, Jakarta Pusat</td>
            </tr>
            <tr>
                <td>Siti Nurhaliza</td>
                <td>081234567891</td>
                <td><EMAIL></td>
                <td>Jl. Thamrin No. 45, Jakarta Pusat</td>
            </tr>
            <tr>
                <td>Ahmad Wijaya</td>
                <td>081234567892</td>
                <td><EMAIL></td>
                <td>Jl. Gatot Subroto No. 67, Jakarta Selatan</td>
            </tr>
            <tr>
                <td>Dewi Lestari</td>
                <td>081234567893</td>
                <td><EMAIL></td>
                <td>Jl. Kemang Raya No. 89, Jakarta Selatan</td>
            </tr>
            <tr>
                <td>Rudi Hartono</td>
                <td>081234567894</td>
                <td><EMAIL></td>
                <td>Jl. Senopati No. 12, Jakarta Selatan</td>
            </tr>
            <tr>
                <td colspan="4" style="text-align: center; font-style: italic; color: #666;">
                    ... and 15 more realistic customers
                </td>
            </tr>
        </tbody>
    </table>

    <h2>Features Implemented:</h2>
    <ul>
        <li>✅ Walk-in customer removed from database</li>
        <li>✅ 20 realistic Indonesian customers added</li>
        <li>✅ Complete contact information (name, phone, email, address)</li>
        <li>✅ Realistic Indonesian names and Jakarta addresses</li>
        <li>✅ Guest Customer option for anonymous orders</li>
        <li>✅ Customer dropdown integration in both Table and Menu views</li>
    </ul>

    <p><strong>Test the POS system now to see the realistic customer data in action!</strong></p>
</body>
</html>
