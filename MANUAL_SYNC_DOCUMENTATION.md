# Manual Product Sync Documentation

## Overview

The Manual Product Sync feature provides **bidirectional synchronization** between local products and API products based on SKU (Stock Keeping Unit). This ensures both systems stay in sync without duplicating existing products.

## 🔄 How It Works

### **Bidirectional Sync Process**

1. **📤 Local → API Sync**
   - Finds local products with SKUs that don't exist in API
   - Sends these products to API endpoint: `POST /api/pos/sync/products`
   - Updates local products with API IDs when successful

2. **📥 API → Local Sync**
   - Finds API products with SKUs that don't exist locally
   - Creates these products in local database
   - Links them with API IDs for future reference

3. **⏭️ Smart Skipping**
   - Products existing in both systems (same SKU) are skipped
   - Prevents duplicate creation and data conflicts
   - Maintains data integrity across systems

## 🎯 Key Features

### **✅ SKU-Based Matching**
- Uses SKU as the unique identifier for sync
- Only products with valid SKUs are synchronized
- Empty or null SKUs are ignored

### **✅ Comprehensive Logging**
- Detailed logs for all sync operations
- Success/failure tracking for each product
- Performance metrics and timing information

### **✅ Error Handling**
- Graceful handling of network failures
- Individual product failures don't stop entire sync
- Detailed error reporting for troubleshooting

### **✅ User Feedback**
- Real-time progress indication
- Detailed sync statistics display
- Clear success/failure notifications

## 🖱️ Using the Manual Sync Button

### **Location**
The **"Manual Sync"** button is located in the Products page header, next to the "Add New Product" button.

### **Button Features**
- **Purple color** to distinguish from other sync buttons
- **Confirmation dialog** before starting sync
- **Loading animation** during sync process
- **Disabled state** to prevent multiple simultaneous syncs

### **Confirmation Dialog**
```
Manual Sync akan melakukan sinkronisasi dua arah:

• Produk lokal dengan SKU baru akan dikirim ke API
• Produk API dengan SKU baru akan dibuat secara lokal  
• Produk yang sudah ada akan dilewati

Proses ini mungkin memakan waktu beberapa menit. Lanjutkan?
```

## 📊 Sync Results Display

### **Success Notification**
Shows comprehensive statistics including:
- **Local → API**: Products sent, skipped, failed
- **API → Local**: Products created, skipped, failed
- **Totals**: Total products in each system
- **Timing**: Sync completion time

### **Detailed Statistics**
```
📤 Local → API Sync:
  ✅ Sent: 5 products
  ⏭️ Skipped: 12 products  
  ❌ Failed: 1 product

📥 API → Local Sync:
  ✅ Created: 3 products
  ⏭️ Skipped: 8 products
  ❌ Failed: 0 products
```

## 🔧 Technical Implementation

### **Service Architecture**

#### **ManualProductSyncService**
- Main service handling bidirectional sync logic
- Coordinates between local database and API
- Manages error handling and statistics collection

#### **Key Methods**
```php
// Main sync method
public function performBidirectionalSync(): array

// Get products indexed by SKU
private function getLocalProductsBySku(): array
private function getApiProductsBySku(): ?array

// Sync in both directions
private function syncLocalToApi(array $localProducts, array $apiProducts): array
private function syncApiToLocal(array $localProducts, array $apiProducts): array
```

### **Database Operations**

#### **Local Product Creation**
```php
private function createLocalProductFromApi(array $apiProduct): ?MenuItem
{
    // Get or create category
    $category = MenuCategory::firstOrCreate(['name' => $categoryName]);
    
    // Create product with API data
    $menuItem = MenuItem::create([
        'name' => $apiProduct['name'],
        'sku' => $apiProduct['sku'],
        'price' => $apiProduct['price'],
        'api_id' => $apiProduct['id'],
        // ... other fields
    ]);
    
    return $menuItem;
}
```

### **API Integration**

#### **Authentication**
- Uses existing ProductSyncService for API authentication
- Bearer token authentication with automatic refresh
- Handles authentication failures gracefully

#### **Data Format**
Products sent to API include:
```json
{
  "name": "Product Name",
  "sku": "PROD-001", 
  "barcode": "1234567890",
  "price": 25000,
  "cost_price": 15000,
  "stock_quantity": 100,
  "is_active": true,
  "is_food_item": true,
  "category": {
    "id": 1,
    "name": "Category Name"
  },
  "local_id": 123
}
```

## 🛡️ Error Handling

### **Network Failures**
- Checks network connectivity before starting
- Graceful failure with user-friendly messages
- Automatic retry suggestions

### **API Failures**
- Individual product failures don't stop sync
- Detailed error logging for each failure
- Partial success reporting

### **Database Failures**
- Transaction-based operations for data integrity
- Rollback on critical failures
- Detailed error reporting

## 📋 Use Cases

### **1. Initial Setup**
When setting up a new POS system:
- Import existing products from API
- Send locally created products to API
- Establish bidirectional sync

### **2. Regular Maintenance**
Periodic sync to ensure consistency:
- Catch products created in either system
- Maintain data synchronization
- Identify sync issues early

### **3. After System Issues**
Recovery after network or system problems:
- Resync products that may have been missed
- Ensure no products are lost
- Restore system consistency

## 🔍 Monitoring & Debugging

### **Log Files**
Check `storage/logs/laravel.log` for:
- Sync start/completion times
- Individual product sync results
- Error details and stack traces
- Performance metrics

### **Console Output**
During sync, detailed information is logged:
```
ManualProductSyncService: Starting bidirectional sync
ManualProductSyncService: Found local products [count: 25]
ManualProductSyncService: Found API products [count: 30]
ManualProductSyncService: Product sent to API [sku: PROD-001, api_id: 456]
ManualProductSyncService: Product created locally from API [sku: API-002, local_id: 123]
ManualProductSyncService: Bidirectional sync completed
```

## ⚙️ Configuration

### **Network Settings**
```env
# API Configuration
POS_API_BASE_URL=http://viera-filament.test/api/pos
POS_API_USERNAME=your_username
POS_API_PASSWORD=your_password
POS_API_TIMEOUT=30

# Optional: Disable network validation for development
POS_DISABLE_NETWORK_VALIDATION=false
```

### **Sync Behavior**
- **SKU Requirement**: Only products with non-empty SKUs are synced
- **Category Handling**: Missing categories are created automatically
- **Conflict Resolution**: Existing products (same SKU) are skipped
- **Transaction Safety**: Database operations use transactions

## 🚀 Benefits

### **✅ Data Consistency**
- Ensures both systems have the same product data
- Prevents data loss during system migrations
- Maintains referential integrity

### **✅ Operational Efficiency**
- Reduces manual data entry
- Automates synchronization process
- Saves time and reduces errors

### **✅ Business Continuity**
- Works with or without network connectivity
- Graceful degradation during failures
- Comprehensive error reporting

### **✅ Scalability**
- Handles large product catalogs efficiently
- Batch processing for optimal performance
- Memory-efficient operations

The Manual Sync feature provides a robust, user-friendly solution for maintaining product data consistency across your POS system and external API! 🎉
