<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Client\RequestException;
use Exception;

/**
 * POS API Service for syncing products
 * Handles communication with external POS API with authentication
 */
class PosApiService
{
    private string $baseUrl;
    private int $timeout;
    private string $cacheKeyToken;
    private int $tokenCacheDuration;

    public function __construct()
    {
        $this->baseUrl = config('pos.api_base_url', 'http://viera-filament.test/api/pos');
        $this->timeout = config('pos.api_timeout', 30);
        $this->cacheKeyToken = config('pos.token_cache_key', 'pos_api_token');
        $this->tokenCacheDuration = config('pos.token_cache_duration', 3600);
    }

    /**
     * Authenticate with the POS API and cache the token
     *
     * @param string|null $email
     * @param string|null $password
     * @param string|null $deviceName
     * @return array
     */
    public function authenticate(?string $email = null, ?string $password = null, ?string $deviceName = null): array
    {
        $email = $email ?? config('pos.api_email');
        $password = $password ?? config('pos.api_password');
        $deviceName = $deviceName ?? config('pos.api_device_name', 'POS System');

        if (!$email || !$password) {
            return [
                'success' => false,
                'error' => 'API credentials not configured. Please set POS_API_EMAIL and POS_API_PASSWORD in your environment.',
                'status_code' => 401
            ];
        }

        try {
            $loginData = [
                'email' => $email,
                'password' => $password,
                'device_name' => $deviceName,
            ];

            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])
            ->timeout($this->timeout)
            ->post($this->baseUrl . '/login', $loginData);

            if ($response->successful()) {
                $data = $response->json();
                $token = $data['token'] ?? $data['access_token'] ?? null;

                if ($token) {
                    // Cache the token
                    Cache::put($this->cacheKeyToken, $token, $this->tokenCacheDuration);

                    // Cache outlet ID if available
                    $outletId = null;

                    // Check for outlet in direct response
                    if (isset($data['outlet']) && isset($data['outlet']['id'])) {
                        $outletId = $data['outlet']['id'];
                    }
                    // Check for outlets in user object
                    elseif (isset($data['user']['outlets']) && !empty($data['user']['outlets'])) {
                        $firstOutlet = $data['user']['outlets'][0];
                        if (isset($firstOutlet['id'])) {
                            $outletId = $firstOutlet['id'];
                        }
                    }

                    if ($outletId) {
                        Cache::put('pos_outlet_id', $outletId, $this->tokenCacheDuration);

                        // Also cache outlet category
                        $outletCategory = null;
                        if (isset($data['outlet']['categories'])) {
                            $outletCategory = $data['outlet']['categories'];
                        } elseif (isset($data['user']['outlets']) && !empty($data['user']['outlets'])) {
                            $firstOutlet = $data['user']['outlets'][0];
                            $outletCategory = $firstOutlet['categories'] ?? null;
                        }

                        if ($outletCategory) {
                            Cache::put('pos_outlet_category', $outletCategory, $this->tokenCacheDuration);
                        }

                        Log::info('POS API: Outlet information cached', [
                            'outlet_id' => $outletId,
                            'outlet_category' => $outletCategory,
                            'source' => isset($data['outlet']) ? 'direct' : 'user.outlets'
                        ]);
                    }

                    Log::info('POS API: Authentication successful', [
                        'email' => $email,
                        'token_length' => strlen($token),
                        'has_outlet' => isset($data['outlet'])
                    ]);

                    return [
                        'success' => true,
                        'token' => $token,
                        'data' => $data,
                        'status_code' => $response->status(),
                        'message' => 'Authentication successful'
                    ];
                }

                return [
                    'success' => false,
                    'error' => 'No token found in authentication response',
                    'status_code' => $response->status(),
                    'response_data' => $data
                ];
            }

            Log::error('POS API: Authentication failed', [
                'status' => $response->status(),
                'body' => $response->body(),
                'email' => $email
            ]);

            return [
                'success' => false,
                'error' => 'Authentication failed: ' . $response->status(),
                'status_code' => $response->status(),
                'response_body' => $response->body()
            ];

        } catch (RequestException $e) {
            Log::error('POS API: Authentication request exception', [
                'message' => $e->getMessage(),
                'response' => $e->response?->body(),
                'email' => $email
            ]);

            return [
                'success' => false,
                'error' => 'Authentication request failed: ' . $e->getMessage(),
                'exception' => get_class($e)
            ];

        } catch (Exception $e) {
            Log::error('POS API: Authentication general exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'email' => $email
            ]);

            return [
                'success' => false,
                'error' => 'Authentication error: ' . $e->getMessage(),
                'exception' => get_class($e)
            ];
        }
    }

    /**
     * Get cached authentication token or authenticate if needed
     *
     * @return string|null
     */
    private function getAuthToken(): ?string
    {
        // First try to get token from session (if user is logged in)
        if (session()->has('api_token')) {
            $sessionToken = session('api_token');
            if ($sessionToken) {
                return $sessionToken;
            }
        }

        // Fallback to cached token for background processes
        $token = Cache::get($this->cacheKeyToken);

        if ($token) {
            return $token;
        }

        // If no token available, try to authenticate
        $authResult = $this->authenticate();

        if ($authResult['success']) {
            return $authResult['token'];
        }

        Log::warning('POS API: Unable to get authentication token', [
            'auth_error' => $authResult['error'] ?? 'Unknown error'
        ]);

        return null;
    }

    /**
     * Get outlet ID from session or cache
     *
     * @return string|null
     */
    private function getOutletId(): ?string
    {
        // First try to get outlet ID from session (if user is logged in)
        if (session()->has('api_outlet_id')) {
            $sessionOutletId = session('api_outlet_id');
            if ($sessionOutletId) {
                return (string) $sessionOutletId;
            }
        }

        // Fallback to cached outlet ID for background processes
        $cachedOutletId = Cache::get('pos_outlet_id');
        if ($cachedOutletId) {
            return (string) $cachedOutletId;
        }

        Log::warning('POS API: No outlet ID available in session or cache');
        return null;
    }

    /**
     * Clear cached authentication token and session token
     *
     * @return void
     */
    public function clearAuthToken(): void
    {
        Cache::forget($this->cacheKeyToken);
        Cache::forget('pos_outlet_id');
        Cache::forget('pos_outlet_category');

        // Also clear session token if it exists
        if (session()->has('api_token')) {
            session()->forget('api_token');
            session()->forget('api_user');
            session()->forget('api_authenticated');
            session()->forget('api_outlet_id');
            session()->forget('api_outlet_category');
            session()->forget('pos_needs_tables');
        }

        Log::info('POS API: Authentication token, outlet ID, and outlet category cleared from cache and session');
    }

    /**
     * Fetch products from POS API using Laravel HTTP Client
     *
     * @return array
     */
    public function fetchProducts(): array
    {
        $token = $this->getAuthToken();

        if (!$token) {
            return [
                'success' => false,
                'error' => 'No authentication token available',
                'status_code' => 401
            ];
        }

        // Get outlet ID for filtering
        $outletId = $this->getOutletId();

        // Build URL with outlet_id parameter if available
        $url = $this->baseUrl . '/sync/products';
        if ($outletId) {
            $url .= '?outlet_id=' . $outletId;
            Log::info('POS API: Fetching products for outlet', [
                'outlet_id' => $outletId,
                'url' => $url,
                'full_url' => $url
            ]);
        } else {
            Log::warning('POS API: Fetching products without outlet filter - no outlet ID available', [
                'url' => $url
            ]);
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])
            ->timeout($this->timeout)
            ->retry(3, 1000) // Retry 3 times with 1 second delay
            ->get($url);

            // Check if request was successful
            if ($response->successful()) {
                $data = $response->json();

                Log::info('POS API: Products fetched successfully', [
                    'count' => count($data['data'] ?? []),
                    'status' => $response->status()
                ]);

                return [
                    'success' => true,
                    'data' => $data,
                    'status_code' => $response->status(),
                    'message' => 'Products fetched successfully'
                ];
            }

            // Handle authentication errors - clear token and retry once
            if ($response->status() === 401) {
                Log::warning('POS API: Authentication token expired, clearing cache and retrying');
                $this->clearAuthToken();

                // Try to get a new token and retry once
                $newToken = $this->getAuthToken();
                if ($newToken && $newToken !== $token) {
                    return $this->fetchProducts(); // Recursive call with new token
                }
            }

            // Handle other HTTP errors
            Log::error('POS API: HTTP Error', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            return [
                'success' => false,
                'error' => 'HTTP Error: ' . $response->status(),
                'status_code' => $response->status(),
                'response_body' => $response->body()
            ];

        } catch (RequestException $e) {
            Log::error('POS API: Request Exception', [
                'message' => $e->getMessage(),
                'response' => $e->response?->body()
            ]);

            return [
                'success' => false,
                'error' => 'Request failed: ' . $e->getMessage(),
                'exception' => get_class($e)
            ];

        } catch (Exception $e) {
            Log::error('POS API: General Exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Unexpected error: ' . $e->getMessage(),
                'exception' => get_class($e)
            ];
        }
    }

    /**
     * Fetch products with additional options
     *
     * @param array $options
     * @return array
     */
    public function fetchProductsWithOptions(array $options = []): array
    {
        $token = $this->getAuthToken();

        if (!$token) {
            return [
                'success' => false,
                'error' => 'No authentication token available',
                'status_code' => 401
            ];
        }

        $defaultOptions = [
            'timeout' => $this->timeout,
            'retry_attempts' => 3,
            'retry_delay' => 1000,
            'verify_ssl' => true,
        ];

        $options = array_merge($defaultOptions, $options);

        try {
            $httpClient = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])
            ->timeout($options['timeout']);

            // Add SSL verification option
            if (!$options['verify_ssl']) {
                $httpClient = $httpClient->withoutVerifying();
            }

            // Add retry logic
            if ($options['retry_attempts'] > 0) {
                $httpClient = $httpClient->retry(
                    $options['retry_attempts'],
                    $options['retry_delay']
                );
            }

            // Get outlet ID for filtering
            $outletId = $this->getOutletId();

            // Build URL with outlet_id parameter if available
            $url = $this->baseUrl . '/sync/products';
            if ($outletId) {
                $url .= '?outlet_id=' . $outletId;
            }

            $response = $httpClient->get($url);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status(),
                    'headers' => $response->headers(),
                    'response_time' => $response->transferStats?->getTransferTime()
                ];
            }

            // Handle authentication errors
            if ($response->status() === 401) {
                $this->clearAuthToken();
            }

            return [
                'success' => false,
                'error' => 'HTTP Error: ' . $response->status(),
                'status_code' => $response->status(),
                'response_body' => $response->body()
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'exception' => get_class($e)
            ];
        }
    }

    /**
     * Test API connection
     *
     * @return array
     */
    public function testConnection(): array
    {
        $token = $this->getAuthToken();

        if (!$token) {
            return [
                'success' => false,
                'error' => 'No authentication token available',
                'status_code' => 401,
                'message' => 'Authentication required'
            ];
        }

        try {
            // Get outlet ID for filtering
            $outletId = $this->getOutletId();

            // Build URL with outlet_id parameter if available
            $url = $this->baseUrl . '/sync/products';
            if ($outletId) {
                $url .= '?outlet_id=' . $outletId;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json',
            ])
            ->timeout(10)
            ->get($url);

            return [
                'success' => $response->successful(),
                'status_code' => $response->status(),
                'response_time' => $response->transferStats?->getTransferTime(),
                'message' => $response->successful() ? 'Connection successful' : 'Connection failed'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Connection test failed'
            ];
        }
    }

    /**
     * Get formatted product data
     * 
     * @return array
     */
    public function getFormattedProducts(): array
    {
        $result = $this->fetchProducts();
        
        if (!$result['success']) {
            return $result;
        }

        $products = $result['data']['data'] ?? [];
        $formattedProducts = [];

        foreach ($products as $product) {
            $formattedProducts[] = [
                'id' => $product['id'] ?? null,
                'name' => $product['name'] ?? 'Unknown Product',
                'price' => $product['price'] ?? 0,
                'category' => $product['category']['name'] ?? 'Uncategorized',
                'is_active' => $product['is_active'] ?? false,
                'description' => $product['description'] ?? '',
                'created_at' => $product['created_at'] ?? null,
                'updated_at' => $product['updated_at'] ?? null,
            ];
        }

        return [
            'success' => true,
            'products' => $formattedProducts,
            'total_count' => count($formattedProducts),
            'original_response' => $result
        ];
    }
}
