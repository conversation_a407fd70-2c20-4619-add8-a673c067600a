<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Order Details - #') . $order->id }}
            </h2>
            <div class="flex space-x-2">
                <span class="bg-{{ $order->status === 'Completed' ? 'green' : ($order->status === 'Paid' ? 'blue' : ($order->status === 'Cancelled' ? 'red' : 'yellow')) }}-100 text-{{ $order->status === 'Completed' ? 'green' : ($order->status === 'Paid' ? 'blue' : ($order->status === 'Cancelled' ? 'red' : 'yellow')) }}-800 text-sm font-medium px-3 py-1 rounded-full">
                    {{ $order->status }}
                </span>
                <a href="{{ route('pos.ongoing-orders') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Orders
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                
                <!-- Order Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Information</h3>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Order ID</label>
                                <p class="mt-1 text-sm text-gray-900">#{{ $order->id }}</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Status</label>
                                <p class="mt-1">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-{{ $order->status === 'Completed' ? 'green' : ($order->status === 'Paid' ? 'blue' : ($order->status === 'Cancelled' ? 'red' : 'yellow')) }}-100 text-{{ $order->status === 'Completed' ? 'green' : ($order->status === 'Paid' ? 'blue' : ($order->status === 'Cancelled' ? 'red' : 'yellow')) }}-800">
                                        {{ $order->status }}
                                    </span>
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Order Type</label>
                                <p class="mt-1">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-{{ $order->order_type === 'dine_in' ? 'blue' : 'orange' }}-100 text-{{ $order->order_type === 'dine_in' ? 'blue' : 'orange' }}-800">
                                        {{ $order->order_type === 'dine_in' ? 'Dine In' : 'Take Away' }}
                                    </span>
                                </p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700">{{ $order->table ? 'Table' : 'Order Type' }}</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $order->table ? $order->table->name : ucfirst($order->order_type ?? 'Take Away') }}</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Party Size</label>
                                <p class="mt-1 text-sm text-gray-900">
                                    <span class="inline-flex items-center">
                                        <svg class="w-4 h-4 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                        {{ $order->party_size ?? 2 }} {{ ($order->party_size ?? 2) == 1 ? 'guest' : 'guests' }}
                                    </span>
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Customer</label>
                                <div class="mt-1 text-sm text-gray-900">
                                    <p class="font-medium">{{ $order->customer->name }}</p>
                                    @if($order->customer->phone)
                                        <p class="text-gray-600">📞 {{ $order->customer->phone }}</p>
                                    @endif
                                    @if($order->customer->email)
                                        <p class="text-gray-600">✉️ {{ $order->customer->email }}</p>
                                    @endif
                                    @if($order->customer->address)
                                        <p class="text-gray-600">📍 {{ $order->customer->address }}</p>
                                    @endif
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Cashier</label>
                                <div class="mt-1 text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        <div>
                                            <p class="font-medium">{{ $order->cashier_name ?? 'Unknown Cashier' }}</p>
                                            @if($order->cashier_email)
                                                <p class="text-gray-600 text-xs">{{ $order->cashier_email }}</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Server</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $order->cashier_name ?? 'Unknown' }}</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Created</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $order->created_at->setTimezone('Asia/Jakarta')->format('M d, Y H:i') }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                        
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Subtotal:</span>
                                <span class="text-sm font-medium">{{ $orderSettings['currency_symbol'] }}{{ number_format($order->subtotal, 2) }}</span>
                            </div>

                            @if($order->discount_amount > 0)
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">
                                    Discount
                                    @if($order->discount_type === 'percentage')
                                        ({{ $order->discount_value }}%)
                                    @elseif($order->discount_type === 'fixed')
                                        ({{ $orderSettings['currency_symbol'] }}{{ number_format($order->discount_value, 2) }} off)
                                    @elseif($order->discount_type === 'coupon')
                                        ({{ $order->coupon_code }})
                                    @elseif($order->discount_type === 'points')
                                        ({{ $order->points_used }} points)
                                    @endif:
                                </span>
                                <span class="text-sm font-medium text-green-600">-{{ $orderSettings['currency_symbol'] }}{{ number_format($order->discount_amount, 2) }}</span>
                            </div>
                            @endif

                            @if($order->tax_amount > 0)
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Tax:</span>
                                <span class="text-sm font-medium">{{ $orderSettings['currency_symbol'] }}{{ number_format($order->tax_amount, 2) }}</span>
                            </div>
                            @endif

                            <div class="flex justify-between border-t pt-3">
                                <span class="text-base font-semibold">Total:</span>
                                <span class="text-base font-semibold">{{ $orderSettings['currency_symbol'] }}{{ number_format($order->total_amount, 2) }}</span>
                            </div>
                            
                            @if($order->payments->count() > 0)
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Amount Paid:</span>
                                <span class="text-sm font-medium text-green-600">{{ $orderSettings['currency_symbol'] }}{{ number_format($order->payments->sum('amount_paid'), 2) }}</span>
                            </div>

                            @php
                                $remainingAmount = $order->total_amount - $order->payments->sum('amount_paid');
                            @endphp
                            @if($remainingAmount > 0)
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Remaining:</span>
                                <span class="text-sm font-medium text-red-600">{{ $orderSettings['currency_symbol'] }}{{ number_format($remainingAmount, 2) }}</span>
                            </div>
                            @endif
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="mt-6 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($order->orderItems as $orderItem)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">{{ $orderItem->menuItem?->name ?? 'Unknown Item' }}</div>
                                                @if($orderItem->notes)
                                                    <div class="text-sm text-gray-500">Note: {{ $orderItem->notes }}</div>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $orderSettings['currency_symbol'] }}{{ number_format($orderItem->price_at_time, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $orderItem->quantity }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ $orderSettings['currency_symbol'] }}{{ number_format($orderItem->total_price, 2) }}
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                                            No items in this order.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            @if($order->payments->count() > 0)
            <div class="mt-6 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Information</h3>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($order->payments as $payment)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $payment->method }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ $orderSettings['currency_symbol'] }}{{ number_format($payment->amount_paid, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $payment->created_at->setTimezone('Asia/Jakarta')->format('M d, Y H:i') }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif

            <!-- Action Buttons -->
            <div class="mt-6 flex justify-center space-x-4">
                <a href="{{ route('pos.kitchen', $order) }}" target="_blank" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
                    Print Kitchen Ticket
                </a>
                
                @if($order->status === 'Completed' || $order->status === 'Paid')
                    <a href="{{ route('pos.receipt', $order) }}" target="_blank" class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded">
                        Print Receipt
                    </a>
                @endif
                
                @if($order->status !== 'Completed' && $order->status !== 'Paid' && $order->status !== 'Cancelled')
                    <a href="{{ route('pos.order', $order) }}" class="bg-indigo-500 hover:bg-indigo-600 text-white font-bold py-2 px-4 rounded">
                        Edit Order
                    </a>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
