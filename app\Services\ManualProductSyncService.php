<?php

namespace App\Services;

use App\Models\MenuItem;
use App\Models\MenuCategory;
use App\Services\ProductSyncService;
use App\Services\ProductService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ManualProductSyncService
{
    private ProductSyncService $productSyncService;
    private ProductService $productService;

    public function __construct(ProductSyncService $productSyncService, ProductService $productService)
    {
        $this->productSyncService = $productSyncService;
        $this->productService = $productService;
    }

    /**
     * Perform bidirectional sync between local and API based on SKU
     *
     * @return array
     */
    public function performBidirectionalSync(): array
    {
        Log::info('ManualProductSyncService: Starting bidirectional sync');

        try {
            // Check network connectivity
            if (!$this->productSyncService->isNetworkAvailable()) {
                return [
                    'success' => false,
                    'error' => 'Network connection not available. Cannot sync with API.',
                    'stats' => $this->getEmptyStats()
                ];
            }

            // Get products from both sources
            $localProducts = $this->getLocalProductsBySku();
            $apiProducts = $this->getApiProductsBySku();

            if ($apiProducts === null) {
                return [
                    'success' => false,
                    'error' => 'Failed to fetch products from API.',
                    'stats' => $this->getEmptyStats()
                ];
            }

            // Perform sync in both directions
            $stats = [
                'local_to_api' => $this->syncLocalToApi($localProducts, $apiProducts),
                'api_to_local' => $this->syncApiToLocal($localProducts, $apiProducts),
                'total_local_products' => count($localProducts),
                'total_api_products' => count($apiProducts),
                'sync_time' => now()->format('Y-m-d H:i:s')
            ];

            Log::info('ManualProductSyncService: Bidirectional sync completed', $stats);

            return [
                'success' => true,
                'message' => 'Bidirectional sync completed successfully',
                'stats' => $stats
            ];

        } catch (\Exception $e) {
            Log::error('ManualProductSyncService: Sync failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Sync failed: ' . $e->getMessage(),
                'stats' => $this->getEmptyStats()
            ];
        }
    }

    /**
     * Get local products indexed by SKU
     *
     * @return array
     */
    private function getLocalProductsBySku(): array
    {
        $products = MenuItem::with('category')
            ->whereNotNull('sku')
            ->where('sku', '!=', '')
            ->get();

        $indexed = [];
        foreach ($products as $product) {
            $indexed[$product->sku] = $product;
        }

        Log::info('ManualProductSyncService: Found local products', ['count' => count($indexed)]);
        return $indexed;
    }

    /**
     * Get API products indexed by SKU
     *
     * @return array|null
     */
    private function getApiProductsBySku(): ?array
    {
        try {
            // Get PosApiService through app container to avoid dependency issues
            $posApiService = app(\App\Services\PosApiService::class);
            $result = $posApiService->fetchProducts();

            if (!$result['success']) {
                Log::error('ManualProductSyncService: Failed to fetch API products', [
                    'error' => $result['error'] ?? 'Unknown error'
                ]);
                return null;
            }

            // Extract products from the result
            $products = [];
            if (isset($result['data']['data'])) {
                $products = $result['data']['data'];
            } elseif (isset($result['data']['products'])) {
                $products = $result['data']['products'];
            } elseif (isset($result['data']) && is_array($result['data'])) {
                $products = $result['data'];
            }

            $indexed = [];
            foreach ($products as $product) {
                $sku = $product['sku'] ?? null;
                if ($sku && !empty(trim($sku))) {
                    $indexed[$sku] = $product;
                }
            }

            Log::info('ManualProductSyncService: Found API products', ['count' => count($indexed)]);
            return $indexed;

        } catch (\Exception $e) {
            Log::error('ManualProductSyncService: Exception while fetching API products', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Sync local products to API (products that exist locally but not in API)
     *
     * @param array $localProducts
     * @param array $apiProducts
     * @return array
     */
    private function syncLocalToApi(array $localProducts, array $apiProducts): array
    {
        $stats = [
            'products_sent' => 0,
            'products_failed' => 0,
            'products_skipped' => 0,
            'details' => []
        ];

        foreach ($localProducts as $sku => $localProduct) {
            // Skip if product already exists in API
            if (isset($apiProducts[$sku])) {
                $stats['products_skipped']++;
                $stats['details'][] = [
                    'sku' => $sku,
                    'name' => $localProduct->name,
                    'action' => 'skipped',
                    'reason' => 'Already exists in API'
                ];
                continue;
            }

            // Send to API
            $syncResult = $this->productSyncService->syncProductToApi($localProduct);

            if ($syncResult['success']) {
                $stats['products_sent']++;
                $stats['details'][] = [
                    'sku' => $sku,
                    'name' => $localProduct->name,
                    'action' => 'sent_to_api',
                    'api_id' => $syncResult['api_id'] ?? null
                ];

                Log::info('ManualProductSyncService: Product sent to API', [
                    'sku' => $sku,
                    'local_id' => $localProduct->id,
                    'api_id' => $syncResult['api_id'] ?? null
                ]);
            } else {
                $stats['products_failed']++;
                $stats['details'][] = [
                    'sku' => $sku,
                    'name' => $localProduct->name,
                    'action' => 'failed',
                    'error' => $syncResult['error']
                ];

                Log::warning('ManualProductSyncService: Failed to send product to API', [
                    'sku' => $sku,
                    'error' => $syncResult['error']
                ]);
            }
        }

        return $stats;
    }

    /**
     * Sync API products to local (products that exist in API but not locally)
     *
     * @param array $localProducts
     * @param array $apiProducts
     * @return array
     */
    private function syncApiToLocal(array $localProducts, array $apiProducts): array
    {
        $stats = [
            'products_created' => 0,
            'products_failed' => 0,
            'products_skipped' => 0,
            'details' => []
        ];

        foreach ($apiProducts as $sku => $apiProduct) {
            // Skip if product already exists locally
            if (isset($localProducts[$sku])) {
                $stats['products_skipped']++;
                $stats['details'][] = [
                    'sku' => $sku,
                    'name' => $apiProduct['name'] ?? 'Unknown',
                    'action' => 'skipped',
                    'reason' => 'Already exists locally'
                ];
                continue;
            }

            // Create locally
            try {
                $localProduct = $this->createLocalProductFromApi($apiProduct);

                if ($localProduct) {
                    $stats['products_created']++;
                    $stats['details'][] = [
                        'sku' => $sku,
                        'name' => $localProduct->name,
                        'action' => 'created_locally',
                        'local_id' => $localProduct->id
                    ];

                    Log::info('ManualProductSyncService: Product created locally from API', [
                        'sku' => $sku,
                        'local_id' => $localProduct->id,
                        'api_id' => $apiProduct['id'] ?? null
                    ]);
                } else {
                    $stats['products_failed']++;
                    $stats['details'][] = [
                        'sku' => $sku,
                        'name' => $apiProduct['name'] ?? 'Unknown',
                        'action' => 'failed',
                        'error' => 'Failed to create local product'
                    ];
                }
            } catch (\Exception $e) {
                $stats['products_failed']++;
                $stats['details'][] = [
                    'sku' => $sku,
                    'name' => $apiProduct['name'] ?? 'Unknown',
                    'action' => 'failed',
                    'error' => $e->getMessage()
                ];

                Log::error('ManualProductSyncService: Failed to create local product', [
                    'sku' => $sku,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $stats;
    }

    /**
     * Create local product from API data
     *
     * @param array $apiProduct
     * @return MenuItem|null
     */
    private function createLocalProductFromApi(array $apiProduct): ?MenuItem
    {
        DB::beginTransaction();

        try {
            // Get or create category
            $categoryName = $apiProduct['category']['name'] ?? 'Imported Products';
            $category = MenuCategory::firstOrCreate(
                ['name' => $categoryName],
                ['description' => 'Category imported from API']
            );

            // Create product
            $productData = [
                'name' => $apiProduct['name'] ?? 'Imported Product',
                'sku' => $apiProduct['sku'],
                'barcode' => $apiProduct['barcode'] ?? null,
                'description' => $apiProduct['description'] ?? null,
                'price' => floatval($apiProduct['price'] ?? 0),
                'cost_price' => floatval($apiProduct['cost_price'] ?? 0),
                'category_id' => $category->id,
                'stock_quantity' => intval($apiProduct['stock_quantity'] ?? 0),
                'is_active' => $apiProduct['is_active'] ?? $apiProduct['is_available'] ?? true,
                'is_food_item' => $apiProduct['is_food_item'] ?? true,
                'image' => $apiProduct['image'] ?? $apiProduct['image_path'] ?? null,
                'api_id' => $apiProduct['id'] ?? null,
                // Mark as synced since this product comes from API
                'is_synced_to_api' => true,
                'last_api_sync_at' => now(),
            ];

            $menuItem = MenuItem::create($productData);

            DB::commit();
            return $menuItem;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get empty stats structure
     *
     * @return array
     */
    private function getEmptyStats(): array
    {
        return [
            'local_to_api' => [
                'products_sent' => 0,
                'products_failed' => 0,
                'products_skipped' => 0,
                'details' => []
            ],
            'api_to_local' => [
                'products_created' => 0,
                'products_failed' => 0,
                'products_skipped' => 0,
                'details' => []
            ],
            'total_local_products' => 0,
            'total_api_products' => 0,
            'sync_time' => now()->format('Y-m-d H:i:s')
        ];
    }
}
