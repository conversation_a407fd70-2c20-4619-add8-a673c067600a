@extends('analytics.layout')

@section('title', 'Sync Analytics')
@section('description', 'API synchronization status, success rates, and error tracking')

@section('header-actions')
    <div class="relative">
        <button onclick="toggleExportDropdown()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded flex items-center">
            📊 Export Sync Data
            <svg class="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
            </svg>
        </button>
        <div id="exportDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
            <div class="py-1">
                <button onclick="exportData('csv')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    📄 Export as CSV
                </button>
                <button onclick="exportData('json')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    📋 Export as JSON
                </button>
            </div>
        </div>
    </div>
@endsection

@section('content')

<!-- Filters -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">📊 Sync Filters</h3>
        <form method="GET" action="{{ route('analytics.sync') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                <input type="date" id="start_date" name="start_date" value="{{ $startDate }}" 
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            </div>
            <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                <input type="date" id="end_date" name="end_date" value="{{ $endDate }}" 
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            </div>
            <div>
                <label for="category_filter" class="block text-sm font-medium text-gray-700">Category Filter</label>
                <select id="category_filter" name="category_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <option value="">All Categories</option>
                    @foreach($categoryData['category_performance'] as $category)
                        <option value="{{ $category->category_id }}" {{ request('category_id') == $category->category_id ? 'selected' : '' }}>
                            {{ $category->category_name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="flex items-end space-x-2">
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded flex-1">
                    📊 Apply Filters
                </button>
                <a href="{{ route('analytics.sync') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    🔄 Reset
                </a>
            </div>
        </form>
        
        <!-- Quick Date Filters -->
        <div class="mt-4 pt-4 border-t border-gray-200">
            <p class="text-sm font-medium text-gray-700 mb-2">Quick Date Ranges:</p>
            <div class="flex flex-wrap gap-2">
                <a href="{{ route('analytics.sync', ['start_date' => now()->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}" 
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">Today</a>
                <a href="{{ route('analytics.sync', ['start_date' => now()->subDays(7)->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}" 
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">Last 7 Days</a>
                <a href="{{ route('analytics.sync', ['start_date' => now()->subDays(30)->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}" 
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">Last 30 Days</a>
                <a href="{{ route('analytics.sync', ['start_date' => now()->startOfMonth()->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}" 
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">This Month</a>
            </div>
        </div>
    </div>
</div>

<!-- Sync Analytics -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">🔄 Sync Analytics</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-blue-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">📊</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Syncable Orders</p>
                        <p class="text-2xl font-bold text-blue-600">{{ number_format($syncData['total_syncable_orders']) }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-green-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">✅</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Synced Orders</p>
                        <p class="text-2xl font-bold text-green-600">{{ number_format($syncData['synced_orders']) }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-yellow-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">⏳</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Pending Sync</p>
                        <p class="text-2xl font-bold text-yellow-600">{{ number_format($syncData['unsynced_orders']) }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-red-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">❌</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Failed Sync</p>
                        <p class="text-2xl font-bold text-red-600">{{ number_format($syncData['failed_sync_orders']) }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sync Success Rate -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">📈 Sync Success Rate</h3>
        <div class="flex items-center justify-center">
            <div class="relative w-32 h-32">
                <svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                    <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                    <path class="text-green-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="{{ $syncData['sync_success_rate'] }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                    <span class="text-2xl font-bold text-green-600">{{ number_format($syncData['sync_success_rate'], 1) }}%</span>
                </div>
            </div>
        </div>
        <div class="mt-4 text-center">
            <p class="text-sm text-gray-600">
                {{ number_format($syncData['synced_orders']) }} out of {{ number_format($syncData['total_syncable_orders']) }} orders successfully synced
            </p>
        </div>
    </div>
</div>

<!-- Sync Status Breakdown -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">📊 Sync Status Breakdown</h3>
        <div class="space-y-4">
            <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                    <span class="font-medium text-green-800">Successfully Synced</span>
                </div>
                <div class="flex items-center">
                    <span class="text-green-600 font-bold mr-2">{{ number_format($syncData['synced_orders']) }}</span>
                    <div class="w-32 bg-green-200 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full" style="width: {{ $syncData['total_syncable_orders'] > 0 ? ($syncData['synced_orders'] / $syncData['total_syncable_orders']) * 100 : 0 }}%"></div>
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-yellow-500 rounded-full mr-3"></div>
                    <span class="font-medium text-yellow-800">Pending Sync</span>
                </div>
                <div class="flex items-center">
                    <span class="text-yellow-600 font-bold mr-2">{{ number_format($syncData['unsynced_orders']) }}</span>
                    <div class="w-32 bg-yellow-200 rounded-full h-2">
                        <div class="bg-yellow-500 h-2 rounded-full" style="width: {{ $syncData['total_syncable_orders'] > 0 ? ($syncData['unsynced_orders'] / $syncData['total_syncable_orders']) * 100 : 0 }}%"></div>
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-red-500 rounded-full mr-3"></div>
                    <span class="font-medium text-red-800">Failed Sync</span>
                </div>
                <div class="flex items-center">
                    <span class="text-red-600 font-bold mr-2">{{ number_format($syncData['failed_sync_orders']) }}</span>
                    <div class="w-32 bg-red-200 rounded-full h-2">
                        <div class="bg-red-500 h-2 rounded-full" style="width: {{ $syncData['total_syncable_orders'] > 0 ? ($syncData['failed_sync_orders'] / $syncData['total_syncable_orders']) * 100 : 0 }}%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@if($syncData['failed_sync_orders'] > 0)
<!-- Failed Sync Orders -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">❌ Failed Sync Orders</h3>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Sync Issues Detected</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <p>{{ number_format($syncData['failed_sync_orders']) }} orders failed to sync. Please check the error logs and retry synchronization.</p>
                    </div>
                </div>
            </div>
        </div>
        <p class="text-sm text-gray-600">Check the ongoing orders page to view specific error messages and retry failed synchronizations.</p>
    </div>
</div>
@endif

@endsection

@section('scripts')
<script>
    // Export functionality
    function toggleExportDropdown() {
        const dropdown = document.getElementById('exportDropdown');
        dropdown.classList.toggle('hidden');
    }

    document.addEventListener('click', function(event) {
        const dropdown = document.getElementById('exportDropdown');
        const button = event.target.closest('button');
        
        if (!button || !button.onclick || button.onclick.toString().indexOf('toggleExportDropdown') === -1) {
            dropdown.classList.add('hidden');
        }
    });

    function exportData(format = 'csv') {
        const button = event.target;
        const originalText = button.innerHTML;
        
        button.disabled = true;
        button.innerHTML = '⏳ Exporting...';
        
        const formData = new FormData();
        formData.append('start_date', '{{ $startDate }}');
        formData.append('end_date', '{{ $endDate }}');
        formData.append('format', format);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        fetch('{{ route("analytics.export") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = `sync_analytics_{{ $startDate }}_to_{{ $endDate }}.${format}`;
                
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        filename = filenameMatch[1];
                    }
                }

                return response.blob().then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    document.getElementById('exportDropdown').classList.add('hidden');
                    alert(`Sync analytics exported successfully as ${format.toUpperCase()}!`);
                });
            } else {
                throw new Error('Export failed');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Export failed. Please try again.');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }
</script>
@endsection
