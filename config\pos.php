<?php

return [
    /*
    |--------------------------------------------------------------------------
    | POS API Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration settings for the external POS API integration.
    | These settings control authentication, endpoints, and caching behavior.
    |
    | The system now supports outlet-specific product filtering. When a user
    | logs in, the outlet ID from the API response is captured and used to
    | filter products by appending ?outlet_id={id} to product API calls.
    |
    */

    'api_base_url' => env('POS_API_BASE_URL', 'http://viera-filament.test/api/pos'),

    'api_email' => env('POS_API_EMAIL'),

    'api_password' => env('POS_API_PASSWORD'),

    'api_username' => env('POS_API_USERNAME'),

    'api_device_name' => env('POS_API_DEVICE_NAME', 'POS System'),

    'default_device_name' => env('POS_DEFAULT_DEVICE_NAME', 'POS System'),
    
    'api_timeout' => env('POS_API_TIMEOUT', 15), // Optimized timeout

    'customer_api_timeout' => env('POS_CUSTOMER_API_TIMEOUT', 15), // Optimized timeout for customer sync

    /*
    |--------------------------------------------------------------------------
    | Network Validation Settings
    |--------------------------------------------------------------------------
    |
    | Control network validation behavior for product creation.
    | In development, network validation is more lenient.
    |
    */

    'disable_network_validation' => env('POS_DISABLE_NETWORK_VALIDATION', false),
    
    /*
    |--------------------------------------------------------------------------
    | Token Caching
    |--------------------------------------------------------------------------
    |
    | Configuration for authentication token caching.
    | Duration is in seconds.
    |
    */
    
    'token_cache_duration' => env('POS_TOKEN_CACHE_DURATION', 3600), // 1 hour
    
    'token_cache_key' => env('POS_TOKEN_CACHE_KEY', 'pos_api_token'),
    
    /*
    |--------------------------------------------------------------------------
    | Product Caching
    |--------------------------------------------------------------------------
    |
    | Configuration for product data caching.
    | Duration is in seconds.
    |
    */
    
    'product_cache_duration' => env('POS_PRODUCT_CACHE_DURATION', 300), // 5 minutes
    
    'product_cache_key' => env('POS_PRODUCT_CACHE_KEY', 'pos_products'),
    
    /*
    |--------------------------------------------------------------------------
    | Fallback Settings
    |--------------------------------------------------------------------------
    |
    | Settings for fallback behavior when API is unavailable.
    |
    */

    'use_local_fallback' => env('POS_USE_LOCAL_FALLBACK', false),
    
    'max_retry_attempts' => env('POS_MAX_RETRY_ATTEMPTS', 3),
    
    'retry_delay' => env('POS_RETRY_DELAY', 1000), // milliseconds
];
