#!/bin/bash

# =============================================================================
# cURL Commands for POS Sync Products API
# Endpoint: https://viera-filament.test/api/pos/sync/products
# Token: 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35
# =============================================================================

echo "=== POS Sync Products API Test ==="
echo ""

# Basic cURL command
echo "1. Basic cURL request:"
echo "curl -X GET \"https://viera-filament.test/api/pos/sync/products\" \\"
echo "  -H \"Authorization: Bearer 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35\" \\"
echo "  -H \"Accept: application/json\" \\"
echo "  -H \"Content-Type: application/json\""
echo ""

# Execute basic request
echo "Executing basic request..."
curl -X GET "https://viera-filament.test/api/pos/sync/products" \
  -H "Authorization: Bearer 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -w "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\nSize: %{size_download} bytes\n" \
  -s
echo ""
echo "----------------------------------------"
echo ""

# cURL with verbose output for debugging
echo "2. cURL with verbose output (for debugging):"
echo "curl -X GET \"https://viera-filament.test/api/pos/sync/products\" \\"
echo "  -H \"Authorization: Bearer 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35\" \\"
echo "  -H \"Accept: application/json\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -v"
echo ""

# cURL with formatted JSON output
echo "3. cURL with formatted JSON output:"
echo "curl -X GET \"https://viera-filament.test/api/pos/sync/products\" \\"
echo "  -H \"Authorization: Bearer 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35\" \\"
echo "  -H \"Accept: application/json\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -s | jq ."
echo ""

# Execute formatted request (if jq is available)
echo "Executing formatted request (requires jq)..."
if command -v jq &> /dev/null; then
    curl -X GET "https://viera-filament.test/api/pos/sync/products" \
      -H "Authorization: Bearer 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35" \
      -H "Accept: application/json" \
      -H "Content-Type: application/json" \
      -s | jq .
else
    echo "jq not found. Install jq for formatted JSON output."
    echo "Raw response:"
    curl -X GET "https://viera-filament.test/api/pos/sync/products" \
      -H "Authorization: Bearer 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35" \
      -H "Accept: application/json" \
      -H "Content-Type: application/json" \
      -s
fi
echo ""
echo "----------------------------------------"
echo ""

# cURL with error handling and output to file
echo "4. cURL with error handling and save to file:"
echo "curl -X GET \"https://viera-filament.test/api/pos/sync/products\" \\"
echo "  -H \"Authorization: Bearer 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35\" \\"
echo "  -H \"Accept: application/json\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -o \"products_response.json\" \\"
echo "  -w \"HTTP Status: %{http_code}\\nTotal Time: %{time_total}s\\n\" \\"
echo "  -f"
echo ""

# Execute with file output
echo "Executing request and saving to products_response.json..."
if curl -X GET "https://viera-filament.test/api/pos/sync/products" \
  -H "Authorization: Bearer 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -o "products_response.json" \
  -w "HTTP Status: %{http_code}\nTotal Time: %{time_total}s\n" \
  -f -s; then
    echo "✅ Success! Response saved to products_response.json"
    echo "File size: $(wc -c < products_response.json) bytes"
    
    # Show first few lines of the response
    echo ""
    echo "Preview of response:"
    head -n 10 products_response.json
    echo "..."
else
    echo "❌ Request failed!"
fi
echo ""
echo "----------------------------------------"
echo ""

# cURL with timeout and retry
echo "5. cURL with timeout and retry options:"
echo "curl -X GET \"https://viera-filament.test/api/pos/sync/products\" \\"
echo "  -H \"Authorization: Bearer 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35\" \\"
echo "  -H \"Accept: application/json\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  --connect-timeout 10 \\"
echo "  --max-time 30 \\"
echo "  --retry 3 \\"
echo "  --retry-delay 2"
echo ""

# PowerShell equivalent (for Windows users)
echo "=== PowerShell Equivalent (Windows) ==="
echo ""
echo "Invoke-RestMethod -Uri \"https://viera-filament.test/api/pos/sync/products\" \\"
echo "  -Method Get \\"
echo "  -Headers @{ \\"
echo "    \"Authorization\" = \"Bearer 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35\" \\"
echo "    \"Accept\" = \"application/json\" \\"
echo "    \"Content-Type\" = \"application/json\" \\"
echo "  } | ConvertTo-Json -Depth 10"
echo ""

# One-liner commands for quick testing
echo "=== Quick One-Liner Commands ==="
echo ""
echo "# Basic request:"
echo "curl -H \"Authorization: Bearer 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35\" -H \"Accept: application/json\" https://viera-filament.test/api/pos/sync/products"
echo ""
echo "# With pretty JSON (requires jq):"
echo "curl -H \"Authorization: Bearer 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35\" -H \"Accept: application/json\" -s https://viera-filament.test/api/pos/sync/products | jq ."
echo ""
echo "# Check only HTTP status:"
echo "curl -H \"Authorization: Bearer 5|DNCm0q0zRfEuJi0cHGN5ENmSFy1pOFPNqU2pXdH46dfe8f35\" -H \"Accept: application/json\" -s -o /dev/null -w \"%{http_code}\" https://viera-filament.test/api/pos/sync/products"
echo ""

echo "=== Test Complete ==="
