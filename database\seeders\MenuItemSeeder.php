<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MenuItem;
use App\Models\MenuCategory;

class MenuItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = MenuCategory::all();

        // Sample menu items for each category
        $menuItems = [
            'Makanan <PERSON>' => [
                ['name' => 'Nasi Goreng Spesial', 'description' => 'Nasi goreng dengan telur, ayam, dan sayuran', 'price' => 25000],
                ['name' => 'Mie Ayam Bakso', 'description' => 'Mie ayam dengan bakso dan pangsit', 'price' => 20000],
                ['name' => 'Ayam Bakar', 'description' => 'Ayam bakar dengan sambal dan lalapan', 'price' => 30000],
                ['name' => 'Gado-Gado', 'description' => 'Sayuran dengan bumbu kacang', 'price' => 18000],
            ],
            'Minuman' => [
                ['name' => 'Es Teh Manis', 'description' => 'Teh manis dingin', 'price' => 5000],
                ['name' => 'Es Jeruk', 'description' => 'Jus jeruk segar', 'price' => 8000],
                ['name' => 'Kopi Hitam', 'description' => 'Kopi hitam panas', 'price' => 6000],
                ['name' => 'Jus Alpukat', 'description' => 'Jus alpukat segar', 'price' => 12000],
            ],
            'Appetizer' => [
                ['name' => 'Kerupuk', 'description' => 'Kerupuk udang crispy', 'price' => 3000],
                ['name' => 'Tahu Goreng', 'description' => 'Tahu goreng dengan sambal kecap', 'price' => 8000],
                ['name' => 'Tempe Mendoan', 'description' => 'Tempe goreng tepung', 'price' => 7000],
            ],
            'Dessert' => [
                ['name' => 'Es Krim Vanilla', 'description' => 'Es krim vanilla dengan topping', 'price' => 10000],
                ['name' => 'Pisang Goreng', 'description' => 'Pisang goreng dengan madu', 'price' => 8000],
                ['name' => 'Puding Coklat', 'description' => 'Puding coklat lembut', 'price' => 7000],
            ],
            'Snack' => [
                ['name' => 'Kentang Goreng', 'description' => 'Kentang goreng crispy', 'price' => 12000],
                ['name' => 'Onion Rings', 'description' => 'Bawang bombay goreng tepung', 'price' => 15000],
                ['name' => 'Chicken Wings', 'description' => 'Sayap ayam goreng pedas', 'price' => 18000],
            ],
        ];

        foreach ($categories as $category) {
            if (isset($menuItems[$category->name])) {
                foreach ($menuItems[$category->name] as $item) {
                    MenuItem::firstOrCreate(
                        [
                            'category_id' => $category->id,
                            'name' => $item['name']
                        ],
                        [
                            'description' => $item['description'],
                            'price' => $item['price'],
                        ]
                    );
                }
            }
        }
    }
}
