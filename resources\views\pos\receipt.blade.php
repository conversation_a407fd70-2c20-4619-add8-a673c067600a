<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt - Order #{{ $order->id }}</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .receipt {
            max-width: 350px;
            margin: 0 auto;
            border: 1px solid #000;
            padding: 20px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .business-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .business-info {
            font-size: 10px;
            color: #666;
        }
        .order-info {
            margin-bottom: 20px;
        }
        .order-info div {
            margin-bottom: 3px;
        }
        .items {
            border-bottom: 1px solid #ccc;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        .item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            align-items: flex-start;
        }
        .item-details {
            flex: 1;
            margin-right: 10px;
        }
        .item-name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        .item-notes {
            font-size: 10px;
            color: #666;
            font-style: italic;
            margin-bottom: 2px;
        }
        .item-price {
            font-weight: bold;
            white-space: nowrap;
        }
        .totals {
            margin-bottom: 20px;
        }
        .total-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .total-line.final {
            border-top: 2px solid #000;
            padding-top: 5px;
            font-weight: bold;
            font-size: 14px;
        }
        .payments {
            border-top: 1px solid #ccc;
            padding-top: 15px;
            margin-bottom: 20px;
        }
        .payment-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        .payment-line.change {
            font-weight: bold;
            color: #2e7d32;
        }
        .footer {
            text-align: center;
            border-top: 2px solid #000;
            padding-top: 15px;
            font-size: 10px;
        }
        .no-print {
            display: block;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="no-print" style="text-align: center; margin-bottom: 20px;">
        <button onclick="window.print()" style="background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
            Print Receipt
        </button>
        <button onclick="window.close()" style="background: #f44336; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; margin-left: 10px;">
            Close
        </button>
        <a href="{{ route('pos.index') }}" style="background: #2196F3; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; margin-left: 10px; text-decoration: none; display: inline-block;">
            Back to POS
        </a>
    </div>

    <div class="receipt">
        <div class="header">
            <div class="business-name">F&B Restaurant</div>
            <div class="business-info">
                123 Main Street<br>
                City, State 12345<br>
                Phone: (*************
            </div>
        </div>

        <div class="order-info">
            <div><strong>Receipt #:</strong> {{ $order->id }}</div>
            <div><strong>Table:</strong> {{ $order->table?->name ?? 'N/A' }}</div>
            <div><strong>Party Size:</strong> {{ $order->party_size ?? 2 }} {{ ($order->party_size ?? 2) == 1 ? 'guest' : 'guests' }}</div>
            <div><strong>Customer:</strong> {{ $order->customer?->name ?? 'Guest' }}</div>
            <div><strong>Server:</strong> {{ $order->cashier_name ?? 'Unknown' }}</div>
            <div><strong>Date:</strong> {{ $order->created_at->setTimezone('Asia/Jakarta')->format('M d, Y H:i') }}</div>
        </div>

        <div class="items">
            @foreach($order->orderItems as $item)
                <div class="item">
                    <div class="item-details">
                        <div class="item-name">{{ $item->menuItem?->name ?? 'Unknown Item' }}</div>
                        @if($item->notes)
                            <div class="item-notes">{{ $item->notes }}</div>
                        @endif
                        <div style="font-size: 10px;">
                            {{ $orderSettings['currency_symbol'] }}{{ number_format($item->price_at_time, 2) }} × {{ $item->quantity }}
                        </div>
                    </div>
                    <div class="item-price">
                        {{ $orderSettings['currency_symbol'] }}{{ number_format($item->price_at_time * $item->quantity, 2) }}
                    </div>
                </div>
            @endforeach
        </div>

        <div class="totals">
            <div class="total-line">
                <span>Subtotal:</span>
                <span>{{ $orderSettings['currency_symbol'] }}{{ number_format($order->subtotal_amount ?? $order->subtotal, 2) }}</span>
            </div>

            @if($order->activeDiscounts && $order->activeDiscounts->count() > 0)
                @foreach($order->activeDiscounts as $discount)
                    <div class="total-line">
                        <span>{{ $discount->formatted_description }}:</span>
                        <span>-{{ $orderSettings['currency_symbol'] }}{{ number_format($discount->amount, 2) }}</span>
                    </div>
                @endforeach
            @endif

            <div class="total-line">
                <span>Tax:</span>
                <span>{{ $orderSettings['currency_symbol'] }}{{ number_format($order->tax_amount, 2) }}</span>
            </div>

            @if($order->service_charge_amount > 0)
                <div class="total-line">
                    <span>Service Charge:</span>
                    <span>{{ $orderSettings['currency_symbol'] }}{{ number_format($order->service_charge_amount, 2) }}</span>
                </div>
            @endif

            <div class="total-line final">
                <span>Total:</span>
                <span>{{ $orderSettings['currency_symbol'] }}{{ number_format($order->total_amount, 2) }}</span>
            </div>
        </div>

        @if($order->payments && $order->payments->count() > 0)
            <div class="payments">
                <div style="font-weight: bold; margin-bottom: 10px;">Payment Details:</div>
                @php
                    $totalPaid = $order->payments->sum('amount_paid');
                    $change = max(0, $totalPaid - $order->total_amount);
                @endphp
                @foreach($order->payments as $payment)
                    <div class="payment-line">
                        <span>{{ $payment->method }}:</span>
                        <span>{{ $orderSettings['currency_symbol'] }}{{ number_format($payment->amount_paid, 2) }}</span>
                    </div>
                @endforeach

                <div class="payment-line" style="border-top: 1px solid #ccc; padding-top: 5px; margin-top: 5px;">
                    <span><strong>Total Paid:</strong></span>
                    <span><strong>{{ $orderSettings['currency_symbol'] }}{{ number_format($totalPaid, 2) }}</strong></span>
                </div>

                @if($change > 0)
                    <div class="payment-line change">
                        <span>Change:</span>
                        <span>{{ $orderSettings['currency_symbol'] }}{{ number_format($change, 2) }}</span>
                    </div>
                @endif
            </div>
        @endif

        <div class="footer">
            <div>Thank you for your visit!</div>
            <div>Please come again</div>
            <div style="margin-top: 10px;">
                Printed: {{ now()->setTimezone('Asia/Jakarta')->format('M d, Y H:i:s') }}
            </div>
        </div>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
